package ports

// ChainConfig defines the configuration for a specific blockchain.
// Moved from internal/logic/task/deposit_check_impl.go to break import cycle.
type ChainConfig struct {
	Enabled       bool                   `json:"enabled"`
	Confirmations uint64                 `json:"confirmations"`
	StartBlock    uint64                 `json:"startBlock"`
	Rpc           string                 `json:"rpc"`                    // RPC URL
	ApiKey        string                 `json:"apiKey"`                 // For TRX TronGrid API Key
	NativeSymbol  string                 `json:"nativeSymbol,omitempty"` // <-- 添加此行
	Tokens        map[string]TokenConfig `json:"tokens"`
}

// TokenConfig defines the configuration for a specific token on a chain.
// Moved from internal/logic/task/deposit_check_impl.go to break import cycle.
type TokenConfig struct {
	Enabled         bool              `json:"enabled"`
	ContractAddress string            `json:"contractAddress"` // Contract address
	Decimals        uint8             `json:"decimals"`
}

// DepositCheckConfig defines the overall configuration for the deposit check task.
// This remains in the task package as it's the top-level config structure for the task itself.
// We might need a way to pass relevant parts (like Network) down without causing cycles,
// or redefine a simpler config structure within ports if absolutely necessary,
// but let's try keeping it in task first.
// If DepositCheckConfig is needed by scanners, it might need refactoring.
// For now, only ChainConfig and TokenConfig are moved.
