package task_registry

import (
	"context"
	"fmt"
	"strings" // Import strings package

	"github.com/gogf/gf/v2/container/gvar"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcron"
	"github.com/gogf/gf/v2/os/glog"
)

// TaskFunc defines the signature for a task execution function.
type TaskFunc func(ctx context.Context)

// CronSpecFunc defines the signature for a function that returns the cron spec and enabled status.
type CronSpecFunc func(ctx context.Context) (spec string, enabled bool, err error)

// TaskInfo holds information about a registered task.
type TaskInfo struct {
	Name        string       // Unique name for the task
	SpecFunc    CronSpecFunc // Function to get cron spec and enabled status
	Func        TaskFunc     // The actual task function to execute
	IsSingleton bool         // Whether the task should run as a singleton
}

var (
	// registeredTasks holds all tasks registered via init functions.
	registeredTasks []TaskInfo
	// defaultLogger is the default logger for the registry.
	defaultLogger = g.Log("task_registry")
)

// Register adds a task to the registry.
// This is typically called from the init() function of a task logic file.
func Register(info TaskInfo) {
	// Basic validation
	if info.Name == "" || info.SpecFunc == nil || info.Func == nil {
		defaultLogger.Warningf(context.Background(), "Attempted to register task with missing information: %+v", info)
		return
	}
	registeredTasks = append(registeredTasks, info)
	defaultLogger.Debugf(context.Background(), "Task '%s' registered for potential scheduling", info.Name)
}

// ApplyRegistrations iterates through registered tasks and adds them to the provided cron scheduler.
func ApplyRegistrations(ctx context.Context, cron *gcron.Cron) error {
	logger := glog.New() // Or use a specific logger instance if needed
	var registrationErrors []error

	logger.Infof(ctx, "Applying %d registered tasks to the scheduler...", len(registeredTasks))

	for _, task := range registeredTasks {
		spec, enabled, err := task.SpecFunc(ctx)
		if err != nil {
			errMsg := fmt.Sprintf("Error getting spec for task '%s': %v. Task will not be scheduled.", task.Name, err)
			logger.Error(ctx, errMsg)
			registrationErrors = append(registrationErrors, fmt.Errorf(errMsg)) // Collect error
			continue                                                            // Skip this task
		}

		if !enabled {
			logger.Infof(ctx, "Task '%s' is disabled via configuration. Skipping scheduling.", task.Name)
			continue
		}

		if spec == "" {
			logger.Warningf(ctx, "Task '%s' is enabled but has an empty cron spec. Skipping scheduling.", task.Name)
			continue
		}

		// Use a closure to capture the task details for the cron job function
		taskFunc := task.Func
		taskName := task.Name

		// Trim quotes from the spec before adding to cron
		cleanSpec := strings.Trim(spec, "\"")
		if cleanSpec != spec {
			logger.Debugf(ctx, "Trimmed quotes from spec for task '%s'. Original: '%s', Cleaned: '%s'", taskName, spec, cleanSpec)
		}

		var addErr error
		if task.IsSingleton {
			_, addErr = cron.AddSingleton(ctx, cleanSpec, func(ctx context.Context) { // Use cleanSpec
				logger.Debugf(ctx, "Executing singleton task: %s", taskName)
				taskFunc(ctx)
				logger.Debugf(ctx, "Finished singleton task: %s", taskName)
			}, taskName) // Use task name as singleton lock name
		} else {
			_, addErr = cron.Add(ctx, cleanSpec, func(ctx context.Context) { // Use cleanSpec
				logger.Debugf(ctx, "Executing task: %s", taskName)
				taskFunc(ctx)
				logger.Debugf(ctx, "Finished task: %s", taskName)
			}, taskName) // Use task name for logging/identification
		}

		if addErr != nil {
			// Use cleanSpec in the error message as that's what was attempted
			errMsg := fmt.Sprintf("Error adding task '%s' with spec '%s' (original: '%s') to cron scheduler: %v", task.Name, cleanSpec, spec, addErr)
			logger.Error(ctx, errMsg)
			registrationErrors = append(registrationErrors, fmt.Errorf(errMsg)) // Collect error
		} else {
			// Log the cleaned spec that was successfully scheduled
			logger.Infof(ctx, "Successfully scheduled task '%s' with spec '%s' (Singleton: %t)", task.Name, cleanSpec, task.IsSingleton)
		}
	}

	// Decide how to handle collected errors. Here we just log a summary.
	// Depending on requirements, you might return a composite error.
	if len(registrationErrors) > 0 {
		logger.Errorf(ctx, "%d errors occurred during task registration.", len(registrationErrors))
		// Optionally return a combined error:
		// return errors.Join(registrationErrors...)
		return fmt.Errorf("encountered %d errors during task registration", len(registrationErrors)) // Return a generic error indicating issues
	}

	logger.Infof(ctx, "Finished applying task registrations. %d tasks scheduled.", cron.Size())
	return nil
}

// GetRegisteredTasks returns a copy of the registered tasks slice.
// This allows other parts of the application to inspect the tasks
// without modifying the original registry.
func GetRegisteredTasks() []TaskInfo {
	// Return a copy to prevent external modification of the original slice
	tasksCopy := make([]TaskInfo, len(registeredTasks))
	copy(tasksCopy, registeredTasks)
	return tasksCopy
}

// Helper function to get config value, similar to what might be used in SpecFunc
func getConfigValue(ctx context.Context, key string) (*gvar.Var, error) {
	v, err := g.Cfg().Get(ctx, key)
	if err != nil {
		return nil, fmt.Errorf("failed to get config for key '%s': %w", key, err)
	}
	return v, nil
}
