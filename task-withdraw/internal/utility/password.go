package utility

import (
	"crypto/sha256"
	"encoding/hex"
	"strings"
)

// HashPassword creates a SHA-256 hash of the provided password
func HashPassword(password string) string {
	// Create a new SHA-256 hash
	hasher := sha256.New()

	// Write the password to the hasher
	hasher.Write([]byte(password))

	// Get the hash and convert to hex string
	return hex.EncodeToString(hasher.Sum(nil))
}

// VerifyPassword checks if the provided password matches the stored hash
func VerifyPassword(password, storedHash string) bool {
	// Hash the provided password
	hashedPassword := HashPassword(password)

	// Compare the hashes (case-insensitive)
	return strings.EqualFold(hashedPassword, storedHash)
}
