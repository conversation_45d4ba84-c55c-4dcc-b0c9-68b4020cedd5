package utils

import (
	"context"
	"crypto/rand" // Added for GenerateSalt
	"crypto/sha256"
	"encoding/hex"
	"errors"
	"fmt"
	"net/url"
	"strings"

	// "task-withdraw/internal/codes" // Import codes package

	"github.com/gogf/gf/v2/crypto/gaes"
	"github.com/gogf/gf/v2/errors/gerror" // Corrected path
	"github.com/gogf/gf/v2/frame/g"
	"github.com/shopspring/decimal"
	"golang.org/x/crypto/pbkdf2" // Added PBKDF2
)

const DefaultSaltLength = 16 // Or 32, as per plan

// GenerateSalt generates a random salt of the specified length.
// If length is non-positive, DefaultSaltLength is used.
func GenerateSalt(length int) ([]byte, error) {
	if length <= 0 {
		length = DefaultSaltLength
	}
	salt := make([]byte, length)
	if _, err := rand.Read(salt); err != nil {
		return nil, err
	}
	return salt, nil
}

// ConcatPasswordAndToken was removed as the old password migration logic is no longer used.

// DEPRECATED: 旧的加密字符串方法，使用全局 app.token 作为密钥来源。
// 请使用 EncryptStringWithPBKDF2 替代。
// func EncryptString(ctx context.Context, plaintext string) (string, error) {
// 	// 从配置获取密钥
// 	key := g.Cfg().MustGet(ctx, "app.token").String()
// 	// 确保密钥长度是16、24或32字节（AES-128、AES-192或AES-256）
// 	// 这里使用SHA256哈希来生成一个32字节的密钥
// 	hashedKey := sha256.Sum256([]byte(key))

// 	// 使用gaes包加密数据
// 	encrypted, err := gaes.Encrypt([]byte(plaintext), hashedKey[:])
// 	if err != nil {
// 		g.Log().Errorf(ctx, "%s: %v", codes.CodeUtilityEncryptFailed.Message(), err)
// 		return "", codes.WrapError(err, codes.CodeUtilityEncryptFailed)
// 	}

// 	// 返回十六进制编码的加密结果
// 	return hex.EncodeToString(encrypted), nil
// }

// DEPRECATED: 旧的解密字符串方法，使用全局 app.token 作为密钥来源。
// 请使用 DecryptStringWithPBKDF2 替代。
// func DecryptString(ctx context.Context, encryptedHex string) (string, error) {
// 	// 从配置获取密钥
// 	key := g.Cfg().MustGet(ctx, "app.token").String()
// 	// 确保密钥长度适合AES加密
// 	hashedKey := sha256.Sum256([]byte(key))

// 	// 将十六进制字符串解码为字节
// 	encryptedBytes, err := hex.DecodeString(encryptedHex)
// 	if err != nil {
// 		g.Log().Errorf(ctx, "%s: %v", codes.CodeUtilityDecryptFormatError.Message(), err)
// 		return "", codes.WrapError(err, codes.CodeUtilityDecryptFormatError)
// 	}

// 	// 使用gaes包解密数据
// 	decrypted, err := gaes.Decrypt(encryptedBytes, hashedKey[:])
// 	if err != nil {
// 		g.Log().Errorf(ctx, "%s: %v", codes.CodeUtilityDecryptFailed.Message(), err)
// 		return "", codes.WrapError(err, codes.CodeUtilityDecryptFailed)
// 	}

// 	return string(decrypted), nil
// }

// --- PBKDF2 and AES based encryption/decryption ---
// Moved imports to the top of the file or within the existing import block.
// For this diff, we'll assume they are correctly placed at the top.

const (
	PBKDF2KeyLength = 32 // For AES-256. AES-128 (16), AES-192 (24), AES-256 (32)
)

// EncryptStringWithPBKDF2 encrypts plaintext using a key derived from password, salt, and iterations via PBKDF2, then AES.
func EncryptStringWithPBKDF2(ctx context.Context, plaintext string, password string, salt []byte, iterations int) (string, error) {
	if password == "" {
		return "", errors.New("password cannot be empty for PBKDF2")
	}
	if len(salt) == 0 {
		return "", errors.New("salt cannot be empty for PBKDF2")
	}
	if iterations <= 0 {
		cfgIterations, errCfg := g.Cfg().Get(ctx, "security.pbkdf2Iterations")
		if errCfg != nil || cfgIterations.IsNil() {
			g.Log().Errorf(ctx, "PBKDF2 iterations not configured and not provided or invalid (<=0). Error: %v", errCfg)
			return "", errors.New("PBKDF2 iterations not configured or invalid")
		}
		iterations = cfgIterations.Int()
		if iterations <= 0 { // Double check after config
			g.Log().Errorf(ctx, "Invalid PBKDF2 iterations from config: %d", iterations)
			return "", errors.New("invalid PBKDF2 iterations from config")
		}
	}

	aesKey := pbkdf2.Key([]byte(password), salt, iterations, PBKDF2KeyLength, sha256.New)

	encrypted, err := gaes.Encrypt([]byte(plaintext), aesKey)
	if err != nil {
		g.Log().Errorf(ctx, "AES encryption failed after PBKDF2 key derivation: %v", err)
		return "", gerror.Wrap(err, "AES encryption failed after PBKDF2 key derivation")
	}
	return hex.EncodeToString(encrypted), nil
}

// DecryptStringWithPBKDF2 decrypts hex-encoded ciphertext using a key derived from password, salt, and iterations via PBKDF2, then AES.
func DecryptStringWithPBKDF2(ctx context.Context, encryptedHex string, password string, salt []byte, iterations int) (string, error) {
	if password == "" {
		return "", errors.New("password cannot be empty for PBKDF2 decryption")
	}
	if len(salt) == 0 {
		return "", errors.New("salt cannot be empty for PBKDF2 decryption")
	}
	if iterations <= 0 {
		cfgIterations, errCfg := g.Cfg().Get(ctx, "security.pbkdf2Iterations")
		if errCfg != nil || cfgIterations.IsNil() {
			g.Log().Errorf(ctx, "PBKDF2 iterations not configured for decryption and not provided or invalid (<=0). Error: %v", errCfg)
			return "", errors.New("PBKDF2 iterations not configured or invalid for decryption")
		}
		iterations = cfgIterations.Int()
		if iterations <= 0 { // Double check after config
			g.Log().Errorf(ctx, "Invalid PBKDF2 iterations from config for decryption: %d", iterations)
			return "", errors.New("invalid PBKDF2 iterations from config for decryption")
		}
	}

	encryptedBytes, err := hex.DecodeString(encryptedHex)
	if err != nil {
		g.Log().Errorf(ctx, "Hex decoding failed for PBKDF2 decryption: %v", err)
		return "", gerror.Wrap(err, "Hex decoding failed for PBKDF2 decryption")
	}

	aesKey := pbkdf2.Key([]byte(password), salt, iterations, PBKDF2KeyLength, sha256.New)

	decrypted, err := gaes.Decrypt(encryptedBytes, aesKey)
	if err != nil {
		g.Log().Errorf(ctx, "AES decryption failed after PBKDF2 key derivation: %v", err)
		return "", gerror.Wrap(err, "AES decryption failed after PBKDF2 key derivation")
	}
	return string(decrypted), nil
}

// AESEncrypt directly encrypts plaintext using a provided key.
func AESEncrypt(plaintext []byte, key []byte) ([]byte, error) {
	if len(key) != PBKDF2KeyLength { // Or other standard AES key lengths like 16, 24
		return nil, errors.New("invalid AES key length for encryption")
	}
	encrypted, err := gaes.Encrypt(plaintext, key)
	if err != nil {
		return nil, gerror.Wrap(err, "AESEncrypt failed")
	}
	return encrypted, nil
}

// AESDecrypt directly decrypts ciphertext using a provided key.
func AESDecrypt(ciphertext []byte, key []byte) ([]byte, error) {
	if len(key) != PBKDF2KeyLength {
		return nil, errors.New("invalid AES key length for decryption")
	}
	decrypted, err := gaes.Decrypt(ciphertext, key)
	if err != nil {
		// This error during direct AES decryption (assuming key is correct)
		// might indicate corrupted data or a wrong key if not from PBKDF2.
		return nil, gerror.Wrap(err, "AESDecrypt failed")
	}
	return decrypted, nil
}

// 校验url 是否合法
func IsUrl(urlStr string) bool {
	parsedUrl, err := url.ParseRequestURI(urlStr)
	if err != nil {
		return false
	}
	return parsedUrl != nil
}

// 传入的数字 / 10^decimals
func FormatTokenValueToDecimal(valueStr decimal.Decimal, decimals int) (decimal.Decimal, error) {
	divisor := decimal.NewFromInt(int64(10)).Pow(decimal.NewFromInt(int64(decimals)))
	return valueStr.Div(divisor), nil
}

// 传入的数字 * 10^decimals
func FormatTokenValueToSmallestUnit(valueStr decimal.Decimal, decimals int) (decimal.Decimal, error) {
	multiplier := decimal.NewFromInt(int64(10)).Pow(decimal.NewFromInt(int64(decimals)))
	return valueStr.Mul(multiplier), nil
}

// 获取代币精度
func GetTokenDecimals(ctx context.Context, token string) (int, error) {
	// Normalize token names to uppercase for consistency
	tokenUpper := strings.ToUpper(token)

	configPath := fmt.Sprintf("blockchain.Decimals.%s", tokenUpper)
	decimals, err := g.Cfg().Get(ctx, configPath)
	if err != nil {
		return 0, fmt.Errorf("failed to get %s decimals from config: %w", tokenUpper, err)
	}

	if decimals.IsEmpty() {
		return 0, fmt.Errorf("%s decimals is empty in config", tokenUpper)
	}

	return decimals.Int(), nil
}
