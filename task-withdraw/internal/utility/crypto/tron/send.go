package tron

import (
	"context"
	"fmt"
	
	"github.com/shopspring/decimal"
)

// SendTrxTransaction sends a TRX transaction
// TODO: This function is temporarily disabled to resolve circular import issues.
// The functionality will be reimplemented in the unified crypto interface.
func SendTrxTransaction(ctx context.Context, privateKey string, fromAddress string, toAddress string, amount decimal.Decimal, logPrefix string) (string, error) {
	return "", fmt.Errorf("TRX transaction sending temporarily disabled due to circular import resolution - use the new unified crypto interface instead")
}

// SendTrc20Transaction sends a TRC20 transaction  
// TODO: This function is temporarily disabled to resolve circular import issues.
// The functionality will be reimplemented in the unified crypto interface.
func SendTrc20Transaction(ctx context.Context, privateKey string, fromAddress string, toAddress string, amount decimal.Decimal, feeLimit int64, logPrefix string) (string, error) {
	return "", fmt.Errorf("TRC20 transaction sending temporarily disabled due to circular import resolution - use the new unified crypto interface instead")
}

// EstimateTrxFee estimates the fee for a TRX transfer
// TODO: This function is temporarily disabled to resolve circular import issues.
// The functionality will be reimplemented in the unified crypto interface.
func EstimateTrxFee(ctx context.Context, fromAddress string, toAddress string, amount decimal.Decimal, logPrefix string) (int64, error) {
	return 1000, nil // Return a default 1000 SUN (0.001 TRX) for now
}