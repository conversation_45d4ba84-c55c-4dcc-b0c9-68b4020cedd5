package eth

import (
	"context"
	"fmt"
	"io"
	"math/big"
	"net/http"
	"strings"
	"task-withdraw/internal/utility/crypto" // Added import for crypto package
	"time"

	"github.com/ethereum/go-ethereum/common"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/shopspring/decimal"
)

const (
	txHttpTimeout = 10 * time.Second
	txMaxRetries  = 3
	txRetryDelay  = 1 * time.Second
	txPageSize    = 20 // Default page size for transaction queries
)

// EthTransaction represents a transaction from the Ethereum network
type EthTransaction struct {
	Hash             string `json:"hash"`
	BlockNumber      string `json:"blockNumber"`
	BlockHash        string `json:"blockHash"`
	From             string `json:"from"`
	To               string `json:"to"`
	Value            string `json:"value"`
	Gas              string `json:"gas"`
	GasPrice         string `json:"gasPrice"`
	Input            string `json:"input"`
	Nonce            string `json:"nonce"`
	TransactionIndex string `json:"transactionIndex"`
	Timestamp        int64  `json:"timestamp,omitempty"` // Added from block info
}

// EthTransactionReceipt represents a transaction receipt from the Ethereum network
type EthTransactionReceipt struct {
	TransactionHash   string `json:"transactionHash"`
	BlockNumber       string `json:"blockNumber"`
	BlockHash         string `json:"blockHash"`
	From              string `json:"from,omitempty"` // Added for convenience
	To                string `json:"to,omitempty"`   // Added for convenience
	CumulativeGasUsed string `json:"cumulativeGasUsed"`
	GasUsed           string `json:"gasUsed"`
	ContractAddress   string `json:"contractAddress"`
	Logs              []any  `json:"logs"`
	Status            string `json:"status"`
}

// EthBlock represents a block from the Ethereum network
type EthBlock struct {
	Number           string           `json:"number"`
	Hash             string           `json:"hash"`
	ParentHash       string           `json:"parentHash"`
	Timestamp        string           `json:"timestamp"`
	Transactions     []EthTransaction `json:"transactions"`
	TransactionCount int              `json:"transactionCount"`
}

// GetETHTransactions retrieves ETH transactions for a given address
func GetETHTransactions(address string, limit int, startBlock int64) ([]EthTransaction, error) {
	ctx := context.Background()

	// Get ETH RPC URL using the unified getter
	rpcUrl, err := crypto.GetEthRpcUrl(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get ETH RPC URL: %w", err)
	}

	// Get API key from config (if any)
	apiKey, err := GetEthApiKey(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get ETH API key: %w", err)
	}

	// Create HTTP client
	client := &http.Client{Timeout: txHttpTimeout}

	// Set default limit if not provided
	if limit <= 0 {
		limit = txPageSize
	}

	// Get latest block number if startBlock is not provided
	if startBlock <= 0 {
		ethClient, err := crypto.GetInstance().GetDefaultEthClient(ctx)
		if err != nil {
			// Consider how to handle this error: return, or log and proceed with a default startBlock if possible
			return nil, fmt.Errorf("failed to get Ethereum client for block number lookup: %w", err)
		}
		latestBlock, err := ethClient.BlockNumber(ctx)
		if err != nil {
			return nil, fmt.Errorf("failed to get latest block number: %w", err)
		}
		startBlock = int64(latestBlock)
	}

	// Normalize address
	normalizedAddr := common.HexToAddress(address).Hex()

	// Fetch transactions
	var transactions []EthTransaction
	currentBlock := startBlock

	// We'll fetch blocks in reverse order (newest to oldest)
	for len(transactions) < limit && currentBlock > 0 {
		// Get block by number
		blockTxs, err := getBlockTransactions(ctx, client, rpcUrl, apiKey, currentBlock, normalizedAddr)
		if err != nil {
			g.Log().Warningf(ctx, "Failed to get transactions for block %d: %v", currentBlock, err)
			currentBlock--
			continue
		}

		// Add transactions to result
		transactions = append(transactions, blockTxs...)

		// Move to previous block
		currentBlock--

		// Break if we have enough transactions
		if len(transactions) >= limit {
			break
		}
	}

	// Trim to requested limit if needed
	if len(transactions) > limit {
		transactions = transactions[:limit]
	}

	return transactions, nil
}

// GetERC20Transactions retrieves ERC20 token transactions for a given address
func GetERC20Transactions(address string, contractAddress string, limit int, startBlock int64) ([]EthTransaction, error) {
	ctx := context.Background()

	// Get ETH RPC URL using the unified getter
	rpcUrl, err := crypto.GetEthRpcUrl(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get ETH RPC URL: %w", err)
	}

	// Get API key from config (if any)
	apiKey, err := GetEthApiKey(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get ETH API key: %w", err)
	}

	// Create HTTP client
	client := &http.Client{Timeout: txHttpTimeout}

	// Set default limit if not provided
	if limit <= 0 {
		limit = txPageSize
	}

	// Get latest block number if startBlock is not provided
	if startBlock <= 0 {
		ethClient, err := crypto.GetInstance().GetDefaultEthClient(ctx)
		if err != nil {
			// Consider how to handle this error: return, or log and proceed with a default startBlock if possible
			return nil, fmt.Errorf("failed to get Ethereum client for block number lookup: %w", err)
		}
		latestBlock, err := ethClient.BlockNumber(ctx)
		if err != nil {
			return nil, fmt.Errorf("failed to get latest block number: %w", err)
		}
		startBlock = int64(latestBlock)
	}

	// Normalize addresses
	normalizedAddr := common.HexToAddress(address).Hex()
	normalizedContract := common.HexToAddress(contractAddress).Hex()

	// Fetch transactions
	var transactions []EthTransaction
	currentBlock := startBlock

	// We'll fetch blocks in reverse order (newest to oldest)
	for len(transactions) < limit && currentBlock > 0 {
		// Get block by number
		blockTxs, err := getBlockTransactions(ctx, client, rpcUrl, apiKey, currentBlock, "")
		if err != nil {
			g.Log().Warningf(ctx, "Failed to get transactions for block %d: %v", currentBlock, err)
			currentBlock--
			continue
		}

		// Filter ERC20 transactions
		for _, tx := range blockTxs {
			// Check if transaction is to the contract address
			if strings.EqualFold(tx.To, normalizedContract) {
				// Check if transaction input data is for a transfer method (0xa9059cbb)
				if len(tx.Input) >= 10 && strings.HasPrefix(tx.Input, "0xa9059cbb") {
					// Extract recipient address from input data
					if len(tx.Input) >= 74 {
						recipientHex := "0x" + tx.Input[34:74]
						recipient := common.HexToAddress(recipientHex).Hex()

						// Check if transaction is to or from the specified address
						if strings.EqualFold(recipient, normalizedAddr) || strings.EqualFold(tx.From, normalizedAddr) {
							transactions = append(transactions, tx)
						}
					}
				}
			}
		}

		// Move to previous block
		currentBlock--

		// Break if we have enough transactions
		if len(transactions) >= limit {
			break
		}
	}

	// Trim to requested limit if needed
	if len(transactions) > limit {
		transactions = transactions[:limit]
	}

	return transactions, nil
}

// GetEthTransactionsByHash retrieves transaction details by hash
func GetEthTransactionsByHash(hash string) (string, error) {
	ctx := context.Background()

	// Get HTTP RPC URL from config
	rpcUrl, err := crypto.GetEthRpcUrl(ctx) // Use unified RPC URL getter
	if err != nil {
		return "0", fmt.Errorf("failed to get ETH RPC URL: %w", err)
	}

	// Get API key from config (if any)
	apiKey, err := GetEthApiKey(ctx)
	if err != nil {
		return "0", fmt.Errorf("failed to get ETH API key: %w", err)
	}

	// Create HTTP client
	client := &http.Client{Timeout: txHttpTimeout}

	// Get transaction receipt
	receipt, err := GetTransactionReceipt(ctx, client, rpcUrl, apiKey, hash)
	if err != nil {
		return "0", fmt.Errorf("failed to get transaction receipt: %w", err)
	}

	// Calculate gas cost
	gasUsed, ok := new(big.Int).SetString(strings.TrimPrefix(receipt.GasUsed, "0x"), 16)
	if !ok {
		return "0", fmt.Errorf("invalid gas used value: %s", receipt.GasUsed)
	}

	// Get transaction to get gas price
	tx, err := GetTransaction(ctx, client, rpcUrl, apiKey, hash)
	if err != nil {
		return "0", fmt.Errorf("failed to get transaction: %w", err)
	}

	gasPrice, ok := new(big.Int).SetString(strings.TrimPrefix(tx.GasPrice, "0x"), 16)
	if !ok {
		return "0", fmt.Errorf("invalid gas price value: %s", tx.GasPrice)
	}

	// Calculate total fee (gasUsed * gasPrice)
	totalFee := new(big.Int).Mul(gasUsed, gasPrice)

	// Convert totalFee from *big.Int to decimal.Decimal (assumed to be in Wei)
	// Similar to the change in balance.go, WeiToETH is expected to handle
	// ETH's decimals internally (e.g., 18).
	// The 'decimals' variable from GetTokenDecimals is therefore not used here.
	weiAmountDecimal := decimal.NewFromBigInt(totalFee, 0)

	// Call WeiToETH. Based on compiler errors, it expects one decimal.Decimal argument (Wei)
	// and returns a decimal.Decimal (ETH) and an error.
	feeEthDecimal, err := WeiToETH(weiAmountDecimal)
	if err != nil {
		return "0", fmt.Errorf("failed to convert fee to ETH: %w", err)
	}

	// The function GetEthTransactionsByHash returns a string, so convert the decimal.Decimal to string.
	return feeEthDecimal.String(), nil
}

// getBlockTransactions retrieves transactions for a specific block
func getBlockTransactions(ctx context.Context, client *http.Client, rpcUrl, apiKey string, blockNumber int64, filterAddress string) ([]EthTransaction, error) {
	// Convert block number to hex
	blockNumberHex := "0x" + fmt.Sprintf("%x", blockNumber)

	// Prepare JSON-RPC request
	requestBody := fmt.Sprintf(`{
		"jsonrpc": "2.0",
		"method": "eth_getBlockByNumber",
		"params": ["%s", true],
		"id": 1
	}`, blockNumberHex)

	// Create request
	req, err := http.NewRequestWithContext(ctx, "POST", rpcUrl, strings.NewReader(requestBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	if apiKey != "" {
		req.Header.Set("Authorization", "Bearer "+apiKey)
	}
	req.Header.Set("Accept", "application/json")
	req.Header.Set("Content-Type", "application/json")

	// Send request with retries
	var respData map[string]any
	var lastErr error

	for attempt := 0; attempt < txMaxRetries; attempt++ {
		resp, err := client.Do(req)
		if err != nil {
			lastErr = fmt.Errorf("attempt %d: failed to execute request: %w", attempt+1, err)
			g.Log().Warningf(ctx, "%s. Retrying...", lastErr)
			time.Sleep(txRetryDelay)
			continue
		}

		bodyBytes, readErr := io.ReadAll(resp.Body)
		resp.Body.Close()
		if readErr != nil {
			lastErr = fmt.Errorf("attempt %d: failed to read response body: %w", attempt+1, readErr)
			g.Log().Warningf(ctx, "%s. Retrying...", lastErr)
			time.Sleep(txRetryDelay)
			continue
		}

		if resp.StatusCode != http.StatusOK {
			lastErr = fmt.Errorf("attempt %d: API returned non-OK status: %d. Body: %s", attempt+1, resp.StatusCode, string(bodyBytes))
			g.Log().Warningf(ctx, "%s. Retrying...", lastErr)
			time.Sleep(txRetryDelay)
			continue
		}

		// Parse response
		if err := gjson.Unmarshal(bodyBytes, &respData); err != nil {
			lastErr = fmt.Errorf("attempt %d: failed to decode response: %w. Body: %s", attempt+1, err, string(bodyBytes))
			g.Log().Warningf(ctx, "%s. Retrying...", lastErr)
			time.Sleep(txRetryDelay)
			continue
		}

		// Check for JSON-RPC error
		if errObj, ok := respData["error"]; ok {
			errMap, ok := errObj.(map[string]any)
			if ok {
				errMsg := fmt.Sprintf("JSON-RPC error: code=%v, message=%v", errMap["code"], errMap["message"])
				lastErr = fmt.Errorf("attempt %d: %s", attempt+1, errMsg)
				g.Log().Warningf(ctx, "%s. Retrying...", lastErr)
				time.Sleep(txRetryDelay)
				continue
			}
		}

		// Extract result from response
		result, ok := respData["result"].(map[string]any)
		if !ok {
			lastErr = fmt.Errorf("attempt %d: invalid response format, missing or invalid 'result'", attempt+1)
			g.Log().Warningf(ctx, "%s. Retrying...", lastErr)
			time.Sleep(txRetryDelay)
			continue
		}

		// Extract timestamp
		timestampHex, _ := result["timestamp"].(string)
		var timestamp int64
		if timestampHex != "" {
			timestampBig, _ := new(big.Int).SetString(strings.TrimPrefix(timestampHex, "0x"), 16)
			if timestampBig != nil {
				timestamp = timestampBig.Int64()
			}
		}

		// Extract transactions
		txsAny, ok := result["transactions"].([]any)
		if !ok {
			lastErr = fmt.Errorf("attempt %d: invalid response format, missing or invalid 'transactions'", attempt+1)
			g.Log().Warningf(ctx, "%s. Retrying...", lastErr)
			time.Sleep(txRetryDelay)
			continue
		}

		// Convert transactions to EthTransaction objects
		var transactions []EthTransaction
		for _, txAny := range txsAny {
			txMap, ok := txAny.(map[string]any)
			if !ok {
				continue
			}

			tx := EthTransaction{
				Hash:             getString(txMap, "hash"),
				BlockNumber:      getString(txMap, "blockNumber"),
				BlockHash:        getString(txMap, "blockHash"),
				From:             getString(txMap, "from"),
				To:               getString(txMap, "to"),
				Value:            getString(txMap, "value"),
				Gas:              getString(txMap, "gas"),
				GasPrice:         getString(txMap, "gasPrice"),
				Input:            getString(txMap, "input"),
				Nonce:            getString(txMap, "nonce"),
				TransactionIndex: getString(txMap, "transactionIndex"),
				Timestamp:        timestamp,
			}

			// Filter by address if provided
			if filterAddress != "" {
				if strings.EqualFold(tx.From, filterAddress) || strings.EqualFold(tx.To, filterAddress) {
					transactions = append(transactions, tx)
				}
			} else {
				transactions = append(transactions, tx)
			}
		}

		return transactions, nil // Success
	}

	g.Log().Errorf(ctx, "Failed to get block transactions after %d retries: %v", txMaxRetries, lastErr)
	return nil, lastErr
}

// GetTransaction retrieves a transaction by hash
func GetTransaction(ctx context.Context, client *http.Client, rpcUrl, apiKey, hash string) (*EthTransaction, error) {
	// Prepare JSON-RPC request
	requestBody := fmt.Sprintf(`{
		"jsonrpc": "2.0",
		"method": "eth_GetTransactionByHash",
		"params": ["%s"],
		"id": 1
	}`, hash)

	// Create request
	req, err := http.NewRequestWithContext(ctx, "POST", rpcUrl, strings.NewReader(requestBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	if apiKey != "" {
		req.Header.Set("Authorization", "Bearer "+apiKey)
	}
	req.Header.Set("Accept", "application/json")
	req.Header.Set("Content-Type", "application/json")

	// Send request with retries
	var respData map[string]any
	var lastErr error

	for attempt := 0; attempt < txMaxRetries; attempt++ {
		resp, err := client.Do(req)
		if err != nil {
			lastErr = fmt.Errorf("attempt %d: failed to execute request: %w", attempt+1, err)
			g.Log().Warningf(ctx, "%s. Retrying...", lastErr)
			time.Sleep(txRetryDelay)
			continue
		}

		bodyBytes, readErr := io.ReadAll(resp.Body)
		resp.Body.Close()
		if readErr != nil {
			lastErr = fmt.Errorf("attempt %d: failed to read response body: %w", attempt+1, readErr)
			g.Log().Warningf(ctx, "%s. Retrying...", lastErr)
			time.Sleep(txRetryDelay)
			continue
		}

		if resp.StatusCode != http.StatusOK {
			lastErr = fmt.Errorf("attempt %d: API returned non-OK status: %d. Body: %s", attempt+1, resp.StatusCode, string(bodyBytes))
			g.Log().Warningf(ctx, "%s. Retrying...", lastErr)
			time.Sleep(txRetryDelay)
			continue
		}

		// Parse response
		if err := gjson.Unmarshal(bodyBytes, &respData); err != nil {
			lastErr = fmt.Errorf("attempt %d: failed to decode response: %w. Body: %s", attempt+1, err, string(bodyBytes))
			g.Log().Warningf(ctx, "%s. Retrying...", lastErr)
			time.Sleep(txRetryDelay)
			continue
		}

		// Check for JSON-RPC error
		if errObj, ok := respData["error"]; ok {
			errMap, ok := errObj.(map[string]any)
			if ok {
				errMsg := fmt.Sprintf("JSON-RPC error: code=%v, message=%v", errMap["code"], errMap["message"])
				lastErr = fmt.Errorf("attempt %d: %s", attempt+1, errMsg)
				g.Log().Warningf(ctx, "%s. Retrying...", lastErr)
				time.Sleep(txRetryDelay)
				continue
			}
		}

		// Extract result from response
		result, ok := respData["result"].(map[string]any)
		if !ok {
			lastErr = fmt.Errorf("attempt %d: invalid response format, missing or invalid 'result'", attempt+1)
			g.Log().Warningf(ctx, "%s. Retrying...", lastErr)
			time.Sleep(txRetryDelay)
			continue
		}

		// Convert to EthTransaction
		tx := &EthTransaction{
			Hash:             getString(result, "hash"),
			BlockNumber:      getString(result, "blockNumber"),
			BlockHash:        getString(result, "blockHash"),
			From:             getString(result, "from"),
			To:               getString(result, "to"),
			Value:            getString(result, "value"),
			Gas:              getString(result, "gas"),
			GasPrice:         getString(result, "gasPrice"),
			Input:            getString(result, "input"),
			Nonce:            getString(result, "nonce"),
			TransactionIndex: getString(result, "transactionIndex"),
		}

		return tx, nil // Success
	}

	g.Log().Errorf(ctx, "Failed to get transaction after %d retries: %v", txMaxRetries, lastErr)
	return nil, lastErr
}

// GetTransactionReceipt retrieves a transaction receipt by hash
func GetTransactionReceipt(ctx context.Context, client *http.Client, rpcUrl, apiKey, hash string) (*EthTransactionReceipt, error) {
	// Prepare JSON-RPC request
	requestBody := fmt.Sprintf(`{
		"jsonrpc": "2.0",
		"method": "eth_GetTransactionReceipt",
		"params": ["%s"],
		"id": 1
	}`, hash)

	// Create request
	req, err := http.NewRequestWithContext(ctx, "POST", rpcUrl, strings.NewReader(requestBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	if apiKey != "" {
		req.Header.Set("Authorization", "Bearer "+apiKey)
	}
	req.Header.Set("Accept", "application/json")
	req.Header.Set("Content-Type", "application/json")

	// Send request with retries
	var respData map[string]any
	var lastErr error

	for attempt := 0; attempt < txMaxRetries; attempt++ {
		resp, err := client.Do(req)
		if err != nil {
			lastErr = fmt.Errorf("attempt %d: failed to execute request: %w", attempt+1, err)
			g.Log().Warningf(ctx, "%s. Retrying...", lastErr)
			time.Sleep(txRetryDelay)
			continue
		}

		bodyBytes, readErr := io.ReadAll(resp.Body)
		resp.Body.Close()
		if readErr != nil {
			lastErr = fmt.Errorf("attempt %d: failed to read response body: %w", attempt+1, readErr)
			g.Log().Warningf(ctx, "%s. Retrying...", lastErr)
			time.Sleep(txRetryDelay)
			continue
		}

		if resp.StatusCode != http.StatusOK {
			lastErr = fmt.Errorf("attempt %d: API returned non-OK status: %d. Body: %s", attempt+1, resp.StatusCode, string(bodyBytes))
			g.Log().Warningf(ctx, "%s. Retrying...", lastErr)
			time.Sleep(txRetryDelay)
			continue
		}

		// Parse response
		if err := gjson.Unmarshal(bodyBytes, &respData); err != nil {
			lastErr = fmt.Errorf("attempt %d: failed to decode response: %w. Body: %s", attempt+1, err, string(bodyBytes))
			g.Log().Warningf(ctx, "%s. Retrying...", lastErr)
			time.Sleep(txRetryDelay)
			continue
		}

		// Check for JSON-RPC error
		if errObj, ok := respData["error"]; ok {
			errMap, ok := errObj.(map[string]any)
			if ok {
				errMsg := fmt.Sprintf("JSON-RPC error: code=%v, message=%v", errMap["code"], errMap["message"])
				lastErr = fmt.Errorf("attempt %d: %s", attempt+1, errMsg)
				g.Log().Warningf(ctx, "%s. Retrying...", lastErr)
				time.Sleep(txRetryDelay)
				continue
			}
		}

		// Extract result from response
		result, ok := respData["result"].(map[string]any)
		if !ok {
			lastErr = fmt.Errorf("attempt %d: invalid response format, missing or invalid 'result'", attempt+1)
			g.Log().Warningf(ctx, "%s. Retrying...", lastErr)
			time.Sleep(txRetryDelay)
			continue
		}

		// Extract logs
		logsAny, _ := result["logs"].([]any)

		// Convert to EthTransactionReceipt
		receipt := &EthTransactionReceipt{
			TransactionHash:   getString(result, "transactionHash"),
			BlockNumber:       getString(result, "blockNumber"),
			BlockHash:         getString(result, "blockHash"),
			From:              getString(result, "from"),
			To:                getString(result, "to"),
			CumulativeGasUsed: getString(result, "cumulativeGasUsed"),
			GasUsed:           getString(result, "gasUsed"),
			ContractAddress:   getString(result, "contractAddress"),
			Logs:              logsAny,
			Status:            getString(result, "status"),
		}

		return receipt, nil // Success
	}

	g.Log().Errorf(ctx, "Failed to get transaction receipt after %d retries: %v", txMaxRetries, lastErr)
	return nil, lastErr
}

// Helper function to safely get string values from a map
func getString(m map[string]any, key string) string {
	if val, ok := m[key]; ok {
		if strVal, ok := val.(string); ok {
			return strVal
		}
	}
	return ""
}
