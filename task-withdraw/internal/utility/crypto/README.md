# Unified Crypto Package

This document describes the refactored crypto package that provides a unified interface for blockchain operations across Ethereum and TRON networks.

## Overview

The crypto package has been completely refactored to provide:

- **Unified Interface**: Single interface for all blockchain operations
- **Type Safety**: Strong typing with clear data structures
- **Error Handling**: Consistent error handling across all chains
- **Backward Compatibility**: Existing code continues to work with deprecation warnings
- **Extensibility**: Easy to add new blockchain support

## Architecture

### Core Components

1. **Types** (`types.go`): Defines all interfaces and data structures
2. **Manager** (`manager.go`): Central blockchain client manager
3. **ETH Client** (`eth_client.go`): Ethereum implementation
4. **TRON Client** (`tron_client.go`): TRON implementation  
5. **Unified Interface** (`crypto.go`): Convenience functions for common operations
6. **Legacy Support** (`client_manager.go`): Backward compatibility layer

### Key Interfaces

#### BlockchainClient
```go
type BlockchainClient interface {
    // Chain information
    GetChainType() ChainType
    GetChainID(ctx context.Context) (*big.Int, error)
    
    // Address operations
    GenerateAddress(privateKey *ecdsa.PrivateKey) (*Address, error)
    ValidateAddress(address string) bool
    GetAddressFromPrivateKey(privateKeyStr string) (string, error)
    
    // HD Wallet operations
    GenerateMnemonic() (string, error)
    ValidateMnemonic(mnemonic string) bool
    GeneratePrivateKey() (string, error)
    
    // Balance operations
    GetNativeBalance(ctx context.Context, address string) (*Balance, error)
    GetTokenBalance(ctx context.Context, address, contract string) (*Balance, error)
    
    // Transaction operations
    SendTransaction(ctx context.Context, req *SendTransactionRequest) (*Transaction, error)
    EstimateFee(ctx context.Context, req *SendTransactionRequest) (*FeeEstimate, error)
}
```

#### BlockchainManager
```go
type BlockchainManager interface {
    GetClient(chainType ChainType) (BlockchainClient, error)
    GetBalance(ctx context.Context, chainType ChainType, address string, tokenType TokenType) (*Balance, error)
    SendTransaction(ctx context.Context, chainType ChainType, req *SendTransactionRequest) (*Transaction, error)
    EstimateFee(ctx context.Context, chainType ChainType, req *SendTransactionRequest) (*FeeEstimate, error)
}
```

### Data Types

#### ChainType & TokenType
```go
type ChainType string
const (
    ChainETH  ChainType = "ETH"
    ChainTRON ChainType = "TRON"
)

type TokenType string
const (
    TokenNative TokenType = "native" // ETH, TRX
    TokenERC20  TokenType = "erc20"  // USDT on Ethereum
    TokenTRC20  TokenType = "trc20"  // USDT on TRON
)
```

#### Core Data Structures
```go
type Balance struct {
    Address      string          `json:"address"`
    ChainType    ChainType       `json:"chain_type"`
    NativeToken  decimal.Decimal `json:"native_token"`  // ETH or TRX
    TokenBalance decimal.Decimal `json:"token_balance"` // ERC20/TRC20
    TokenSymbol  string          `json:"token_symbol"`
    UpdatedAt    time.Time       `json:"updated_at"`
}

type Transaction struct {
    Hash            string          `json:"hash"`
    ChainType       ChainType       `json:"chain_type"`
    TokenType       TokenType       `json:"token_type"`
    FromAddress     string          `json:"from_address"`
    ToAddress       string          `json:"to_address"`
    Amount          decimal.Decimal `json:"amount"`
    Status          TxStatus        `json:"status"`
    CreatedAt       time.Time       `json:"created_at"`
}
```

## Usage Examples

### Basic Operations

```go
import "task-withdraw/internal/utility/crypto"

ctx := context.Background()

// Get balance
balance, err := crypto.GetETHBalance(ctx, "0x742d35Cc1234567890123456789012345678901234")
if err != nil {
    log.Fatal(err)
}
fmt.Printf("ETH Balance: %s\n", balance.NativeToken.String())

// Send transaction
amount := decimal.NewFromFloat(0.001) // 0.001 ETH
tx, err := crypto.SendETH(ctx, privateKey, toAddress, amount)
if err != nil {
    log.Fatal(err)
}
fmt.Printf("Transaction hash: %s\n", tx.Hash)
```

### Advanced Usage

```go
// Get manager instance
manager := crypto.DefaultManager()

// Get specific client
ethClient, err := manager.GetClient(crypto.ChainETH)
if err != nil {
    log.Fatal(err)
}

// Generate addresses
privateKey, _ := ethClient.GeneratePrivateKey()
address, _ := ethClient.GetAddressFromPrivateKey(privateKey)

// Custom transaction request
req := &crypto.SendTransactionRequest{
    PrivateKey:      privateKey,
    ToAddress:       toAddress,
    Amount:          decimal.NewFromFloat(10.0),
    TokenType:       crypto.TokenERC20,
    ContractAddress: "******************************************",
}

tx, err := manager.SendTransaction(ctx, crypto.ChainETH, req)
```

### Error Handling

```go
_, err := crypto.GetBalance(ctx, crypto.ChainETH, "invalid_address", crypto.TokenNative)
if err != nil {
    if blockchainErr, ok := err.(*crypto.BlockchainError); ok {
        fmt.Printf("Error code: %s\n", blockchainErr.Code)
        fmt.Printf("Chain: %s\n", blockchainErr.ChainType)
        fmt.Printf("Message: %s\n", blockchainErr.Message)
    }
}
```

## Migration Guide

### From Old Interface

The old interface is still supported but deprecated. Here's how to migrate:

#### Old Way (Deprecated)
```go
// Old balance checking
ethBalance, err := ethImpl.GetETHBalance(address)

// Old transaction sending  
txHash, err := ethImpl.SendETH(ctx, privateKey, toAddress, amountWei, gasLimit, gasPrice, maxFee)
```

#### New Way (Recommended)
```go
// New balance checking
balance, err := crypto.GetETHBalance(ctx, address)

// New transaction sending
amount := decimal.NewFromFloat(0.001)
tx, err := crypto.SendETH(ctx, privateKey, toAddress, amount)
```

### Configuration Migration

#### Old Configuration Access
```go
rpcUrl, err := crypto.GetEthRpcUrl(ctx) // Still works but deprecated
```

#### New Configuration Access
```go
manager := crypto.DefaultManager()
config, err := manager.GetConfig(crypto.ChainETH)
rpcUrl := config.RPCUrl
```

## Configuration

The package automatically loads configuration from GoFrame config system:

```yaml
blockchain:
  ETH:
    rpcUrl: "https://mainnet.infura.io/v3/your-project-id"
    erc20ContractAddress: "******************************************"
  TRON:
    rpcUrl: "grpc.trongrid.io:50051"
    apiKey: "your-api-key"
    trc20ContractAddress: "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"

tron_grpc_backup_urls:
  - "*************:50051"
  - "************:50051"
```

## Error Codes

The package defines standard error codes:

- `INVALID_ADDRESS`: Invalid blockchain address
- `INVALID_PRIVATE_KEY`: Invalid private key format
- `INSUFFICIENT_BALANCE`: Insufficient balance for transaction
- `INSUFFICIENT_GAS`: Insufficient gas for transaction
- `TRANSACTION_FAILED`: Transaction execution failed
- `NETWORK_ERROR`: Network connectivity error
- `CONTRACT_ERROR`: Smart contract interaction error
- `UNSUPPORTED_OPERATION`: Operation not supported

## Best Practices

1. **Always use context**: Pass context for proper cancellation and timeouts
2. **Handle errors properly**: Check for specific error codes when needed
3. **Use decimal.Decimal**: For all amount calculations to avoid floating point errors
4. **Validate inputs**: Always validate addresses and private keys before use
5. **Use the unified interface**: Prefer the new interface over direct implementations
6. **Cache clients**: The manager automatically caches clients for efficiency

## Backward Compatibility

All existing code continues to work unchanged. The old `client_manager.go` interface is preserved with deprecation warnings. The migration can be done gradually:

1. Start using new convenience functions (`crypto.GetETHBalance`, etc.)
2. Gradually replace direct client usage with manager interface
3. Update configuration access to use new manager methods
4. Replace old transaction sending with new unified interface

## Extension Points

To add support for new blockchains:

1. Implement the `BlockchainClient` interface
2. Add new `ChainType` and `TokenType` constants
3. Update the manager to handle the new chain type
4. Add convenience functions in `crypto.go`

Example:
```go
// Add to types.go
const ChainBTC ChainType = "BTC"

// Implement BTCClient
type BTCClient struct {
    // implementation
}

// Add to manager factory
case ChainBTC:
    newClient, err = NewBTCClient(config)
```

## Testing

See `example.go` for comprehensive usage examples and testing patterns.

## Dependencies

- `github.com/ethereum/go-ethereum`: Ethereum client and crypto
- `github.com/fbsobreira/gotron-sdk`: TRON client
- `github.com/shopspring/decimal`: Precise decimal arithmetic
- `github.com/tyler-smith/go-bip39`: BIP39 mnemonic support
- `github.com/gogf/gf/v2`: Configuration and logging

## Performance Considerations

- Clients are cached and reused automatically
- Connection pooling is handled by the underlying libraries
- Failed connections trigger automatic failover (TRON)
- Consider implementing connection limits for high-throughput applications

## Security Notes

- Private keys are never logged or stored
- All network connections use appropriate security (TLS/SSL)
- Input validation is performed at all entry points
- Sensitive operations require explicit context passing