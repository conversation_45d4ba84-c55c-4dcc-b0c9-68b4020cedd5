package service

import (
	"context"
	taskv1 "task-withdraw/api" // Assuming correct path to generated protobuf types
)

// IWithdrawalClient defines the interface for the withdrawal gRPC client service.
type IWithdrawalClient interface {
	// FetchPendingWithdrawals fetches pending withdrawal orders from the gRPC service.
	FetchPendingWithdrawals(ctx context.Context, batchSize int) ([]*taskv1.Withdrawal, error)
	// UpdateWithdrawalStatus updates the status of a withdrawal order via gRPC.
	UpdateWithdrawalStatus(ctx context.Context, req *taskv1.UpdateWithdrawalStatusRequest) error
	// Close closes the underlying gRPC connection.
	Close() error
}

var (
	// localWithdrawalClient holds the registered implementation.
	localWithdrawalClient IWithdrawalClient
)

// RegisterWithdrawalClient registers the withdrawal client service implementation.
// It is usually called in the init function of the implementing package.
func RegisterWithdrawalClient(i IWithdrawalClient) {
	localWithdrawalClient = i
}

// WithdrawalClient returns the registered withdrawal client service implementation.
// It panics if no implementation is registered.
func WithdrawalClient() IWithdrawalClient {
	if localWithdrawalClient == nil {
		panic("implement not found for interface IWithdrawalClient, forgot register?")
	}
	return localWithdrawalClient
}