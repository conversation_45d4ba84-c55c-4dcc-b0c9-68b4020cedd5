package task_withdraw

import (
	"context"

	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/net/grpcx"
	taskv1 "task-withdraw/api"
)

type Controller struct {
	taskv1.UnimplementedTaskServiceServer
}

func Register(s *grpcx.GrpcServer) {
	taskv1.RegisterTaskServiceServer(s.Server, &Controller{})
}

func (*Controller) ListWithdrawals(ctx context.Context, req *taskv1.ListWithdrawalsRequest) (res *taskv1.ApiResponse, err error) {
	return nil, gerror.NewCode(gcode.CodeNotImplemented)
}

func (*Controller) UpdateWithdrawalStatus(ctx context.Context, req *taskv1.UpdateWithdrawalStatusRequest) (res *taskv1.ApiResponse, err error) {
	return nil, gerror.NewCode(gcode.CodeNotImplemented)
}
