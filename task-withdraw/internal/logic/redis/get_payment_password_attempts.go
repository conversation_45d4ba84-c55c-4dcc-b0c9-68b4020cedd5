package redis

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
)

// GetPaymentPasswordAttempts 获取用户支付密码尝试次数。
// Note: This method is moved here from the original internal/logic/redis.go
func (s *sRedis) GetPaymentPasswordAttempts(ctx context.Context, userID uint64) (int64, error) {
	key := getPaymentAttemptsKey(userID)
	g.Log().Debugf(ctx, "Getting payment password attempts for user %d, key=%s", userID, key)

	v, err := s.Get(ctx, key) // Use the Get method of this service
	if err != nil {
		g.Log().<PERSON>rrorf(ctx, "Failed to get payment password attempts for user %d: %v", userID, err)
		return 0, err
	}

	if v == nil || v.IsNil() {
		return 0, nil // Key doesn't exist or value is nil
	}
	return v.Int64(), nil
}
