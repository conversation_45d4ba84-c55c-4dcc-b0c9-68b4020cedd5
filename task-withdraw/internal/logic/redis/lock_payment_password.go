package redis

import (
	"context"
	"time"

	"github.com/gogf/gf/v2/frame/g"
)

// LockPaymentPassword 锁定用户支付密码指定时间。
// Note: This method is moved here from the original internal/logic/redis.go
func (s *sRedis) LockPaymentPassword(ctx context.Context, userID uint64, duration time.Duration) error {
	key := getPaymentLockKey(userID)
	g.Log().Debugf(ctx, "Locking payment password for user %d, key=%s, duration=%v", userID, key, duration)

	// Use the Set method of this service
	err := s.Set(ctx, key, time.Now().Unix(), duration)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to set payment lock for user %d: %v", userID, err)
		return err
	}
	return nil
}
