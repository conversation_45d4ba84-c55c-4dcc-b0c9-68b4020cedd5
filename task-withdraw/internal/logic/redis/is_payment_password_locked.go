package redis

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
)

// IsPaymentPasswordLocked 检查用户支付密码是否被锁定。
// Note: This method is moved here from the original internal/logic/redis.go
func (s *sRedis) IsPaymentPasswordLocked(ctx context.Context, userID uint64) (bool, error) {
	key := getPaymentLockKey(userID)
	g.Log().Debugf(ctx, "Checking if payment password is locked for user %d, key=%s", userID, key)

	// Use the Exists method of this service
	exists, err := s.Exists(ctx, key)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to check if payment lock exists for user %d: %v", userID, err)
		return false, err
	}
	return exists, nil
}
