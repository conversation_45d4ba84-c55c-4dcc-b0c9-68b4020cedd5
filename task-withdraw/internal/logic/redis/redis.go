package redis

import (
	"fmt"
	"sync"

	"github.com/gogf/gf/v2/database/gredis"

	"task-withdraw/internal/service" // Import the service package
)

// sRedis implements the IRedis interface.
type sRedis struct {
	// clientCache caches Redis client instances.
	clientCache map[string]*gredis.Redis
	// cacheMutex protects clientCache.
	cacheMutex sync.RWMutex
}

// init registers the Redis service implementation.
func init() {
	service.RegisterRedis(NewRedis())
}

// NewRedis creates and returns a new instance of IRedis implementation.
func NewRedis() service.IRedis {
	return &sRedis{
		clientCache: make(map[string]*gredis.Redis),
	}
}

// --- Helper functions for keys ---

func getPaymentAttemptsKey(userID uint64) string {
	return fmt.Sprintf("payment_attempts:%d", userID)
}

func getPaymentLockKey(userID uint64) string {
	return fmt.Sprintf("payment_lock:%d", userID)
}
