package logic

import (
_ "task-withdraw/internal/client/task" // Register WithdrawalClient implementation.
	_ "task-withdraw/internal/logic/redis" // Register Redis logic implementation.
	_ "task-withdraw/internal/logic/task" // Register Task logic implementation.
)

func init() {
	// Service registration happens in service package's init() functions.
	// Ensure service.RedPacket() is called somewhere during initialization if needed,
	// but simply importing the service package is usually sufficient for init-based registration.

}
