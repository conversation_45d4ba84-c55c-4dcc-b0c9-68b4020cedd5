package grpc_updater

import (
	"context"
	"time"

	"task-withdraw/internal/service"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
)

// StartGrpcStatusUpdater starts the background worker goroutine that processes status update messages from Redis.
func StartGrpcStatusUpdater(ctx context.Context) {
	logPrefix := "[GrpcStatusUpdater]"
	glog.Infof(ctx, "%s Starting worker...", logPrefix)

	// Get dependencies (panics if services are not registered)
	redisClient := service.Redis().Client() // Get default Redis client
	withdrawalClient := service.WithdrawalClient()

	// Get configuration using defined keys
	queueName := g.Cfg().MustGet(ctx, CfgRedisQueueNameKey, "queue:withdrawal_status_update").String() // Keep default for safety
	dlqName := g.Cfg().MustGet(ctx, CfgRedisDlqName<PERSON>ey, "queue:withdrawal_status_update_dlq").String() // Keep default for safety
	brpopTimeout := g.Cfg().MustGet(ctx, CfgBrpopTimeoutKey, DefaultBrpopTimeout).Int()
	maxRetries := g.Cfg().MustGet(ctx, CfgMaxRetriesKey, DefaultMaxRetries).Int32()
	retryDelay := time.Duration(g.Cfg().MustGet(ctx, CfgRetryDelaySecondsKey, DefaultRetryDelaySecs).Int()) * time.Second

	if queueName == "" || dlqName == "" {
		// Use the config keys in the error message
		glog.Fatalf(ctx, "%s Redis queue names ('%s', '%s') cannot be empty in configuration. Worker stopping.", logPrefix, CfgRedisQueueNameKey, CfgRedisDlqNameKey)
		return // Or panic
	}
	if brpopTimeout <= 0 {
		glog.Warningf(ctx, "%s Invalid brpopTimeoutSeconds (%d), using default: %d", logPrefix, brpopTimeout, DefaultBrpopTimeout)
		brpopTimeout = DefaultBrpopTimeout
	}
	if maxRetries < 0 {
		glog.Warningf(ctx, "%s Invalid maxRetries (%d), using default: %d", logPrefix, maxRetries, DefaultMaxRetries)
		maxRetries = DefaultMaxRetries
	}

	glog.Infof(ctx, "%s Worker configured: Queue='%s', DLQ='%s', Timeout=%ds, MaxRetries=%d, RetryDelay=%s",
		logPrefix, queueName, dlqName, brpopTimeout, maxRetries, retryDelay)

	// Start the processing loop in a separate goroutine
	go func() {
		for {
			select {
			case <-ctx.Done():
				glog.Infof(ctx, "%s Context cancelled. Worker stopping...", logPrefix)
				return
			default:
				// Blocking pop from the queue. BRPop timeout expects int64 seconds.
				resultVars, err := redisClient.BRPop(ctx, int64(brpopTimeout), queueName)

				if err != nil {
					// Handle BRPop errors (e.g., context cancelled, connection error, timeout)
					if gerror.Is(err, context.Canceled) || gerror.Is(err, context.DeadlineExceeded) {
						glog.Debugf(ctx, "%s BRPop cancelled or timed out, checking context...", logPrefix)
						// Check context again immediately in the outer loop
						continue
					}
					// Check for Redis nil reply specifically for timeout (BRPop returns nil error on timeout)
					// Note: gredis behavior might vary slightly; testing is recommended.
					// If err is nil after timeout, resultVars should also be nil or empty.
					if err == nil && (resultVars == nil || len(resultVars) == 0) {
						glog.Debugf(ctx, "%s BRPop timed out after %d seconds, continuing loop.", logPrefix, brpopTimeout)
						continue // Timeout is expected, just continue polling
					}
					// Log other Redis errors and potentially pause before retrying
					glog.Errorf(ctx, "%s Error during BRPop on queue '%s': %v. Pausing before retry.", logPrefix, queueName, err)
					time.Sleep(5 * time.Second) // Pause briefly after other errors
					continue
				}

				// BRPop should return a slice [keyName, value]
				if len(resultVars) != 2 || resultVars[1] == nil || resultVars[1].IsNil() {
					glog.Warningf(ctx, "%s BRPop returned unexpected data (length %d) or nil value: %v. Skipping.", logPrefix, len(resultVars), resultVars)
					continue
				}

				// Extract the message string from the *gvar.Var
				messageJson := resultVars[1].String()
				glog.Debugf(ctx, "%s Received message from queue '%s': %s", logPrefix, queueName, messageJson)

				// Process the message
				ProcessMessage(ctx, withdrawalClient, redisClient, messageJson, queueName, dlqName, maxRetries, retryDelay)
			}
		}
	}()

	glog.Infof(ctx, "%s Worker goroutine started.", logPrefix)
}
