package grpc_updater

// 配置常量
const (
	// Configuration keys for the worker
	// Assuming these keys exist under a 'grpcUpdater' section in config.yaml
	CfgRedisQueueNameKey    = "grpcUpdater.redisQueueName"
	CfgRedisDlqNameKey      = "grpcUpdater.redisDlqName"
	CfgBrpopTimeoutKey      = "grpcUpdater.brpopTimeoutSeconds"
	CfgMaxRetriesKey        = "grpcUpdater.maxRetries"
	CfgRetryDelaySecondsKey = "grpcUpdater.retryDelaySeconds" // Optional: delay between retries

	DefaultBrpopTimeout   = 5  // Default BRPop timeout in seconds
	DefaultMaxRetries     = 5  // Default max retries for gRPC calls
	DefaultRetryDelaySecs = 10 // Default delay between retries in seconds
)
