package withdrawal_processor

import (
	"context"
	"strings" // Added for address validation
	"sync"

	"github.com/gogf/gf/v2/errors/gerror" // Import gerror
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/shopspring/decimal" // For precise amount handling
)

const (
	configRootKey       = "withdrawalProcessor"
	grpcConfigRootKey = "grpc" // New key for gRPC config section
)

// GrpcClientConfig holds configuration for a specific gRPC client connection.
type GrpcClientConfig struct {
	Endpoints []string `json:"endpoints"` // List of target service addresses
	ApiKeys   string   `json:"apiKeys"`   // API Key for authentication
	Balancer  string   `json:"balancer"`  // Optional: Load balancing strategy
	Timeout   int      `json:"timeout"`   // Optional: Call timeout in ms
}

// GrpcClientMap holds a map of gRPC client configurations.
type GrpcClientMap map[string]GrpcClientConfig

// GrpcConfig holds the top-level gRPC configuration.
type GrpcConfig struct {
	Client GrpcClientMap `json:"client"`
}

// WithdrawalConfig holds the configuration for the withdrawal processor task.
type WithdrawalConfig struct {
	Enabled   bool   `json:"enabled"`
	Spec      string `json:"spec"`
	BatchSize int    `json:"batchSize"`
	// PendingState, ProcessingState, CompletedState, FailedState, ManualReviewState are removed. Use constants from tg-bot-common/consts.
	Wallets        map[string]WalletConfig  `json:"wallets"`  // Keyed by chain name (e.g., "ETH", "TRON")
	Rpc            map[string]RpcConfig     `json:"rpc"`      // Keyed by chain name
	Retry          RetryConfig              `json:"retry"`
	Monitoring     MonitoringConfig         `json:"monitoring"`
	EnabledTokens  map[string]bool          `json:"enabledTokens"`  // Map of token standard symbol (e.g., USDT_ERC20) -> enabled status
	TokenContracts map[string]string        `json:"tokenContracts"` // Map of token standard symbol -> contract address
	TokenPrecisions map[string]int          `json:"tokenPrecisions"` // Map of token standard symbol -> chain precision
	// GrpcServerAddress string                `json:"grpcServerAddress"` // Removed: Address of the withdrawal gRPC service

	// Added: Holds the specific configuration for the TaskService gRPC client
	TaskServiceClientConfig GrpcClientConfig `json:"-"` // Loaded separately, ignore json tag
}


// WalletConfig holds hot wallet configurations for a specific chain.
type WalletConfig struct {
	PrivateKeyEnvVar string `json:"privateKeyEnvVar"` // Optional: Environment variable name holding the private key
	PrivateKey       string `json:"privateKey"`       // Optional: Direct private key (use with caution, prefer env var for production)
	Address          string `json:"address"`
}

// RpcConfig holds RPC endpoint configurations for a specific chain.
type RpcConfig struct {
	Url          string `json:"url"`
	ApiKey       string `json:"apiKey"`       // Changed from ApiKeyEnvVar, now directly holds the API key
	ChainId      int64  `json:"chainId"`      // Required for ETH-like chains (EIP-155)
}


// RetryConfig holds retry mechanism configurations.
type RetryConfig struct {
	Maxattempts        int      `json:"maxAttempts"`               // Keep json tag, remove yaml tag
	DelaySeconds       int      `json:"delaySeconds"`             // Keep json tag, remove yaml tag
	NonRetryableErrors []string `json:"nonRetryableErrors"` // Keep json tag, remove yaml tag
}

// MonitoringConfig holds monitoring and alerting configurations.
type MonitoringConfig struct {
	LowBalanceThreshold  map[string]string `json:"lowBalanceThreshold"` // Keyed by token symbol, amounts as strings
	AlertWebhookUrl      string            `json:"alertWebhookUrl"`
	AlertOnFailure       bool              `json:"alertOnFailure"`
	AlertOnLowBalance    bool              `json:"alertOnLowBalance"`
	AlertOnLimitExceeded bool              `json:"alertOnLimitExceeded"`
}

var (
	configInstance *WithdrawalConfig
	configOnce     sync.Once
	configLoadErr  error
)

// LoadConfig loads the withdrawal processor configuration from the central config file.
// It uses sync.Once to ensure the configuration is loaded only once.
func LoadConfig(ctx context.Context) (*WithdrawalConfig, error) {
	configOnce.Do(func() {
		logPrefix := "[WithdrawalConfig]"
		glog.Info(ctx, logPrefix, "Attempting to load configuration...")

		var cfg WithdrawalConfig
		// Load withdrawalProcessor specific config
		err := g.Cfg().MustGet(ctx, configRootKey).Scan(&cfg)
		if err != nil {
			configLoadErr = gerror.Wrapf(err, "%s failed to scan withdrawal processor configuration from key '%s'", logPrefix, configRootKey)
			glog.Error(ctx, configLoadErr)
			return
		}

		// Load gRPC client config separately
		var grpcCfg GrpcConfig
		err = g.Cfg().MustGet(ctx, grpcConfigRootKey).Scan(&grpcCfg)
		if err != nil {
			configLoadErr = gerror.Wrapf(err, "%s failed to scan gRPC configuration from key '%s'", logPrefix, grpcConfigRootKey)
			glog.Error(ctx, configLoadErr)
			return
		}

		// Extract and validate the specific 'user-service' client config
		taskServiceClientCfg, ok := grpcCfg.Client["user-service"]
		if !ok {
			configLoadErr = gerror.Newf("%s gRPC client configuration for 'user-service' not found under '%s.client'", logPrefix, grpcConfigRootKey)
			glog.Error(ctx, configLoadErr)
			return
		}
		if len(taskServiceClientCfg.Endpoints) == 0 || taskServiceClientCfg.Endpoints[0] == "" {
			configLoadErr = gerror.Newf("%s gRPC client 'user-service' requires at least one endpoint", logPrefix)
			glog.Error(ctx, configLoadErr)
			return
		}
		// API Key validation (optional, depends on whether it's mandatory)
		// if taskServiceClientCfg.ApiKeys == "" {
		// 	configLoadErr = gerror.Newf("%s gRPC client 'user-service' requires apiKeys", logPrefix)
		// 	glog.Error(ctx, configLoadErr)
		// 	return
		// }

		// Store the extracted config into the main config struct
		cfg.TaskServiceClientConfig = taskServiceClientCfg
		glog.Infof(ctx, "%s Loaded TaskService gRPC client config. Endpoint(s): %v, API Key provided: %t", logPrefix, cfg.TaskServiceClientConfig.Endpoints, cfg.TaskServiceClientConfig.ApiKeys != "")


		// Basic validation (add more as needed)
		if cfg.BatchSize <= 0 {
			cfg.BatchSize = 10 // Default batch size
			glog.Warningf(ctx, "%s BatchSize is invalid or not set, defaulting to %d", logPrefix, cfg.BatchSize)
		}
		// Removed validation for PendingState, CompletedState, FailedState as they are no longer in config.
		if len(cfg.Wallets) == 0 || len(cfg.Rpc) == 0 {
			configLoadErr = gerror.Newf("%s wallets and rpc configurations are required", logPrefix)
			glog.Error(ctx, configLoadErr)
			return
		}

		// Daily limit validation removed
		if err := validateDecimalMap(cfg.Monitoring.LowBalanceThreshold, "monitoring.lowBalanceThreshold"); err != nil {
			configLoadErr = err
			return
		}


		// Check and log EnabledTokens status
		if len(cfg.EnabledTokens) > 0 {
			glog.Infof(ctx, "%s Loaded EnabledTokens config: %v", logPrefix, cfg.EnabledTokens)
		} else {
			// If EnabledTokens is empty or nil, log a warning.
			// The processor logic will handle the default behavior (assuming enabled).
			glog.Warningf(ctx, "%s EnabledTokens map is empty or not configured. All tokens will be processed by default unless explicitly disabled later.", logPrefix)
			// Optionally initialize the map if nil to avoid nil pointer issues later, though the check logic should handle nil maps.
			if cfg.EnabledTokens == nil {
				cfg.EnabledTokens = make(map[string]bool)
			}
		}

		// Validate TokenContracts configuration
		if cfg.TokenContracts == nil {
			cfg.TokenContracts = make(map[string]string) // Initialize if nil
			glog.Warningf(ctx, "%s TokenContracts map is nil, initialized to empty map. Contract addresses must be configured for token withdrawals.", logPrefix)
		}
		for tokenStandard, enabled := range cfg.EnabledTokens {
			if !enabled {
				continue // Skip disabled tokens
			}
			// Skip native tokens like ETH, TRX as they don't have contract addresses in this context
			// Use strings.EqualFold for case-insensitive comparison if needed, but config keys are likely exact.
			if tokenStandard == "ETH" || tokenStandard == "TRX" {
				continue
			}

			contractAddress, found := cfg.TokenContracts[tokenStandard]
			if !found || contractAddress == "" {
				configLoadErr = gerror.Newf("%s enabled token '%s' is missing its contract address in tokenContracts configuration", logPrefix, tokenStandard)
				glog.Error(ctx, configLoadErr)
				return // Stop loading on critical config error
			}

			// Basic address format validation (can be enhanced)
			// Assuming keys like "USDT_ERC20", "XYZ_TRC20"
			if strings.HasSuffix(tokenStandard, "_ERC20") && !strings.HasPrefix(contractAddress, "0x") {
				configLoadErr = gerror.Newf("%s invalid contract address format for '%s': expected '0x' prefix, got '%s'", logPrefix, tokenStandard, contractAddress)
				glog.Error(ctx, configLoadErr)
				return
			}
			if strings.HasSuffix(tokenStandard, "_TRC20") && !strings.HasPrefix(contractAddress, "T") {
				configLoadErr = gerror.Newf("%s invalid contract address format for '%s': expected 'T' prefix, got '%s'", logPrefix, tokenStandard, contractAddress)
				glog.Error(ctx, configLoadErr)
				return
			}
			// Add more validation for other standards if needed

			glog.Debugf(ctx, "%s Validated contract address for %s: %s", logPrefix, tokenStandard, contractAddress)
		}

		// Removed validation for GrpcServerAddress as it's loaded separately now.

		configInstance = &cfg
		glog.Infof(ctx, "%s Withdrawal Processor configuration loaded successfully. Enabled: %t, Spec: %s", logPrefix, cfg.Enabled, cfg.Spec)
	})

	// Check if an error occurred during the first load attempt
	if configLoadErr != nil {
		// Return a new error wrapping the original one to avoid modifying the shared error variable
		return nil, gerror.Wrap(configLoadErr, "failed to load withdrawal configuration")
	}
	if configInstance == nil && configLoadErr == nil {
		// This case should ideally not happen if sync.Once works correctly,
		// but handle it defensively. It might mean MustGet returned nil without error.
		return nil, gerror.Newf("withdrawal configuration '%s' not found or is empty", configRootKey)
	}

	return configInstance, nil
}

// Helper function to validate map[string]string represents valid decimals
func validateDecimalMap(m map[string]string, path string) error {
	for k, v := range m {
		if _, err := decimal.NewFromString(v); err != nil {
			return gerror.Wrapf(err, "invalid decimal format for %s[%s]: %s", path, k, v)
		}
	}
	return nil
}
