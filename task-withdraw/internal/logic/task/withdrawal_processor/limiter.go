package withdrawal_processor

import (
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/os/gcache"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/shopspring/decimal"
)

// MemoryRateLimiter handles checking withdrawal single limits using gcache.
type MemoryRateLimiter struct {
	cache     *gcache.Cache
	singleCfg map[string]string // Map of tokenSymbol -> maxAmountString
}

// NewMemoryRateLimiter creates a new MemoryRateLimiter instance.
func NewMemoryRateLimiter(cache *gcache.Cache, singleCfg map[string]string) *MemoryRateLimiter {
	return &MemoryRateLimiter{
		cache:     cache,
		singleCfg: singleCfg,
	}
}

// CheckSingleLimit checks if the withdrawal amount exceeds the configured single transaction limit for the token.
func (l *MemoryRateLimiter) CheckSingleLimit(ctx context.Context, amount decimal.Decimal, tokenSymbol string) (bool, error) {
	logPrefix := "[MemoryRateLimiter.CheckSingleLimit]"
	maxAmountStr, ok := l.singleCfg[tokenSymbol]
	if !ok {
		glog.Warningf(ctx, "%s No single transaction limit configured for token %s. Allowing.", logPrefix, tokenSymbol)
		return true, nil // No limit configured, allow
	}

	maxAmountDecimal, err := decimal.NewFromString(maxAmountStr)
	if err != nil {
		// This should have been caught during config loading, but check again defensively.
		glog.Errorf(ctx, "%s Failed to parse configured max single amount '%s' for token %s: %v", logPrefix, maxAmountStr, tokenSymbol, err)
		// Fail safe: disallow if config is broken
		return false, gerror.Wrapf(err, "invalid single limit config for %s", tokenSymbol)
	}

	if amount.GreaterThan(maxAmountDecimal) {
		glog.Warningf(ctx, "%s Withdrawal amount %s %s exceeds single limit %s %s.", logPrefix, amount.String(), tokenSymbol, maxAmountDecimal.String(), tokenSymbol)
		return false, nil // Exceeds limit
	}

	glog.Debugf(ctx, "%s Withdrawal amount %s %s is within single limit %s %s.", logPrefix, amount.String(), tokenSymbol, maxAmountDecimal.String(), tokenSymbol)
	return true, nil // Within limit
}
