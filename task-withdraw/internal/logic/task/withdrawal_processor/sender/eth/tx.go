package eth

import (
	"context"
	"fmt"
	"math/big"
	"strings"

	// "task-withdraw/internal/model/entity" // Removed import

	ethereum "github.com/ethereum/go-ethereum" // Import ethereum package for CallMsg
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types" // Import types for Transaction
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/shopspring/decimal"
)

// txInputData holds validated and prepared input data for a transaction.
type txInputData struct {
	toAddress       common.Address
	amountWei       *big.Int
	contractAddress common.Address // Zero address if sending ETH
	isERC20         bool
	tokenSymbol     string // For logging
	// tokenDecimals field removed from struct
	recordLogPrefix string // For consistent logging
}

// txBuildParams holds parameters needed to build and sign the transaction.
type txBuildParams struct {
	nonce           uint64
	gasPrice        *big.Int
	gasLimit        uint64
	txData          []byte
	txValue         *big.Int
	txToAddress     common.Address // Recipient for ETH, Contract for ERC20
	recordLogPrefix string         // For consistent logging
}

// SendTransaction sends ETH or an ERC20 token by orchestrating preparation, building, and sending.
// It now accepts individual token parameters instead of entity.Tokens.
func (s *EthSender) SendTransaction(
	ctx context.Context,
	recipientAddress string,
	amount decimal.Decimal,
	symbol string, // e.g., "ETH", "USDT"
	standard string, // e.g., "NATIVE", "ERC20"
	contractAddress string, // Hex string, empty for native ETH
	precision int,
) (txHash string, err error) {
	// Use the passed parameters for logging
	recordLogPrefix := fmt.Sprintf("[EthSender.SendTransaction:To:%s:Amount:%s:%s:%s:Precision:%d]",
		recipientAddress, amount.String(), symbol, standard, precision)
	glog.Infof(ctx, "%s Attempting to send transaction.", recordLogPrefix)

	// Validate passed precision
	if precision <= 0 {
		return "", gerror.Newf("%s invalid precision passed: %d", recordLogPrefix, precision)
	}

	// 1. Prepare and Validate Input, passing the individual parameters
	inputData, err := s.prepareTxInput(ctx, recipientAddress, amount, symbol, standard, contractAddress, precision, recordLogPrefix)
	if err != nil {
		return "", err // Error already wrapped in prepareTxInput
	}

	// 2. Build Unsigned Transaction Parameters
	buildParams, err := s.buildUnsignedTx(ctx, inputData)
	if err != nil {
		return "", err // Error already wrapped in buildUnsignedTx
	}

	// 3. Sign and Send Transaction
	txHash, err = s.signAndSend(ctx, buildParams)
	if err != nil {
		return "", err // Error already wrapped in signAndSend
	}

	glog.Infof(ctx, "%s Transaction successfully sent. TxHash: %s", recordLogPrefix, txHash)
	return txHash, nil
}

// prepareTxInput validates inputs and prepares data needed for transaction building.
// It now accepts individual token parameters instead of entity.Tokens.
func (s *EthSender) prepareTxInput(
	ctx context.Context,
	recipientAddress string,
	amount decimal.Decimal,
	symbol string,
	standard string,
	contractAddressHex string, // Renamed for clarity
	precision int,
	recordLogPrefix string,
) (*txInputData, error) {
	// 1. Validate recipient address format
	if !common.IsHexAddress(recipientAddress) {
		return nil, gerror.Newf("%s invalid recipient address format: %s", recordLogPrefix, recipientAddress)
	}
	toAddress := common.HexToAddress(recipientAddress)

	// 2. Determine if ERC20 and validate contract address
	// Use standard parameter to determine if it's ERC20
	isERC20 := strings.ToUpper(standard) == "ERC20"
	var contractAddr common.Address // Renamed variable
	if isERC20 {
		if contractAddressHex == "" {
			return nil, gerror.Newf("%s contract address is missing for ERC20 token %s", recordLogPrefix, symbol)
		}
		if !common.IsHexAddress(contractAddressHex) {
			return nil, gerror.Newf("%s invalid contract address format for token %s: %s", recordLogPrefix, symbol, contractAddressHex)
		}
		contractAddr = common.HexToAddress(contractAddressHex)
	} else {
		// For native ETH, ensure contractAddressHex is empty or log a warning if provided unexpectedly
		if contractAddressHex != "" {
			glog.Warningf(ctx, "%s Contract address '%s' provided for native ETH transfer, it will be ignored.", recordLogPrefix, contractAddressHex)
		}
	}

	// 3. Convert amount to Wei/smallest unit using the passed precision
	amountWei := amount.Mul(decimal.New(1, int32(precision))).BigInt()
	if amountWei.Cmp(big.NewInt(0)) <= 0 {
		return nil, gerror.Newf("%s calculated amount in Wei is not positive: %s (using passed precision %d)", recordLogPrefix, amountWei.String(), precision)
	}
	glog.Debugf(ctx, "%s Amount in Wei/smallest unit: %s (using passed precision %d)", recordLogPrefix, amountWei.String(), precision)

	return &txInputData{
		toAddress:       toAddress,
		amountWei:       amountWei,
		contractAddress: contractAddr, // Use renamed variable
		isERC20:         isERC20,
		tokenSymbol:     symbol, // Use passed symbol
		// tokenDecimals field removed
		recordLogPrefix: recordLogPrefix,
	}, nil
}

// buildUnsignedTx gathers on-chain data, prepares tx data, estimates gas, and checks fees.
func (s *EthSender) buildUnsignedTx(ctx context.Context, inputData *txInputData) (*txBuildParams, error) {
	recordLogPrefix := inputData.recordLogPrefix // Use prefix from input data

	// 4. Get Nonce
	nonce, err := s.getNonce(ctx)
	if err != nil {
		return nil, gerror.Wrapf(err, "%s failed to get nonce", recordLogPrefix)
	}

	// 5. Get Gas Price
	gasPrice, err := s.getGasPrice(ctx)
	if err != nil {
		return nil, gerror.Wrapf(err, "%s failed to get gas price", recordLogPrefix)
	}

	// 6. Prepare transaction data
	var txData []byte
	var txValue *big.Int
	var txToAddress common.Address // This is the address the transaction is *sent* to

	if inputData.isERC20 {
		txValue = big.NewInt(0)                 // Value is 0 for ERC20 transfers
		txToAddress = inputData.contractAddress // Send transaction *to* the contract address

		// Ensure transfer ABI is loaded
		if loadErr := loadTransferABI(); loadErr != nil { // Use the specific loader
			return nil, gerror.Wrapf(loadErr, "%s failed to load transfer ABI", recordLogPrefix)
		}
		// Pack the transfer function call data using the specific transferABI
		txData, err = transferABI.Pack("transfer", inputData.toAddress, inputData.amountWei) // Use transferABI
		if err != nil {
			return nil, gerror.Wrapf(err, "%s failed to pack ERC20 transfer data", recordLogPrefix)
		}
		glog.Debugf(ctx, "%s Packed ERC20 transfer data: %x", recordLogPrefix, txData)
	} else {
		txValue = inputData.amountWei     // Value is the amount for ETH transfers
		txToAddress = inputData.toAddress // Send transaction directly *to* the recipient
		txData = nil
	}

	// 7. Get Gas Limit
	// Note: For ETH transfer, txToAddress is recipient. For ERC20, txToAddress is contract.
	gasLimit, err := s.getGasLimit(ctx, inputData.isERC20, txToAddress, txValue, txData)
	if err != nil {
		return nil, gerror.Wrapf(err, "%s failed to get gas limit", recordLogPrefix)
	}

	// 7.5 Check Estimated Fee against Max Fee Limit
	estimatedFeeWei := new(big.Int).Mul(gasPrice, new(big.Int).SetUint64(gasLimit))
	maxFeeEthDecimal := s.txParams.maxFeeEthDecimal
	if maxFeeEthDecimal.IsZero() {
		glog.Warningf(ctx, "%s MaxFeeEth configuration is zero or not loaded correctly. Skipping fee check.", recordLogPrefix)
	} else {
		maxFeeWei := maxFeeEthDecimal.Mul(decimal.New(1, 18)).BigInt() // ETH has 18 decimals
		glog.Debugf(ctx, "%s Estimated Fee: %s Wei, Max Allowed Fee: %s Wei", recordLogPrefix, estimatedFeeWei.String(), maxFeeWei.String())

		if estimatedFeeWei.Cmp(maxFeeWei) > 0 {
			errMsg := fmt.Sprintf("estimated fee %s Wei exceeds the configured max limit %s Wei (%s ETH)",
				estimatedFeeWei.String(), maxFeeWei.String(), maxFeeEthDecimal.String())
			glog.Errorf(ctx, "%s %s", recordLogPrefix, errMsg)
			return nil, gerror.Newf("%s: %s", recordLogPrefix, errMsg)
		}
		glog.Debugf(ctx, "%s Estimated fee is within the maximum limit.", recordLogPrefix)
	}

	return &txBuildParams{
		nonce:           nonce,
		gasPrice:        gasPrice,
		gasLimit:        gasLimit,
		txData:          txData,
		txValue:         txValue,
		txToAddress:     txToAddress,
		recordLogPrefix: recordLogPrefix,
	}, nil
}

// signAndSend builds the transaction, signs it, and sends it to the network.
func (s *EthSender) signAndSend(ctx context.Context, buildParams *txBuildParams) (string, error) {
	recordLogPrefix := buildParams.recordLogPrefix

	// 8. Build Transaction (Using legacy type for now)
	tx := types.NewTransaction(
		buildParams.nonce,
		buildParams.txToAddress,
		buildParams.txValue,
		buildParams.gasLimit,
		buildParams.gasPrice,
		buildParams.txData,
	)
	glog.Debugf(ctx, "%s Built transaction: Nonce=%d, To=%s, Value=%s, GasLimit=%d, GasPrice=%s",
		recordLogPrefix, buildParams.nonce, buildParams.txToAddress.Hex(), buildParams.txValue.String(), buildParams.gasLimit, buildParams.gasPrice.String())

	// 9. Sign Transaction
	signedTx, err := types.SignTx(tx, types.NewEIP155Signer(s.chainId), s.privateKey)
	if err != nil {
		return "", gerror.Wrapf(err, "%s failed to sign transaction", recordLogPrefix)
	}
	txHash := signedTx.Hash().Hex()
	glog.Debugf(ctx, "%s Transaction signed. TxHash: %s", recordLogPrefix, txHash)

	// 10. Send Raw Transaction
	err = s.client.SendTransaction(ctx, signedTx)
	if err != nil {
		// Log the raw error for detailed debugging
		glog.Errorf(ctx, "%s Failed to send transaction: Raw Error = %v", recordLogPrefix, err)

		// Provide more context for common errors
		errMsg := err.Error()
		if strings.Contains(errMsg, "nonce too low") {
			// Nonce issue, potentially needs manual reset or better nonce management
			// If using Redis nonce, this might indicate Redis state is out of sync.
			glog.Errorf(ctx, "%s Nonce mismatch detected. Consider checking Redis nonce key or RPC nonce.", recordLogPrefix)
			return "", gerror.Wrapf(err, "%s nonce too low", recordLogPrefix)
		} else if strings.Contains(errMsg, "insufficient funds") {
			// Covers both insufficient funds for value and insufficient funds for gas
			glog.Errorf(ctx, "%s Insufficient funds detected.", recordLogPrefix)
			return "", gerror.Wrapf(err, "%s insufficient funds", recordLogPrefix)
		} else if strings.Contains(errMsg, "transaction underpriced") {
			glog.Errorf(ctx, "%s Transaction underpriced. Consider increasing gas price.", recordLogPrefix)
			return "", gerror.Wrapf(err, "%s transaction underpriced", recordLogPrefix)
		}
		// Return wrapped generic error for other cases
		return "", gerror.Wrapf(err, "%s failed to send transaction", recordLogPrefix)
	}

	// 11. Return transaction hash on success
	finalTxHash := signedTx.Hash().Hex()
	glog.Infof(ctx, "%s Transaction sent successfully! TxHash: %s", recordLogPrefix, finalTxHash)
	return finalTxHash, nil
}

// --- Helper methods for Nonce, Gas Price, Gas Limit etc. ---

// getNonce determines the next nonce to use by querying the RPC node.
func (s *EthSender) getNonce(ctx context.Context) (uint64, error) {
	logPrefix := "[EthSender.getNonce]"
	
	// Always use RPC to get the pending nonce
	s.nonceLock.Lock() // Lock to prevent race conditions if this sender instance is used concurrently
	defer s.nonceLock.Unlock()
	
	// Use PendingNonceAt to get the next nonce to be used
	nonce, err := s.client.PendingNonceAt(ctx, s.publicKey)
	if err != nil {
		glog.Errorf(ctx, "%s Failed to get pending nonce via RPC for address %s: %v", logPrefix, s.publicKey.Hex(), err)
		return 0, gerror.Wrapf(err, "%s failed to get pending nonce via RPC for %s", logPrefix, s.publicKey.Hex())
	}
	glog.Debugf(ctx, "%s Got pending nonce %d via RPC for address %s.", logPrefix, nonce, s.publicKey.Hex())
	return nonce, nil
}

// getGasPrice determines the gas price based on the configured strategy.
func (s *EthSender) getGasPrice(ctx context.Context) (*big.Int, error) {
	logPrefix := "[EthSender.getGasPrice]"
	
	// Always use node suggested gas price
	gasPrice, err := s.client.SuggestGasPrice(ctx)
	if err != nil {
		glog.Errorf(ctx, "%s Failed to suggest gas price via RPC: %v", logPrefix, err)
		return nil, gerror.Wrap(err, "failed to suggest gas price via RPC")
	}
	glog.Debugf(ctx, "%s Using suggested gas price: %s Wei", logPrefix, gasPrice.String())
	return gasPrice, nil
}

// getGasLimit determines the gas limit for a transaction.
// It attempts to estimate gas and adds a 20% buffer for safety.
func (s *EthSender) getGasLimit(ctx context.Context, isERC20 bool, to common.Address, value *big.Int, data []byte) (uint64, error) {
	logPrefix := "[EthSender.getGasLimit]"
	
	// Default gas limits
	const (
		defaultETHGasLimit    = uint64(21000)
		defaultERC20GasLimit  = uint64(100000)
		gasLimitMultiplier    = 1.2  // 20% buffer
	)

	var baseLimit uint64

	if isERC20 {
		// Attempt to estimate gas for ERC20 transfer
		msg := ethereum.CallMsg{
			From:  s.publicKey, // Sender address
			To:    &to,         // Contract address
			Value: value,       // Should be 0 for ERC20 transfer
			Data:  data,        // Packed transfer data
		}
		estimatedGas, err := s.client.EstimateGas(ctx, msg)
		if err == nil {
			baseLimit = estimatedGas
			glog.Debugf(ctx, "%s Gas estimated successfully: %d", logPrefix, baseLimit)
		} else {
			baseLimit = defaultERC20GasLimit // Fallback to default
			glog.Warningf(ctx, "%s Gas estimation failed: %v. Using default limit: %d", logPrefix, err, baseLimit)
		}
	} else {
		// For native ETH transfer, use the standard gas limit
		baseLimit = defaultETHGasLimit
		glog.Debugf(ctx, "%s Using standard ETH gas limit: %d", logPrefix, baseLimit)
	}

	// Apply multiplier for safety buffer
	finalLimit := uint64(float64(baseLimit) * gasLimitMultiplier)

	// Ensure minimum gas limits
	if !isERC20 && finalLimit < defaultETHGasLimit {
		finalLimit = defaultETHGasLimit
	}

	glog.Debugf(ctx, "%s Using final gas limit: %d (Base: %d, Multiplier: %.2f)", logPrefix, finalLimit, baseLimit, gasLimitMultiplier)

	return finalLimit, nil
}
