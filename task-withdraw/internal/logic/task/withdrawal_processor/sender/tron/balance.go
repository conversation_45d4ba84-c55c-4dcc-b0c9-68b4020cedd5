package tron

import (
	"context"
	"fmt"
	"strings"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/shopspring/decimal"
)

// Removed trc20BalanceOfSignature constant

// GetBalance retrieves the balance of the hot wallet for a given symbol (TRX or TRC20).
// For TRC20 tokens, contractAddressStr and precision must be provided.
func (s *TronSender) GetBalance(ctx context.Context, symbol string, precision int, contractAddressStr string) (decimal.Decimal, error) {
	logPrefix := fmt.Sprintf("[TronSender.GetBalance:%s]", symbol)
	glog.Debugf(ctx, "%s Getting balance for address %s", logPrefix, s.publicKey)

	// Check if it's native TRX balance request
	// contractAddressStr will be empty for native TRX, passed from withdrawal_handler
	if contractAddressStr == "" {
		if strings.ToUpper(symbol) != "TRX" {
			// This case should ideally not happen if called correctly from handler
			return decimal.Zero, gerror.Newf("%s contract address is empty but symbol is not TRX (%s)", logPrefix, symbol)
		}
		// Get TRX balance using crypto client
		balance, err := s.client.GetNativeBalance(ctx, s.publicKey)
		if err != nil {
			if strings.Contains(err.Error(), "account not found") {
				glog.Warningf(ctx, "%s Account %s not found on chain, returning zero balance.", logPrefix, s.publicKey)
				return decimal.Zero, nil
			}
			return decimal.Zero, gerror.Wrapf(err, "%s failed to get TRX balance for %s", logPrefix, s.publicKey)
		}
		glog.Debugf(ctx, "%s TRX Balance: %s TRX", logPrefix, balance.NativeToken.String())
		return balance.NativeToken, nil
	}

	// --- Handle TRC20 Token Balance ---

	// Validate contract address format (basic check)
	if !strings.HasPrefix(contractAddressStr, "T") || len(contractAddressStr) != 34 {
		return decimal.Zero, gerror.Newf("%s invalid TRC20 contract address format: %s", logPrefix, contractAddressStr)
	}

	// Validate precision (passed from handler, now from config)
	if precision <= 0 {
		// Precision should always be positive, this indicates a config error.
		return decimal.Zero, gerror.Newf("%s invalid precision value %d for token %s from config", logPrefix, precision, symbol)
	}

	// Get TRC20 token balance using crypto client
	balance, err := s.client.GetTokenBalance(ctx, s.publicKey, contractAddressStr)
	if err != nil {
		// Check for "account not found" specifically for TRC20
		if strings.Contains(err.Error(), "account not found") || strings.Contains(err.Error(), "contract validate error") {
			glog.Warningf(ctx, "%s Account %s or Contract %s not found on chain for TRC20 balance check, returning zero balance.", logPrefix, s.publicKey, contractAddressStr)
			return decimal.Zero, nil
		}
		// Log other errors
		glog.Errorf(ctx, "%s Raw error from GetTokenBalance: Type=%T, Value=%#v", logPrefix, err, err)
		return decimal.Zero, gerror.Wrapf(err, "%s failed to get TRC20 balance for %s contract %s", logPrefix, symbol, contractAddressStr)
	}

	glog.Debugf(ctx, "%s %s Balance: %s", logPrefix, symbol, balance.TokenBalance.String())
	return balance.TokenBalance, nil
}
