package tron

import (
	"context"
	"fmt"
	"strings"

	"task-withdraw/internal/utility/crypto"
	tronwallet "task-withdraw/internal/utility/tron-wallet"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/shopspring/decimal"
)

const (
	// TRC20 Method Signatures (first 4 bytes of Keccak256 hash)
	trc20TransferSignature = "transfer(address,uint256)"
)

// SendTransaction sends TRX or a TRC20 token.
// It now accepts individual token parameters instead of entity.Tokens.
func (s *TronSender) SendTransaction(
	ctx context.Context,
	recipientAddress string,
	amount decimal.Decimal,
	symbol string, // e.g., "TRX", "USDT"
	standard string, // e.g., "NATIVE", "TRC20"
	contractAddressHex string, // Renamed for clarity, Base58 string
	precision int,
) (txHash string, err error) {
	// Use the passed parameters for logging
	recordLogPrefix := fmt.Sprintf("[TronSender.SendTransaction:To:%s:Amount:%s:%s:%s:Precision:%d]",
		recipientAddress, amount.String(), symbol, standard, precision)
	glog.Infof(ctx, "%s Attempting to send transaction.", recordLogPrefix)

	// Validate passed precision
	if precision <= 0 {
		return "", gerror.Newf("%s invalid precision passed: %d", recordLogPrefix, precision)
	}

	// 1. Validate recipient address using crypto client
	if !s.client.ValidateAddress(recipientAddress) {
		return "", gerror.Newf("%s invalid recipient address format: %s", recordLogPrefix, recipientAddress)
	}

	// 2. Determine if TRC20 and validate contract address
	isTRC20 := strings.ToUpper(standard) == "TRC20"
	var contractAddr string // Renamed variable
	if isTRC20 {
		if contractAddressHex == "" {
			return "", gerror.Newf("%s contract address is missing for TRC20 token %s", recordLogPrefix, symbol)
		}
		// Validate TRON contract address format
		if !s.client.ValidateAddress(contractAddressHex) {
			return "", gerror.Newf("%s invalid contract address format for token %s: %s", recordLogPrefix, symbol, contractAddressHex)
		}
		contractAddr = contractAddressHex
	} else {
		// For native TRX, ensure contractAddressHex is empty or log a warning
		if contractAddressHex != "" {
			glog.Warningf(ctx, "%s Contract address '%s' provided for native TRX transfer, it will be ignored.", recordLogPrefix, contractAddressHex)
		}
	}

	// 3. Validate amount
	if amount.LessThanOrEqual(decimal.Zero) {
		return "", gerror.Newf("%s amount must be positive: %s", recordLogPrefix, amount.String())
	}
	glog.Debugf(ctx, "%s Amount: %s", recordLogPrefix, amount.String())

	// 4. Determine Fee Limit - validate configuration
	feeLimit := s.txParams.FeeLimitSun
	if feeLimit <= 0 {
		return "", gerror.Newf("%s FeeLimitSun is invalid or missing in config: %d. Please check auto_withdrawal_setting.max_fee configuration", recordLogPrefix, feeLimit)
	}
	glog.Debugf(ctx, "%s Using Fee Limit: %d Sun", recordLogPrefix, feeLimit)

	// 5. Create transaction using crypto client
	var tokenType crypto.TokenType
	if isTRC20 {
		tokenType = crypto.TokenTRC20
	} else {
		tokenType = crypto.TokenNative
	}

	// Create transaction request
	maxFeeTRX := decimal.NewFromInt(feeLimit).Div(decimal.NewFromInt(1000000)) // Convert Sun to TRX
	privateKeyHex := tronwallet.PrivateKeyToHex(s.privateKey)
	req := &crypto.SendTransactionRequest{
		PrivateKey:      privateKeyHex,
		FromAddress:     s.publicKey,
		ToAddress:       recipientAddress,
		Amount:          amount,
		TokenType:       tokenType,
		ContractAddress: contractAddr,
		MaxFee:          &maxFeeTRX,
	}

	tx, err := s.client.SendTransaction(ctx, req)
	if err != nil {
		return "", gerror.Wrapf(err, "%s failed to send transaction using crypto client", recordLogPrefix)
	}

	// 6. Validate transaction hash
	if tx.Hash == "" {
		return "", gerror.Newf("%s transaction hash is empty", recordLogPrefix)
	}
	glog.Debugf(ctx, "%s Transaction created. Hash: %s", recordLogPrefix, tx.Hash)

	glog.Infof(ctx, "%s Transaction sent successfully via crypto client! TxHash: %s", recordLogPrefix, tx.Hash)
	return strings.TrimPrefix(tx.Hash, "0x"), nil
}
