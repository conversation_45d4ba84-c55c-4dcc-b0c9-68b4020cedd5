package withdrawal_processor

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/shopspring/decimal"

	"task-withdraw/internal/service"
	"task-withdraw/internal/utility/crypto/tron"
)

// EnergyOrderStatus represents the status of an energy order
type EnergyOrderStatus string

const (
	EnergyOrderStatusPending   EnergyOrderStatus = "pending"   // 订单已创建，等待能量到账
	EnergyOrderStatusChecking  EnergyOrderStatus = "checking"  // 正在检查订单状态
	EnergyOrderStatusCompleted EnergyOrderStatus = "completed" // 订单已完成，能量已到账
	EnergyOrderStatusFailed    EnergyOrderStatus = "failed"    // 订单失败
	EnergyOrderStatusExpired   EnergyOrderStatus = "expired"   // 订单已过期
)

// EnergyOrder represents an energy purchase order stored in Redis
type EnergyOrder struct {
	OrderID        string            `json:"order_id"`        // iTRX订单号
	TargetAddress  string            `json:"target_address"`  // 目标收款地址（提现地址）
	PoolAddress    string            `json:"pool_address"`    // 资金池地址（购买能量的地址）
	EnergyAmount   int               `json:"energy_amount"`   // 购买的能量数量
	Status         EnergyOrderStatus `json:"status"`          // 订单状态
	CreatedAt      time.Time         `json:"created_at"`      // 创建时间
	UpdatedAt      time.Time         `json:"updated_at"`      // 更新时间
	ExpireAt       time.Time         `json:"expire_at"`       // 过期时间
	RetryCount     int               `json:"retry_count"`     // 重试次数
	LastCheckAt    time.Time         `json:"last_check_at"`   // 最后检查时间
	WithdrawalID   int64             `json:"withdrawal_id"`   // 关联的提现订单ID
	ErrorMessage   string            `json:"error_message"`   // 错误信息
}

// EnergyManagerConfig represents the configuration for energy management
type EnergyManagerConfig struct {
	Enabled              bool    `json:"enabled"`                // 是否启用能量管理
	EnergyReservePercent float64 `json:"energy_reserve_percent"` // 能量储备百分比
	OrderTimeoutHours    int     `json:"order_timeout_hours"`    // 订单超时时间(小时)
	MaxRetryCount        int     `json:"max_retry_count"`        // 最大重试次数
	CheckIntervalSeconds int     `json:"check_interval_seconds"` // 检查间隔(秒)
}

// Redis键名定义
const (
	RedisKeyEnergyOrderPrefix = "energy:order:"       // 单个订单信息前缀
	RedisKeyPendingOrders     = "energy:pending"      // 待检查订单列表
	RedisKeyOrderStats        = "energy:stats"        // 订单统计信息
	RedisKeyConfigPrefix      = "energy:config"       // 配置信息前缀
)

// EnergyManager handles energy purchase and management for TRC20 transactions
type EnergyManager struct {
	config *EnergyManagerConfig
}

// NewEnergyManager creates a new energy manager instance
func NewEnergyManager(config *EnergyManagerConfig) *EnergyManager {
	return &EnergyManager{
		config: config,
	}
}

// GetDefaultEnergyManagerConfig returns default configuration for energy management
func GetDefaultEnergyManagerConfig() *EnergyManagerConfig {
	return &EnergyManagerConfig{
		Enabled:              true,
		EnergyReservePercent: 5.0, // 5%储备
		OrderTimeoutHours:    24,  // 24小时超时
		MaxRetryCount:        3,   // 最大重试3次
		CheckIntervalSeconds: 30,  // 30秒检查间隔
	}
}

// StoreEnergyOrder stores an energy order to Redis
func (em *EnergyManager) StoreEnergyOrder(ctx context.Context, order *EnergyOrder) error {
	logPrefix := "[EnergyManager.StoreEnergyOrder]"
	
	// 设置时间戳
	now := time.Now()
	order.CreatedAt = now
	order.UpdatedAt = now
	order.ExpireAt = now.Add(time.Duration(em.config.OrderTimeoutHours) * time.Hour)
	order.Status = EnergyOrderStatusPending
	order.RetryCount = 0

	// 序列化订单信息
	orderJSON, err := json.Marshal(order)
	if err != nil {
		return gerror.Wrapf(err, "%s failed to marshal energy order", logPrefix)
	}

	redis := service.Redis()
	
	// 存储订单详情
	orderKey := RedisKeyEnergyOrderPrefix + order.OrderID
	err = redis.Set(ctx, orderKey, string(orderJSON), time.Duration(em.config.OrderTimeoutHours)*time.Hour)
	if err != nil {
		return gerror.Wrapf(err, "%s failed to store energy order to Redis", logPrefix)
	}

	// 添加到待检查列表
	_, err = redis.LPush(ctx, RedisKeyPendingOrders, order.OrderID)
	if err != nil {
		glog.Errorf(ctx, "%s failed to add order to pending list: %v", logPrefix, err)
		// 这个错误不返回，因为主要存储已经成功
	}

	glog.Infof(ctx, "%s stored energy order: %s for address %s", logPrefix, order.OrderID, order.TargetAddress)
	return nil
}

// GetEnergyOrder retrieves an energy order from Redis
func (em *EnergyManager) GetEnergyOrder(ctx context.Context, orderID string) (*EnergyOrder, error) {
	logPrefix := "[EnergyManager.GetEnergyOrder]"
	
	redis := service.Redis()
	orderKey := RedisKeyEnergyOrderPrefix + orderID

	orderJSON, err := redis.Get(ctx, orderKey)
	if err != nil {
		return nil, gerror.Wrapf(err, "%s failed to get energy order from Redis", logPrefix)
	}

	if orderJSON.IsNil() {
		return nil, nil // 订单不存在
	}

	var order EnergyOrder
	err = json.Unmarshal([]byte(orderJSON.String()), &order)
	if err != nil {
		return nil, gerror.Wrapf(err, "%s failed to unmarshal energy order", logPrefix)
	}

	return &order, nil
}

// UpdateEnergyOrderStatus updates the status of an energy order
func (em *EnergyManager) UpdateEnergyOrderStatus(ctx context.Context, orderID string, status EnergyOrderStatus, errorMsg string) error {
	logPrefix := "[EnergyManager.UpdateEnergyOrderStatus]"
	
	order, err := em.GetEnergyOrder(ctx, orderID)
	if err != nil {
		return gerror.Wrapf(err, "%s failed to get order for update", logPrefix)
	}
	if order == nil {
		return gerror.Newf("%s order not found: %s", logPrefix, orderID)
	}

	// 更新状态
	order.Status = status
	order.UpdatedAt = time.Now()
	order.LastCheckAt = time.Now()
	if errorMsg != "" {
		order.ErrorMessage = errorMsg
	}

	// 重新存储
	orderJSON, err := json.Marshal(order)
	if err != nil {
		return gerror.Wrapf(err, "%s failed to marshal updated order", logPrefix)
	}

	redis := service.Redis()
	orderKey := RedisKeyEnergyOrderPrefix + orderID
	err = redis.Set(ctx, orderKey, string(orderJSON), time.Duration(em.config.OrderTimeoutHours)*time.Hour)
	if err != nil {
		return gerror.Wrapf(err, "%s failed to update energy order in Redis", logPrefix)
	}

	glog.Debugf(ctx, "%s updated order %s status to %s", logPrefix, orderID, status)
	return nil
}

// RemoveEnergyOrder removes an energy order from Redis
func (em *EnergyManager) RemoveEnergyOrder(ctx context.Context, orderID string) error {
	logPrefix := "[EnergyManager.RemoveEnergyOrder]"
	
	redis := service.Redis()
	orderKey := RedisKeyEnergyOrderPrefix + orderID

	// 删除订单详情
	err := redis.Remove(ctx, orderKey)
	if err != nil {
		glog.Errorf(ctx, "%s failed to delete order from Redis: %v", logPrefix, err)
	}

	// 从待检查列表中移除 - 这需要直接使用客户端，因为LRem不在服务接口中
	client := redis.Client()
	_, err = client.LRem(ctx, RedisKeyPendingOrders, 0, orderID)
	if err != nil {
		glog.Errorf(ctx, "%s failed to remove order from pending list: %v", logPrefix, err)
	}

	glog.Infof(ctx, "%s removed energy order: %s", logPrefix, orderID)
	return nil
}

// GetPendingEnergyOrder checks if there's a pending energy order for the given target address
func (em *EnergyManager) GetPendingEnergyOrder(ctx context.Context, targetAddress string) (*EnergyOrder, error) {
	logPrefix := "[EnergyManager.GetPendingEnergyOrder]"
	
	redis := service.Redis()
	client := redis.Client()
	
	// 获取所有待检查的订单ID
	orderIDs, err := client.LRange(ctx, RedisKeyPendingOrders, 0, -1)
	if err != nil {
		return nil, gerror.Wrapf(err, "%s failed to get pending orders list", logPrefix)
	}

	// 遍历查找匹配的订单
	for _, orderIDVar := range orderIDs {
		orderID := orderIDVar.String()
		order, err := em.GetEnergyOrder(ctx, orderID)
		if err != nil {
			glog.Errorf(ctx, "%s failed to get order %s: %v", logPrefix, orderID, err)
			continue
		}
		if order == nil {
			// 订单不存在，从列表中移除
			client.LRem(ctx, RedisKeyPendingOrders, 0, orderID)
			continue
		}

		// 检查是否匹配目标地址
		if order.TargetAddress == targetAddress {
			// 检查是否过期
			if time.Now().After(order.ExpireAt) {
				// 订单已过期，更新状态并移除
				em.UpdateEnergyOrderStatus(ctx, orderID, EnergyOrderStatusExpired, "order expired")
				em.RemoveEnergyOrder(ctx, orderID)
				continue
			}
			return order, nil
		}
	}

	return nil, nil // 没有找到匹配的订单
}

// CheckEnergyOrderStatus checks the status of an energy order via iTRX API
func (em *EnergyManager) CheckEnergyOrderStatus(ctx context.Context, orderID string, apiKey string, apiBaseUrl string) (EnergyOrderStatus, error) {
	logPrefix := "[EnergyManager.CheckEnergyOrderStatus]"
	
	// 调用iTRX API查询订单状态
	orderResp, err := tron.QueryItrxOrderStatus(ctx, apiKey, apiBaseUrl, orderID)
	if err != nil {
		return EnergyOrderStatusFailed, gerror.Wrapf(err, "%s failed to query iTRX order status", logPrefix)
	}

	// 根据iTRX返回的状态码转换为内部状态
	// Status mapping from iTRX API documentation:
	// (0, '超时关闭'), (10, '等待支付'), (20, '已支付'), 
	// (30, '委托准备中'), (31, '部分委托'), (32, '异常重试中'),
	// (40, '正常完成'), (41, '退款终止'), (43, '异常终止')
	switch orderResp.Status {
	case 40: // 正常完成
		return EnergyOrderStatusCompleted, nil
	case 30, 31: // 委托准备中, 部分委托
		return EnergyOrderStatusPending, nil
	case 20: // 已支付
		return EnergyOrderStatusPending, nil
	case 10: // 等待支付
		return EnergyOrderStatusPending, nil
	case 32: // 异常重试中
		return EnergyOrderStatusPending, nil
	case 0, 41, 43: // 超时关闭, 退款终止, 异常终止
		return EnergyOrderStatusFailed, nil
	default:
		// 未知状态码也视为失败
		return EnergyOrderStatusFailed, fmt.Errorf("unknown order status: %d", orderResp.Status)
	}
}

// CalculateRequiredEnergy calculates the required energy based on recipient address balance
func (em *EnergyManager) CalculateRequiredEnergy(ctx context.Context, recipientAddress string, apiKey string, apiBaseUrl string) (int, error) {
	logPrefix := "[EnergyManager.CalculateRequiredEnergy]"
	
	// 获取平台数据
	platformData, err := tron.GetItrxPlatformData(ctx, apiKey, apiBaseUrl)
	if err != nil {
		return 0, gerror.Wrapf(err, "%s failed to get iTRX platform data", logPrefix)
	}

	// 检查收款地址的USDT余额
	// 这里需要实现获取TRC20 USDT余额的逻辑
	balance, err := em.getTRC20USDTBalance(ctx, recipientAddress)
	if err != nil {
		glog.Warningf(ctx, "%s failed to get recipient balance, assuming new address: %v", logPrefix, err)
		// 如果获取余额失败，按新地址处理
		balance = decimal.Zero
	}

	// 根据余额判断使用哪个能量需求值
	var baseEnergy int
	if balance.Equal(decimal.Zero) {
		// 无U地址
		baseEnergy = platformData.UsdtEnergyNeedNew
		glog.Debugf(ctx, "%s recipient %s is new address, required energy: %d", logPrefix, recipientAddress, baseEnergy)
	} else {
		// 有U地址
		baseEnergy = platformData.UsdtEnergyNeedOld
		glog.Debugf(ctx, "%s recipient %s has balance %s, required energy: %d", logPrefix, recipientAddress, balance.String(), baseEnergy)
	}

	// 计算安全储备
	reserveAmount := float64(baseEnergy) * (em.config.EnergyReservePercent / 100.0)
	totalRequired := baseEnergy + int(reserveAmount)

	glog.Debugf(ctx, "%s calculated total required energy: %d (base: %d, reserve: %.0f)", 
		logPrefix, totalRequired, baseEnergy, reserveAmount)
	
	return totalRequired, nil
}

// PurchaseEnergyIfNeeded checks energy and purchases if needed
func (em *EnergyManager) PurchaseEnergyIfNeeded(ctx context.Context, 
	poolAddress string, 
	targetAddress string, 
	withdrawalID int64,
	apiKey string, 
	apiSecret string, 
	apiBaseUrl string) (*EnergyOrder, error) {
	
	logPrefix := "[EnergyManager.PurchaseEnergyIfNeeded]"
	
	// 1. 计算所需能量
	requiredEnergy, err := em.CalculateRequiredEnergy(ctx, targetAddress, apiKey, apiBaseUrl)
	if err != nil {
		return nil, gerror.Wrapf(err, "%s failed to calculate required energy", logPrefix)
	}
	
	// 2. 获取当前资金池能量
	currentEnergy, err := em.getCurrentPoolEnergy(ctx, poolAddress)
	if err != nil {
		return nil, gerror.Wrapf(err, "%s failed to get current pool energy", logPrefix)
	}
	
	glog.Infof(ctx, "%s current energy: %d, required: %d for address %s", 
		logPrefix, currentEnergy, requiredEnergy, targetAddress)
	
	// 3. 检查是否需要购买能量
	if currentEnergy >= int64(requiredEnergy) {
		glog.Infof(ctx, "%s sufficient energy available, no purchase needed", logPrefix)
		return nil, nil // 不需要购买
	}
	
	// 4. 计算需要购买的能量
	needToPurchase := requiredEnergy - int(currentEnergy)
	if needToPurchase < 0 {
		needToPurchase = requiredEnergy // 安全起见，至少购买所需的能量
	}
	
	glog.Infof(ctx, "%s need to purchase %d energy for %s", logPrefix, needToPurchase, targetAddress)
	
	// 5. 购买能量
	orderResp, err := tron.RentItrxEnergy(ctx, apiKey, apiSecret, apiBaseUrl, poolAddress, needToPurchase, "1H")
	if err != nil {
		return nil, gerror.Wrapf(err, "%s failed to purchase energy", logPrefix)
	}
	
	// 6. 创建并存储订单记录
	order := &EnergyOrder{
		OrderID:       orderResp.Serial,
		TargetAddress: targetAddress,
		PoolAddress:   poolAddress,
		EnergyAmount:  needToPurchase,
		WithdrawalID:  withdrawalID,
	}
	
	err = em.StoreEnergyOrder(ctx, order)
	if err != nil {
		return nil, gerror.Wrapf(err, "%s failed to store energy order", logPrefix)
	}
	
	glog.Infof(ctx, "%s successfully purchased energy. OrderID: %s, Amount: %d", 
		logPrefix, order.OrderID, order.EnergyAmount)
	
	return order, nil
}

// ProcessPendingEnergyOrder processes a pending energy order and returns whether to continue
func (em *EnergyManager) ProcessPendingEnergyOrder(ctx context.Context, 
	targetAddress string, 
	apiKey string, 
	apiBaseUrl string) (shouldContinue bool, err error) {
	
	logPrefix := "[EnergyManager.ProcessPendingEnergyOrder]"
	
	// 查找待处理的订单
	pendingOrder, err := em.GetPendingEnergyOrder(ctx, targetAddress)
	if err != nil {
		return false, gerror.Wrapf(err, "%s failed to get pending order", logPrefix)
	}
	
	if pendingOrder == nil {
		// 没有待处理订单，可以继续
		return true, nil
	}
	
	glog.Infof(ctx, "%s found pending energy order: %s for address %s", 
		logPrefix, pendingOrder.OrderID, targetAddress)
	
	// 检查订单状态
	status, err := em.CheckEnergyOrderStatus(ctx, pendingOrder.OrderID, apiKey, apiBaseUrl)
	if err != nil {
		glog.Errorf(ctx, "%s failed to check order status for %s: %v", 
			logPrefix, pendingOrder.OrderID, err)
		
		// 增加重试计数
		pendingOrder.RetryCount++
		if pendingOrder.RetryCount >= em.config.MaxRetryCount {
			// 超过最大重试次数，标记为失败
			em.UpdateEnergyOrderStatus(ctx, pendingOrder.OrderID, EnergyOrderStatusFailed, 
				fmt.Sprintf("max retries reached: %v", err))
			em.RemoveEnergyOrder(ctx, pendingOrder.OrderID)
			return false, gerror.Newf("%s order %s failed after max retries", logPrefix, pendingOrder.OrderID)
		}
		
		// 更新重试计数但保持pending状态
		em.UpdateEnergyOrderStatus(ctx, pendingOrder.OrderID, EnergyOrderStatusPending, 
			fmt.Sprintf("retry %d: %v", pendingOrder.RetryCount, err))
		return false, gerror.Newf("%s order %s still checking, will retry", logPrefix, pendingOrder.OrderID)
	}
	
	// 更新订单状态
	em.UpdateEnergyOrderStatus(ctx, pendingOrder.OrderID, status, "")
	
	switch status {
	case EnergyOrderStatusCompleted:
		// 订单完成，检查地址匹配
		if pendingOrder.TargetAddress != targetAddress {
			glog.Warningf(ctx, "%s address mismatch: order target %s != current target %s", 
				logPrefix, pendingOrder.TargetAddress, targetAddress)
			return false, gerror.Newf("%s address mismatch, skip execution", logPrefix)
		}
		
		// 清理订单并继续执行
		em.RemoveEnergyOrder(ctx, pendingOrder.OrderID)
		glog.Infof(ctx, "%s order %s completed, continuing with transfer", logPrefix, pendingOrder.OrderID)
		return true, nil
		
	case EnergyOrderStatusFailed:
		// 订单失败，清理并返回错误
		em.RemoveEnergyOrder(ctx, pendingOrder.OrderID)
		return false, gerror.Newf("%s energy order %s failed", logPrefix, pendingOrder.OrderID)
		
	case EnergyOrderStatusExpired:
		// 订单过期，清理并返回错误
		em.RemoveEnergyOrder(ctx, pendingOrder.OrderID)
		return false, gerror.Newf("%s energy order %s expired", logPrefix, pendingOrder.OrderID)
		
	default:
		// 仍在处理中，等待下次检查
		glog.Infof(ctx, "%s order %s still pending, waiting for completion", logPrefix, pendingOrder.OrderID)
		return false, gerror.Newf("%s energy order %s still processing", logPrefix, pendingOrder.OrderID)
	}
}

// getCurrentPoolEnergy gets the current energy of the pool address
func (em *EnergyManager) getCurrentPoolEnergy(ctx context.Context, poolAddress string) (int64, error) {
	// 从现有的TRON RPC配置获取节点信息
	grpcNodeURL := g.Cfg().MustGet(ctx, "withdrawalProcessor.rpc.TRON.url").String()
	grpcApiKey := g.Cfg().MustGet(ctx, "withdrawalProcessor.rpc.TRON.apiKey").String()
	
	if grpcNodeURL == "" || grpcApiKey == "" {
		return 0, gerror.New("TRON RPC configuration incomplete")
	}
	
	// 调用现有的TRON能量查询逻辑
	energy, err := tron.GetAccountEnergy(ctx, poolAddress, grpcNodeURL, grpcApiKey)
	if err != nil {
		return 0, gerror.Wrapf(err, "failed to get account energy for %s", poolAddress)
	}
	
	return energy, nil
}

// getTRC20USDTBalance gets the TRC20 USDT balance for an address
// This is a helper method that should use the existing TRON balance checking logic
func (em *EnergyManager) getTRC20USDTBalance(ctx context.Context, address string) (decimal.Decimal, error) {
	// 从现有的代币合约配置获取TRC20 USDT合约地址
	contractAddress := g.Cfg().MustGet(ctx, "withdrawalProcessor.tokenContracts.USDT_TRC20").String()
	
	if contractAddress == "" {
		return decimal.Zero, gerror.New("USDT_TRC20 contract address not configured")
	}
	
	// 调用现有的TRC20余额查询逻辑
	balance, err := tron.GetTRC20UsdtBalance(address, contractAddress)
	if err != nil {
		return decimal.Zero, gerror.Wrapf(err, "failed to get TRC20 USDT balance for %s", address)
	}
	
	// 将字符串转换为decimal
	balanceDecimal, err := decimal.NewFromString(balance)
	if err != nil {
		return decimal.Zero, gerror.Wrapf(err, "failed to parse balance string: %s", balance)
	}
	
	return balanceDecimal, nil
}