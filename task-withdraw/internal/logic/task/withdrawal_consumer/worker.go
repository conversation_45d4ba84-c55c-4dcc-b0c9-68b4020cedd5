package withdrawal_consumer

import (
	"context"
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/os/gcache"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/shopspring/decimal"

	// "github.com/a19ba14d/tg-bot-common/consts" // Removed unused import

	"task-withdraw/internal/config"
	wp "task-withdraw/internal/logic/task/withdrawal_processor"
	"task-withdraw/internal/logic/task/withdrawal_processor/sender/eth"
	"task-withdraw/internal/logic/task/withdrawal_processor/sender/tron"
	"task-withdraw/internal/service"
)

// Global variable to track if a worker is currently processing a task
var isProcessingTask int32

// getMaxFeeTRXWithFallback safely retrieves the TRX max fee configuration with graceful fallback
func getMaxFeeTRXWithFallback(ctx context.Context, logPrefix string) decimal.Decimal {
	defaultFee, _ := decimal.NewFromString("100") // 100 TRX default
	
	// Try to get configuration from config manager
	maxFeeMap, err := config.GetMap(ctx, "auto_withdrawal_setting.max_fee")
	if err != nil {
		glog.Warningf(ctx, "%s Failed to get max_fee from config manager, using default 100 TRX: %v", logPrefix, err)
		return defaultFee
	}
	
	if maxFeeMap == nil {
		glog.Warningf(ctx, "%s max_fee config is nil, using default 100 TRX", logPrefix)
		return defaultFee
	}
	
	if maxFeeMap["TRX"] == nil {
		glog.Infof(ctx, "%s TRX max_fee not configured, using default 100 TRX", logPrefix)
		return defaultFee
	}
	
	// Type-safe conversion
	var maxFeeTrxStr string
	switch v := maxFeeMap["TRX"].(type) {
	case string:
		maxFeeTrxStr = v
	case float64:
		maxFeeTrxStr = fmt.Sprintf("%.6f", v)
	case int:
		maxFeeTrxStr = fmt.Sprintf("%d", v)
	case int64:
		maxFeeTrxStr = fmt.Sprintf("%d", v)
	default:
		glog.Warningf(ctx, "%s Unexpected type for TRX max fee: %T (value: %v), using default 100 TRX", 
			logPrefix, v, v)
		return defaultFee
	}
	
	// Parse and validate
	maxFeeTrxDecimal, err := decimal.NewFromString(maxFeeTrxStr)
	if err != nil {
		glog.Warningf(ctx, "%s Failed to parse max fee TRX '%s': %v. Using default 100 TRX.", 
			logPrefix, maxFeeTrxStr, err)
		return defaultFee
	}
	
	// Range validation
	if maxFeeTrxDecimal.LessThanOrEqual(decimal.Zero) {
		glog.Warningf(ctx, "%s Invalid max fee TRX: %s (must be positive). Using default 100 TRX.", 
			logPrefix, maxFeeTrxStr)
		return defaultFee
	}
	
	if maxFeeTrxDecimal.GreaterThan(decimal.NewFromInt(10000)) {
		glog.Warningf(ctx, "%s Max fee TRX too high: %s (max allowed: 10000). Using default 100 TRX.", 
			logPrefix, maxFeeTrxStr)
		return defaultFee
	}
	
	glog.Debugf(ctx, "%s Using TRX max fee from config: %s TRX", logPrefix, maxFeeTrxDecimal.String())
	return maxFeeTrxDecimal
}

// StartConsumers initializes and starts the withdrawal consumer workers.
// It now requires both ConsumerConfig and the shared WithdrawalConfig from the processor.
func StartConsumers(ctx context.Context, consumerCfg ConsumerConfig, processorCfg *wp.WithdrawalConfig) {
	logPrefix := "[WithdrawalConsumer]"
	if !consumerCfg.Enabled {
		glog.Infof(ctx, "%s Consumer is disabled in configuration.", logPrefix)
		return
	}

	queueName := consumerCfg.RedisQueueName
	if queueName == "" {
		queueName = DefaultQueueName
		glog.Warningf(ctx, "%s Redis queue name not configured, using default: %s", logPrefix, queueName)
	}

	dlqName := consumerCfg.DlqName
	if dlqName == "" {
		dlqName = DefaultDlqName
		glog.Warningf(ctx, "%s Dead-letter queue name not configured, using default: %s", logPrefix, dlqName)
	}

	concurrency := consumerCfg.Concurrency
	if concurrency <= 0 {
		concurrency = DefaultConcurrency
	}

	glog.Infof(ctx, "%s Starting %d consumer worker(s) for queue: %s", logPrefix, concurrency, queueName)

	var wg sync.WaitGroup
	for i := 0; i < concurrency; i++ {
		wg.Add(1)
		// Create a separate context for each worker to manage its lifecycle if needed,
		// but for now, pass the main context directly so it gets cancelled by the signal handler.
		// workerCtx := context.Background() // OLD: This context was independent and wouldn't be cancelled.
		workerCtx := ctx                                                               // NEW: Pass the cancellable context from the caller.
		go RunWorker(workerCtx, i, queueName, dlqName, &wg, consumerCfg, processorCfg) // Pass both configs to worker
	}

	// Wait for context cancellation from the main application context to initiate shutdown
	<-ctx.Done()
	glog.Infof(ctx, "%s Shutdown signal received. Waiting for workers to finish...", logPrefix)
	// Cancel worker contexts if derived? (Depends on strategy)
	// Wait for all workers to complete
	wg.Wait()
	glog.Infof(ctx, "%s All consumer workers stopped.", logPrefix)
}

// RunWorker is the main loop for a single consumer worker goroutine.
// It now receives both consumerCfg and processorCfg.
func RunWorker(ctx context.Context, workerID int, queueName string, dlqName string, wg *sync.WaitGroup, consumerCfg ConsumerConfig, processorCfg *wp.WithdrawalConfig) {
	defer wg.Done()
	logPrefix := fmt.Sprintf("[Worker-%d]", workerID)
	glog.Infof(ctx, "%s Starting worker for queue: %s", logPrefix, queueName)

	redisClient := service.Redis().Client()

	// Initialize dependencies that can be shared across message processing for this worker
	workerCache := gcache.New()
	// Initialize MemoryRateLimiter with single transaction limits from config manager
	var singleLimits map[string]string
	// Get max single amount from config manager instead of processorCfg
	maxSingleAmountMap, err := config.GetMap(ctx, "auto_withdrawal_setting.max_single_amount")
	if err != nil {
		glog.Errorf(ctx, "%s Failed to get max_single_amount from config manager: %v", logPrefix, err)
		panic(fmt.Sprintf("Critical configuration error: Failed to get max_single_amount from config manager: %v", err))
	}

	if maxSingleAmountMap == nil {
		glog.Errorf(ctx, "%s max_single_amount config is nil", logPrefix)
		panic("Critical configuration error: max_single_amount config is nil")
	}

	// Convert map[string]interface{} to map[string]string
	singleLimits = make(map[string]string)
	for k, v := range maxSingleAmountMap {
		if strVal, ok := v.(string); ok {
			singleLimits[k] = strVal
		} else {
			// Try to convert to string
			singleLimits[k] = fmt.Sprintf("%v", v)
		}
	}
	glog.Debugf(ctx, "%s Loaded MaxSingleAmount limits from config manager: %v", logPrefix, singleLimits)
	memoryLimiter := wp.NewMemoryRateLimiter(workerCache, singleLimits) // Pass the map
	glog.Infof(ctx, "%s Initialized MemoryRateLimiter for worker.", logPrefix)

	// Senders should be initialized here once per worker if possible,
	// especially if they maintain connections (like ETH client).
	var ethSender *eth.EthSender
	var tronSender *tron.TronSender
	var initErr error

	// Initialize ETH Sender if configured
	// Access Rpc, Wallets, TxParams from processorCfg
	if processorCfg != nil { // Check if processorCfg is not nil before accessing its fields
		if ethRpcCfgData, ok := processorCfg.Rpc["ETH"]; ok {
			if ethWalletCfgData, okW := processorCfg.Wallets["ETH"]; okW {
				// Get max fee from config manager
				maxFeeMap, err := config.GetMap(ctx, "auto_withdrawal_setting.max_fee")
				if err != nil {
					glog.Errorf(ctx, "%s Failed to get max_fee from config manager: %v", logPrefix, err)
					panic(fmt.Sprintf("Critical configuration error: Failed to get max_fee from config manager: %v", err))
				}

				if maxFeeMap == nil {
					glog.Errorf(ctx, "%s max_fee config is nil", logPrefix)
					panic("Critical configuration error: max_fee config is nil")
				}

				maxFeeEth := "0.01" // Default value
				if maxFeeMap["ETH"] != nil {
					maxFeeEth = fmt.Sprintf("%v", maxFeeMap["ETH"])
				}

				// Create specific config structs and copy fields
				ethRpcCfg := eth.RpcConfigEth{
					Url:     ethRpcCfgData.Url,
					ChainId: ethRpcCfgData.ChainId,
				}
				ethWalletCfg := eth.WalletConfigEth{
					PrivateKey:       ethWalletCfgData.PrivateKey,
					PrivateKeyEnvVar: ethWalletCfgData.PrivateKeyEnvVar,
					Address:          ethWalletCfgData.Address,
				}
				ethTxParamCfg := eth.TxParamConfigEth{
					MaxFeeEth: maxFeeEth,
				}
				ethSender, initErr = eth.NewEthSender(ctx, ethRpcCfg, ethWalletCfg, ethTxParamCfg, nil)
				if initErr != nil {
					glog.Errorf(ctx, "%s Failed to initialize ETH Sender: %v. ETH withdrawals will fail for this worker.", logPrefix, initErr)
					// Worker might still be able to process TRON, so don't exit immediately.
				} else {
					defer ethSender.Close() // Ensure connection is closed when worker stops
					glog.Infof(ctx, "%s ETH Sender initialized.", logPrefix)
				}
			} else {
				glog.Warningf(ctx, "%s ETH Wallet config missing. ETH Sender not initialized.", logPrefix)
			}
		} else {
			glog.Infof(ctx, "%s ETH RPC config missing. ETH Sender not initialized.", logPrefix)
		}
	} // End of if processorCfg != nil for ETH Sender

	// Initialize TRON Sender if configured
	if processorCfg != nil { // Check if processorCfg is not nil
		if tronRpcCfgData, ok := processorCfg.Rpc["TRON"]; ok {
			if tronWalletCfgData, okW := processorCfg.Wallets["TRON"]; okW {
				// Get max fee with graceful fallback
				maxFeeTrxDecimal := getMaxFeeTRXWithFallback(ctx, logPrefix)
				
				// Convert TRX to Sun with precision protection
				sunMultiplier := decimal.NewFromInt(1000000)
				feeLimitSunDecimal := maxFeeTrxDecimal.Mul(sunMultiplier)
				feeLimitSun := feeLimitSunDecimal.IntPart()
				
				// Verify precision loss is acceptable
				if !feeLimitSunDecimal.Equal(decimal.NewFromInt(feeLimitSun)) {
					remainderSun := feeLimitSunDecimal.Sub(decimal.NewFromInt(feeLimitSun))
					if remainderSun.GreaterThan(decimal.NewFromFloat(0.1)) {
						glog.Warningf(ctx, "%s Precision loss in fee conversion: %s TRX -> %d Sun (lost %s Sun)", 
							logPrefix, maxFeeTrxDecimal.String(), feeLimitSun, remainderSun.String())
					}
				}

				// Create specific config structs and copy fields
				tronRpcCfg := tron.RpcConfigTron{
					Url:    tronRpcCfgData.Url,
					ApiKey: tronRpcCfgData.ApiKey,
				}
				tronWalletCfg := tron.WalletConfigTron{
					PrivateKey: tronWalletCfgData.PrivateKey, PrivateKeyEnvVar: tronWalletCfgData.PrivateKeyEnvVar, Address: tronWalletCfgData.Address,
				}
				tronTxParamCfg := tron.TxParamConfigTron{
					FeeLimitSun: feeLimitSun,
				}
				tronSender, initErr = tron.NewTronSender(ctx, tronRpcCfg, tronWalletCfg, tronTxParamCfg)
				if initErr != nil {
					glog.Errorf(ctx, "%s Failed to initialize TRON Sender: %v. TRON withdrawals will fail for this worker.", logPrefix, initErr)
				} else {
					defer tronSender.Close()
					glog.Infof(ctx, "%s TRON Sender initialized.", logPrefix)
				}
			} else {
				glog.Warningf(ctx, "%s TRON Wallet config missing. TRON Sender not initialized.", logPrefix)
			}
		} else { // This else corresponds to the 'if tronRpcCfgData, ok' on L145
			glog.Infof(ctx, "%s TRON RPC config missing. TRON Sender not initialized.", logPrefix) // Log only if RPC config is missing
		}
	} // End of if processorCfg != nil for TRON Sender

	for {
		select {
		case <-ctx.Done():
			glog.Infof(ctx, "%s Stopping worker due to context cancellation.", logPrefix)
			return
		default:
			// Use BRPOP with a timeout
			// BRPop timeout expects seconds as int64.
			// It returns gvar.Vars which is []*gvar.Var, typically [queueNameVar, valueVar]
			glog.Debugf(ctx, "%s Waiting for message from queue '%s' (timeout: %v)...", logPrefix, queueName, BrpopTimeout)
			resultVar, err := redisClient.BRPop(ctx, int64(BrpopTimeout/time.Second), queueName)
			// Log the raw result and error immediately after BRPop returns
			glog.Debugf(ctx, "%s BRPop returned: resultVar=%+v, err=%v", logPrefix, resultVar, err)
			if err != nil {
				// Check for context cancellation error vs other Redis errors
				if gerror.Is(err, context.Canceled) || gerror.Is(err, context.DeadlineExceeded) {
					// Expected error during shutdown or timeout, continue loop to check ctx.Done()
					continue
				}
				// Log other Redis errors
				glog.Errorf(ctx, "%s Error during BRPOP from queue '%s': %v. Retrying after delay...", logPrefix, queueName, err)
				time.Sleep(5 * time.Second) // Avoid busy-looping on persistent Redis errors
				continue
			}

			// Check if BRPop returned a valid result [queueName, value]
			if resultVar == nil || len(resultVar) != 2 || resultVar[1] == nil {
				// Timeout occurred or invalid format, continue loop to check ctx.Done()
				if resultVar != nil && len(resultVar) != 2 {
					glog.Warningf(ctx, "%s Invalid result format from BRPop: expected 2 elements, got %d.", logPrefix, len(resultVar))
				}
				continue // Skip invalid format or timeout
			}

			// Check if another task is currently being processed
			if !atomic.CompareAndSwapInt32(&isProcessingTask, 0, 1) {
				// Another task is currently being processed, put the message back to queue
				message := resultVar[1].String()
				redisClient.LPush(ctx, queueName, message)
				glog.Debugf(ctx, "%s Another task is currently being processed, message returned to queue.", logPrefix)
				time.Sleep(1 * time.Second) // Wait a bit before next attempt
				continue
			}

			// Log the raw message content before processing
			message := resultVar[1].String() // The actual message is the second element's string value
			glog.Debugf(ctx, "%s Raw message content: %s", logPrefix, message)
			glog.Debugf(ctx, "%s Received message from queue '%s'. Starting serial processing.", logPrefix, queueName)
			
			// Process message with defer to ensure flag is reset
			func() {
				defer func() {
					// Reset the processing flag when done
					atomic.StoreInt32(&isProcessingTask, 0)
					glog.Debugf(ctx, "%s Message processing completed, ready for next task.", logPrefix)
				}()
				
				// Pass initialized dependencies and processorCfg to processMessage
				ProcessMessage(ctx, logPrefix, message, dlqName, processorCfg, memoryLimiter, ethSender, tronSender)
			}()
		}
	}
}
