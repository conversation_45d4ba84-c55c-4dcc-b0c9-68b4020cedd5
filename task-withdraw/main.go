package main

import (
	"context"                        // 保留 context 包导入
	"task-withdraw/internal/cmd"     // 恢复导入 cmd 包以访问 Main
	_ "task-withdraw/internal/logic" // Import logic package to trigger service registration scanning

	// "github.com/gogf/gf/v2/os/gcmd" // 移除 gcmd 包导入
	// "github.com/gogf/gf/v2/os/gctx" // 确认 gctx 导入已移除

	_ "github.com/gogf/gf/contrib/drivers/mysql/v2"
	_ "github.com/gogf/gf/contrib/nosql/redis/v2" // Add Redis adapter import
)

func main() {
	cmd.Main.AddCommand(&cmd.Task, cmd.RunTask)
	// 运行主命令，它会自动处理子命令的解析和执行
	cmd.Main.Run(context.Background())
}
