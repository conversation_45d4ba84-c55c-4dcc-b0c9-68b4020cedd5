grpc:
  client:
    user-service:
      endpoints:
        - "localhost:50051" # 替换成目标服务的实际地址和端口
      apiKeys: "01JT41XVG16HNHZ2FCEJECT7AH"
      balancer: "round_robin"
      timeout: 5000

redis:
  default:
    address: "127.0.0.1:6379" # 确认或修改为您的 Redis 地址
    db: 3 # 确认或修改 DB 编号
    pass: "valkey_password" # 确认或修改密码
    idleTimeout: 20s # 补充单位

logger:
  path: "logs" # 统一日志目录，各应用可分子目录
  level: "all"
  stdout: true
  rotateSize: "100M"
  rotateExpire: "7d"
  format: "json" # 使用 JSON 格式方便收集

withdrawalProcessor:
  enabled: true       # 启用提现处理任务 (Fetcher)
  spec: "*/10 * * * * *" # Fetcher 运行频率 (例如每 10秒)
  batchSize: 50         # Fetcher 每次获取数量
  
  # TRC20能量管理配置
  trc20EnergyManagement:
    enabled: true                    # 启用TRC20能量管理
    bypassCheck: false               # TEMPORARY: 绕过能量检查（RPC连接问题）
    energyReservePercent: 10.0       # 能量储备百分比 (10%)
    orderTimeoutHours: 24            # 订单超时时间(小时)
    maxRetryCount: 3                 # 最大重试次数
    checkIntervalSeconds: 30         # 订单状态检查间隔(秒)
    
    # iTRX API配置
    itrx:
      apiKey: "A44137431E0D402AB441DB0E06B5D257"           # iTRX API密钥
      apiSecret: "997EBBDC83C0161B8902F2CC1685DCA4F7F7B562118CC186599EC6834FFF4AAD"     # iTRX API密钥
      apiBaseUrl: "https://itrx.io"         # iTRX API基础URL
      energyPeriod: "1H"                    # 能量租用时长 (1H/3H/1D等)
  # --- Processing Logic Configuration (Moved from withdrawalConsumer) ---
  enabledTokens: # 控制哪些代币的提现是启用的 (e.g., USDT_ERC20, USDT_TRC20)
    ETH: true
    USDT_ERC20: true
    TRX: true
    USDT_TRC20: true
  tokenContracts: # 代币合约地址配置
    USDT_ERC20: "******************************************"
    USDT_TRC20: "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"
  tokenPrecisions: # 链上交易精度配置
    ETH: 18
    USDT_ERC20: 6
    TRX: 6
    USDT_TRC20: 6
  wallets:
    ETH:
      privateKey: "e9568f7d468f88a306e91bc549136416b32dcfe07dff2aa6ef652f0f70db8320" # 留空，优先使用环境变量
      privateKeyEnvVar: "WITHDRAW_HOT_ETH_PK"
      address: "******************************************"
    TRON:
      privateKey: "e762b5c4ec9b1c5e347f560e845d86b217c0ce80b74ce0d7d2d7b89524b43bc3" # 留空，优先使用环境变量
      privateKeyEnvVar: "WITHDRAW_HOT_TRON_PK"
      address: "TRfDoj9v6vv6QixPzoDf69LqoyrkTaxyMQ"
  rpc:
    ETH:
      url: "https://blockchain.googleapis.com/v1/projects/kinetic-harbor-460313-n6/locations/us-central1/endpoints/ethereum-mainnet/rpc?key=AIzaSyDsLLHG_zaqWLMXXot-fKAzstGYBUwDzsM"
      chainId: 1
    TRON:
      url: "cool-responsive-brook.tron-mainnet.quiknode.pro:50051"
      apiKey: "2279113c33d66b6b0cc5fe2d9970c47b51b27f3e"
      callTimeoutSeconds: 15
  retry:
    maxAttempts: 30
    nonRetryableErrors:
      - "Insufficient hot wallet balance"
      - "Invalid recipient address format"
      - "exceeds single limit"

# --- Withdrawal Consumer Worker Configuration ---
withdrawalConsumer:
  enabled: true         # 启用 Consumer Worker
  redisQueueName: "queue:withdrawal_processing" # 消费队列名称
  concurrency: 1        # 启动 1 个串行 Worker
  dlqName: "queue:withdrawal_processing_dlq" # 死信队列名称
  # Note: Processing logic configuration moved to withdrawalProcessor

# --- gRPC Status Updater Worker Configuration ---
grpcUpdater:
  redisQueueName: "queue:withdrawal_status_update" # 主处理队列名称
  redisDlqName: "queue:withdrawal_status_update_dlq" # 死信队列名称
  brpopTimeoutSeconds: 5 # Redis BRPop 阻塞超时时间 (秒)
  maxRetries: 5 # gRPC 调用最大重试次数
  retryDelaySeconds: 10 # gRPC 调用重试间隔 (秒)
  
# Consul 配置（用于配置同步）
consul:
  address: "127.0.0.1:8500" # Consul 服务器地址
  token: "af8c827b-0bfd-f3cd-f276-c2b7f4e6e874" # ACL Token（生产环境请修改）
  config_prefix: "xpay/config" # 配置存储前缀