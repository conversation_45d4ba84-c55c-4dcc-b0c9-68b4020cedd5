syntax = "proto3";

package task.v1; // 使用版本化的包名

import "google/protobuf/timestamp.proto"; // 用于时间戳
// 移除: import "google/protobuf/any.proto";
import "google/protobuf/empty.proto";     // 添加: 用于无数据成功响应

// 指定 Go 包路径和别名，将在 task-server/api/task/v1 下生成代码
option go_package = "task-server/api/task/v1;taskv1";

// 提币记录消息 (映射 entity.UserWithdraws)
// 注意: WithdrawalStatus 枚举已移除，状态使用 int32 state 字段
message Withdrawal {
  int64 user_withdraws_id = 1; // uint -> int64
  int64 user_id = 2;           // uint64 -> int64
  int64 token_id = 3;          // uint -> int64
  string wallet_id = 4;
  string name = 5;
  string chan = 6;
  string order_no = 7;
  string address = 8;
  string recipient_name = 9;    // 法币字段，保留
  string recipient_account = 10; // 法币字段，保留
  double amount = 11;
  double handling_fee = 12;
  double actual_amount = 13;
  int32 state = 14; // 数据库中的 state 字段 (int 类型)
  string refuse_reason_zh = 15;
  string refuse_reason_en = 16;
  string tx_hash = 17;
  string error_message = 18; // 注意：DB 中可能是 JSON 字符串
  string user_remark = 19;
  string admin_remark = 20;
  google.protobuf.Timestamp created_at = 21;
  google.protobuf.Timestamp checked_at = 22;
  google.protobuf.Timestamp processing_at = 23;
  google.protobuf.Timestamp completed_at = 24;
  google.protobuf.Timestamp updated_at = 25;
  int32 retries = 26;
  int32 nergy_state = 27;
}

// ListWithdrawals 请求
message ListWithdrawalsRequest {
  int32 page_size = 1; // 每页数量 (建议 > 0)
  int32 page = 2;      // 页码 (建议 >= 1)
  // 可选过滤条件
  int32 filter_state = 3; // 按状态过滤 (0 表示不过滤)
  int64 filter_user_id = 4;           // 按用户ID过滤 (0 表示不过滤)
  string filter_order_no = 5;         // 按订单号过滤 (空字符串表示不过滤)
  // 可以添加更多过滤，如 token_id, address, 时间范围等
}

// ListWithdrawals 响应
message ListWithdrawalsResponse {
  repeated Withdrawal withdrawals = 1; // 提币记录列表
  int32 total_count = 2;             // 总记录数
  int32 current_page = 3;            // 当前页码
  int32 total_pages = 4;             // 总页数
}

// UpdateWithdrawalStatus 请求
message UpdateWithdrawalStatusRequest {
  int64 withdrawal_id = 1;               // 提币记录ID
  int32 state = 2;           // 新状态
  string error_message = 3;              // 可选：错误信息（JSON字符串）
  string refuse_reason_zh = 4;           // 可选：拒绝原因（中文）
  string refuse_reason_en = 5;           // 可选：拒绝原因（英文）
  string tx_hash = 6;                    // 可选：交易哈希
  int32 retries = 7;                     // 可选：重试次数
  string admin_remark = 8;               // 可选：管理员备注
}

// 通用 API 响应结构 (使用 oneof)
message ApiResponse {
  int32 code = 1;       // 业务状态码
  string message = 2;   // 响应消息
  oneof data_payload { // 定义 oneof 字段
    ListWithdrawalsResponse list_withdrawals_data = 3; // 用于 ListWithdrawals 成功响应
    google.protobuf.Empty success_no_data = 4;       // 用于 UpdateWithdrawalStatus 等无数据成功响应
  }
}

// TaskService 服务定义
service TaskService {
  // 获取提币记录列表 (带过滤和分页)
  // 返回通用响应结构，data 字段包含 ListWithdrawalsResponse
  rpc ListWithdrawals(ListWithdrawalsRequest) returns (ApiResponse);

  // 更新提币记录状态和字段
  // 返回通用响应结构，data 字段包含更新后的 Withdrawal (如果成功且需要返回) 或为空
  rpc UpdateWithdrawalStatus(UpdateWithdrawalStatusRequest) returns (ApiResponse);
}