version: '3.8'

services:
  task-withdraw-app:
    image: task-withdraw-app:latest
    build: . # Tells Compose to build the image using the Dockerfile in the current directory
    restart: unless-stopped
    volumes:
      # Mount config read-only
      - ./config_variables.json:/app/config_variables.json:ro
      # Mount logs directory
      - ./logs:/home/<USER>
    environment:
      # Pass the path to the config file inside the container
      - PATH_TO_SECRET_FILE=/app/config_variables.json
    networks:
      - xpay_app-network

networks:
  xpay_app-network:
    external: true