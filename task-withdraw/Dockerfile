# === Builder Stage ===
FROM golang:1.24-alpine AS builder

# Set GO111MODULE=on, disable <PERSON><PERSON><PERSON> to avoid potential cross-compilation issues with gcc in Alpine
ENV GO111MODULE=on \
    CGO_ENABLED=0

# Install build dependencies (gcc/libc-dev might not be strictly needed if <PERSON><PERSON><PERSON> is disabled, but keep make/git)
RUN apk update && apk add make git

# Set timezone
ENV TZ=Asia/Shanghai
RUN apk --no-cache add tzdata && \
    ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && \
    echo $TZ > /etc/timezone

WORKDIR /build
COPY . .

ENV GOPROXY=https://goproxy.cn,direct
RUN go mod download
RUN go mod tidy

# Directly use go build, disable C<PERSON><PERSON>, and specify target OS/ARCH
# Output directly to /build/bin
RUN mkdir -p /build/bin
RUN GOOS=linux GOARCH=amd64 go build -o /build/bin/task_withdraw main.go


# === Final Stage ===
FROM alpine:latest
WORKDIR /home

# Copy binary from builder stage
COPY --from=builder /build/bin/task_withdraw /home/<USER>
RUN chmod +x /home/<USER>

# Copy entrypoint script and configuration template from builder stage
# These files are directly in /build in the builder stage
# Copy entrypoint script and configuration template directly from build context
COPY entrypoint.sh /home/<USER>
COPY manifest/config/config.yaml.template /home/<USER>/config/config.yaml.template

# Install runtime dependencies (keep bash, jq, gettext, tzdata)
RUN apk update && apk add --no-cache bash jq gettext tzdata && \
    chmod 500 /home/<USER>
    rm -rf /var/cache/apk/* # Clean apk cache

# Set the entrypoint script
ENTRYPOINT [ "/home/<USER>" ]

# Default command to run the task_withdraw application
CMD ["/home/<USER>", "task"]

