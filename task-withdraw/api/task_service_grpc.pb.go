// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v3.21.12
// source: task_service.proto

package taskv1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	TaskService_ListWithdrawals_FullMethodName        = "/task.v1.TaskService/ListWithdrawals"
	TaskService_UpdateWithdrawalStatus_FullMethodName = "/task.v1.TaskService/UpdateWithdrawalStatus"
)

// TaskServiceClient is the client API for TaskService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// TaskService 服务定义
type TaskServiceClient interface {
	// 获取提币记录列表 (带过滤和分页)
	// 返回通用响应结构，data 字段包含 ListWithdrawalsResponse
	ListWithdrawals(ctx context.Context, in *ListWithdrawalsRequest, opts ...grpc.CallOption) (*ApiResponse, error)
	// 更新提币记录状态和字段
	// 返回通用响应结构，data 字段包含更新后的 Withdrawal (如果成功且需要返回) 或为空
	UpdateWithdrawalStatus(ctx context.Context, in *UpdateWithdrawalStatusRequest, opts ...grpc.CallOption) (*ApiResponse, error)
}

type taskServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTaskServiceClient(cc grpc.ClientConnInterface) TaskServiceClient {
	return &taskServiceClient{cc}
}

func (c *taskServiceClient) ListWithdrawals(ctx context.Context, in *ListWithdrawalsRequest, opts ...grpc.CallOption) (*ApiResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ApiResponse)
	err := c.cc.Invoke(ctx, TaskService_ListWithdrawals_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *taskServiceClient) UpdateWithdrawalStatus(ctx context.Context, in *UpdateWithdrawalStatusRequest, opts ...grpc.CallOption) (*ApiResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ApiResponse)
	err := c.cc.Invoke(ctx, TaskService_UpdateWithdrawalStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TaskServiceServer is the server API for TaskService service.
// All implementations must embed UnimplementedTaskServiceServer
// for forward compatibility.
//
// TaskService 服务定义
type TaskServiceServer interface {
	// 获取提币记录列表 (带过滤和分页)
	// 返回通用响应结构，data 字段包含 ListWithdrawalsResponse
	ListWithdrawals(context.Context, *ListWithdrawalsRequest) (*ApiResponse, error)
	// 更新提币记录状态和字段
	// 返回通用响应结构，data 字段包含更新后的 Withdrawal (如果成功且需要返回) 或为空
	UpdateWithdrawalStatus(context.Context, *UpdateWithdrawalStatusRequest) (*ApiResponse, error)
	mustEmbedUnimplementedTaskServiceServer()
}

// UnimplementedTaskServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedTaskServiceServer struct{}

func (UnimplementedTaskServiceServer) ListWithdrawals(context.Context, *ListWithdrawalsRequest) (*ApiResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListWithdrawals not implemented")
}
func (UnimplementedTaskServiceServer) UpdateWithdrawalStatus(context.Context, *UpdateWithdrawalStatusRequest) (*ApiResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateWithdrawalStatus not implemented")
}
func (UnimplementedTaskServiceServer) mustEmbedUnimplementedTaskServiceServer() {}
func (UnimplementedTaskServiceServer) testEmbeddedByValue()                     {}

// UnsafeTaskServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TaskServiceServer will
// result in compilation errors.
type UnsafeTaskServiceServer interface {
	mustEmbedUnimplementedTaskServiceServer()
}

func RegisterTaskServiceServer(s grpc.ServiceRegistrar, srv TaskServiceServer) {
	// If the following call pancis, it indicates UnimplementedTaskServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&TaskService_ServiceDesc, srv)
}

func _TaskService_ListWithdrawals_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListWithdrawalsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TaskServiceServer).ListWithdrawals(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TaskService_ListWithdrawals_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TaskServiceServer).ListWithdrawals(ctx, req.(*ListWithdrawalsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TaskService_UpdateWithdrawalStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateWithdrawalStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TaskServiceServer).UpdateWithdrawalStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TaskService_UpdateWithdrawalStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TaskServiceServer).UpdateWithdrawalStatus(ctx, req.(*UpdateWithdrawalStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// TaskService_ServiceDesc is the grpc.ServiceDesc for TaskService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TaskService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "task.v1.TaskService",
	HandlerType: (*TaskServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListWithdrawals",
			Handler:    _TaskService_ListWithdrawals_Handler,
		},
		{
			MethodName: "UpdateWithdrawalStatus",
			Handler:    _TaskService_UpdateWithdrawalStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "task_service.proto",
}
