// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: task_service.proto

package taskv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 提币记录消息 (映射 entity.UserWithdraws)
// 注意: WithdrawalStatus 枚举已移除，状态使用三个独立的 int32 状态字段
type Withdrawal struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	UserWithdrawsId        int64                  `protobuf:"varint,1,opt,name=user_withdraws_id,json=userWithdrawsId,proto3" json:"user_withdraws_id,omitempty"` // uint -> int64
	UserId                 int64                  `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`                              // uint64 -> int64
	TokenId                int64                  `protobuf:"varint,3,opt,name=token_id,json=tokenId,proto3" json:"token_id,omitempty"`                           // uint -> int64
	WalletId               string                 `protobuf:"bytes,4,opt,name=wallet_id,json=walletId,proto3" json:"wallet_id,omitempty"`
	Name                   string                 `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	Chan                   string                 `protobuf:"bytes,6,opt,name=chan,proto3" json:"chan,omitempty"`
	OrderNo                string                 `protobuf:"bytes,7,opt,name=order_no,json=orderNo,proto3" json:"order_no,omitempty"`
	Address                string                 `protobuf:"bytes,8,opt,name=address,proto3" json:"address,omitempty"`
	RecipientName          string                 `protobuf:"bytes,9,opt,name=recipient_name,json=recipientName,proto3" json:"recipient_name,omitempty"`           // 法币字段，保留
	RecipientAccount       string                 `protobuf:"bytes,10,opt,name=recipient_account,json=recipientAccount,proto3" json:"recipient_account,omitempty"` // 法币字段，保留
	Amount                 float64                `protobuf:"fixed64,11,opt,name=amount,proto3" json:"amount,omitempty"`
	HandlingFee            float64                `protobuf:"fixed64,12,opt,name=handling_fee,json=handlingFee,proto3" json:"handling_fee,omitempty"`
	ActualAmount           float64                `protobuf:"fixed64,13,opt,name=actual_amount,json=actualAmount,proto3" json:"actual_amount,omitempty"`
	AuditStatus            int32                  `protobuf:"varint,14,opt,name=audit_status,json=auditStatus,proto3" json:"audit_status,omitempty"`                                    // 审核状态: 1-免审, 2-待审核, 3-审核通过, 4-审核拒绝
	AutoWithdrawalProgress int32                  `protobuf:"varint,15,opt,name=auto_withdrawal_progress,json=autoWithdrawalProgress,proto3" json:"auto_withdrawal_progress,omitempty"` // 自动提现状态 0 未开始 1 进行中 2 成功 3 结束
	ProcessingStatus       int32                  `protobuf:"varint,16,opt,name=processing_status,json=processingStatus,proto3" json:"processing_status,omitempty"`                     // 提现处理状态: 1-自动放币处理中, 2-处理中(待冷钱包转入)，3.待人工转账.，4-成功, 5-失败
	RefuseReasonZh         string                 `protobuf:"bytes,17,opt,name=refuse_reason_zh,json=refuseReasonZh,proto3" json:"refuse_reason_zh,omitempty"`
	RefuseReasonEn         string                 `protobuf:"bytes,18,opt,name=refuse_reason_en,json=refuseReasonEn,proto3" json:"refuse_reason_en,omitempty"`
	TxHash                 string                 `protobuf:"bytes,19,opt,name=tx_hash,json=txHash,proto3" json:"tx_hash,omitempty"`
	ErrorMessage           string                 `protobuf:"bytes,20,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"` // 注意：DB 中可能是 JSON 字符串
	UserRemark             string                 `protobuf:"bytes,21,opt,name=user_remark,json=userRemark,proto3" json:"user_remark,omitempty"`
	AdminRemark            string                 `protobuf:"bytes,22,opt,name=admin_remark,json=adminRemark,proto3" json:"admin_remark,omitempty"`
	CreatedAt              *timestamppb.Timestamp `protobuf:"bytes,23,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	CheckedAt              *timestamppb.Timestamp `protobuf:"bytes,24,opt,name=checked_at,json=checkedAt,proto3" json:"checked_at,omitempty"`
	ProcessingAt           *timestamppb.Timestamp `protobuf:"bytes,25,opt,name=processing_at,json=processingAt,proto3" json:"processing_at,omitempty"`
	CompletedAt            *timestamppb.Timestamp `protobuf:"bytes,26,opt,name=completed_at,json=completedAt,proto3" json:"completed_at,omitempty"`
	UpdatedAt              *timestamppb.Timestamp `protobuf:"bytes,27,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	Retries                int32                  `protobuf:"varint,28,opt,name=retries,proto3" json:"retries,omitempty"`
	NergyState             int32                  `protobuf:"varint,29,opt,name=nergy_state,json=nergyState,proto3" json:"nergy_state,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *Withdrawal) Reset() {
	*x = Withdrawal{}
	mi := &file_task_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Withdrawal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Withdrawal) ProtoMessage() {}

func (x *Withdrawal) ProtoReflect() protoreflect.Message {
	mi := &file_task_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Withdrawal.ProtoReflect.Descriptor instead.
func (*Withdrawal) Descriptor() ([]byte, []int) {
	return file_task_service_proto_rawDescGZIP(), []int{0}
}

func (x *Withdrawal) GetUserWithdrawsId() int64 {
	if x != nil {
		return x.UserWithdrawsId
	}
	return 0
}

func (x *Withdrawal) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *Withdrawal) GetTokenId() int64 {
	if x != nil {
		return x.TokenId
	}
	return 0
}

func (x *Withdrawal) GetWalletId() string {
	if x != nil {
		return x.WalletId
	}
	return ""
}

func (x *Withdrawal) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Withdrawal) GetChan() string {
	if x != nil {
		return x.Chan
	}
	return ""
}

func (x *Withdrawal) GetOrderNo() string {
	if x != nil {
		return x.OrderNo
	}
	return ""
}

func (x *Withdrawal) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *Withdrawal) GetRecipientName() string {
	if x != nil {
		return x.RecipientName
	}
	return ""
}

func (x *Withdrawal) GetRecipientAccount() string {
	if x != nil {
		return x.RecipientAccount
	}
	return ""
}

func (x *Withdrawal) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *Withdrawal) GetHandlingFee() float64 {
	if x != nil {
		return x.HandlingFee
	}
	return 0
}

func (x *Withdrawal) GetActualAmount() float64 {
	if x != nil {
		return x.ActualAmount
	}
	return 0
}

func (x *Withdrawal) GetAuditStatus() int32 {
	if x != nil {
		return x.AuditStatus
	}
	return 0
}

func (x *Withdrawal) GetAutoWithdrawalProgress() int32 {
	if x != nil {
		return x.AutoWithdrawalProgress
	}
	return 0
}

func (x *Withdrawal) GetProcessingStatus() int32 {
	if x != nil {
		return x.ProcessingStatus
	}
	return 0
}

func (x *Withdrawal) GetRefuseReasonZh() string {
	if x != nil {
		return x.RefuseReasonZh
	}
	return ""
}

func (x *Withdrawal) GetRefuseReasonEn() string {
	if x != nil {
		return x.RefuseReasonEn
	}
	return ""
}

func (x *Withdrawal) GetTxHash() string {
	if x != nil {
		return x.TxHash
	}
	return ""
}

func (x *Withdrawal) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *Withdrawal) GetUserRemark() string {
	if x != nil {
		return x.UserRemark
	}
	return ""
}

func (x *Withdrawal) GetAdminRemark() string {
	if x != nil {
		return x.AdminRemark
	}
	return ""
}

func (x *Withdrawal) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Withdrawal) GetCheckedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CheckedAt
	}
	return nil
}

func (x *Withdrawal) GetProcessingAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ProcessingAt
	}
	return nil
}

func (x *Withdrawal) GetCompletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletedAt
	}
	return nil
}

func (x *Withdrawal) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Withdrawal) GetRetries() int32 {
	if x != nil {
		return x.Retries
	}
	return 0
}

func (x *Withdrawal) GetNergyState() int32 {
	if x != nil {
		return x.NergyState
	}
	return 0
}

// ListWithdrawals 请求
type ListWithdrawalsRequest struct {
	state    protoimpl.MessageState `protogen:"open.v1"`
	PageSize int32                  `protobuf:"varint,1,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"` // 每页数量 (建议 > 0)
	Page     int32                  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`                         // 页码 (建议 >= 1)
	// 可选过滤条件
	FilterAuditStatus      int32  `protobuf:"varint,3,opt,name=filter_audit_status,json=filterAuditStatus,proto3" json:"filter_audit_status,omitempty"`                // 按审核状态过滤 (0 表示不过滤)
	FilterProcessingStatus int32  `protobuf:"varint,4,opt,name=filter_processing_status,json=filterProcessingStatus,proto3" json:"filter_processing_status,omitempty"` // 按处理状态过滤 (0 表示不过滤)
	FilterUserId           int64  `protobuf:"varint,5,opt,name=filter_user_id,json=filterUserId,proto3" json:"filter_user_id,omitempty"`                               // 按用户ID过滤 (0 表示不过滤)
	FilterOrderNo          string `protobuf:"bytes,6,opt,name=filter_order_no,json=filterOrderNo,proto3" json:"filter_order_no,omitempty"`                             // 按订单号过滤 (空字符串表示不过滤)
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *ListWithdrawalsRequest) Reset() {
	*x = ListWithdrawalsRequest{}
	mi := &file_task_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListWithdrawalsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWithdrawalsRequest) ProtoMessage() {}

func (x *ListWithdrawalsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_task_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWithdrawalsRequest.ProtoReflect.Descriptor instead.
func (*ListWithdrawalsRequest) Descriptor() ([]byte, []int) {
	return file_task_service_proto_rawDescGZIP(), []int{1}
}

func (x *ListWithdrawalsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListWithdrawalsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListWithdrawalsRequest) GetFilterAuditStatus() int32 {
	if x != nil {
		return x.FilterAuditStatus
	}
	return 0
}

func (x *ListWithdrawalsRequest) GetFilterProcessingStatus() int32 {
	if x != nil {
		return x.FilterProcessingStatus
	}
	return 0
}

func (x *ListWithdrawalsRequest) GetFilterUserId() int64 {
	if x != nil {
		return x.FilterUserId
	}
	return 0
}

func (x *ListWithdrawalsRequest) GetFilterOrderNo() string {
	if x != nil {
		return x.FilterOrderNo
	}
	return ""
}

// ListWithdrawals 响应
type ListWithdrawalsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Withdrawals   []*Withdrawal          `protobuf:"bytes,1,rep,name=withdrawals,proto3" json:"withdrawals,omitempty"`                     // 提币记录列表
	TotalCount    int32                  `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`    // 总记录数
	CurrentPage   int32                  `protobuf:"varint,3,opt,name=current_page,json=currentPage,proto3" json:"current_page,omitempty"` // 当前页码
	TotalPages    int32                  `protobuf:"varint,4,opt,name=total_pages,json=totalPages,proto3" json:"total_pages,omitempty"`    // 总页数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListWithdrawalsResponse) Reset() {
	*x = ListWithdrawalsResponse{}
	mi := &file_task_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListWithdrawalsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWithdrawalsResponse) ProtoMessage() {}

func (x *ListWithdrawalsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_task_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWithdrawalsResponse.ProtoReflect.Descriptor instead.
func (*ListWithdrawalsResponse) Descriptor() ([]byte, []int) {
	return file_task_service_proto_rawDescGZIP(), []int{2}
}

func (x *ListWithdrawalsResponse) GetWithdrawals() []*Withdrawal {
	if x != nil {
		return x.Withdrawals
	}
	return nil
}

func (x *ListWithdrawalsResponse) GetTotalCount() int32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *ListWithdrawalsResponse) GetCurrentPage() int32 {
	if x != nil {
		return x.CurrentPage
	}
	return 0
}

func (x *ListWithdrawalsResponse) GetTotalPages() int32 {
	if x != nil {
		return x.TotalPages
	}
	return 0
}

// UpdateWithdrawalStatus 请求
type UpdateWithdrawalStatusRequest struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	WithdrawalId           int64                  `protobuf:"varint,1,opt,name=withdrawal_id,json=withdrawalId,proto3" json:"withdrawal_id,omitempty"`                                 // 提币记录ID
	AuditStatus            int32                  `protobuf:"varint,2,opt,name=audit_status,json=auditStatus,proto3" json:"audit_status,omitempty"`                                    // 可选：审核状态: 1-免审, 2-待审核, 3-审核通过, 4-审核拒绝
	AutoWithdrawalProgress int32                  `protobuf:"varint,3,opt,name=auto_withdrawal_progress,json=autoWithdrawalProgress,proto3" json:"auto_withdrawal_progress,omitempty"` // 可选：自动提现状态 0 未开始 1 进行中 2 成功 3 结束
	ProcessingStatus       int32                  `protobuf:"varint,4,opt,name=processing_status,json=processingStatus,proto3" json:"processing_status,omitempty"`                     // 可选：提现处理状态: 1-自动放币处理中, 2-处理中(待冷钱包转入)，3.待人工转账.，4-成功, 5-失败
	ErrorMessage           string                 `protobuf:"bytes,5,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`                                  // 可选：错误信息（JSON字符串）
	RefuseReasonZh         string                 `protobuf:"bytes,6,opt,name=refuse_reason_zh,json=refuseReasonZh,proto3" json:"refuse_reason_zh,omitempty"`                          // 可选：拒绝原因（中文）
	RefuseReasonEn         string                 `protobuf:"bytes,7,opt,name=refuse_reason_en,json=refuseReasonEn,proto3" json:"refuse_reason_en,omitempty"`                          // 可选：拒绝原因（英文）
	TxHash                 string                 `protobuf:"bytes,8,opt,name=tx_hash,json=txHash,proto3" json:"tx_hash,omitempty"`                                                    // 可选：交易哈希
	Retries                int32                  `protobuf:"varint,9,opt,name=retries,proto3" json:"retries,omitempty"`                                                               // 可选：重试次数
	AdminRemark            string                 `protobuf:"bytes,10,opt,name=admin_remark,json=adminRemark,proto3" json:"admin_remark,omitempty"`                                    // 可选：管理员备注
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *UpdateWithdrawalStatusRequest) Reset() {
	*x = UpdateWithdrawalStatusRequest{}
	mi := &file_task_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateWithdrawalStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWithdrawalStatusRequest) ProtoMessage() {}

func (x *UpdateWithdrawalStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_task_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWithdrawalStatusRequest.ProtoReflect.Descriptor instead.
func (*UpdateWithdrawalStatusRequest) Descriptor() ([]byte, []int) {
	return file_task_service_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateWithdrawalStatusRequest) GetWithdrawalId() int64 {
	if x != nil {
		return x.WithdrawalId
	}
	return 0
}

func (x *UpdateWithdrawalStatusRequest) GetAuditStatus() int32 {
	if x != nil {
		return x.AuditStatus
	}
	return 0
}

func (x *UpdateWithdrawalStatusRequest) GetAutoWithdrawalProgress() int32 {
	if x != nil {
		return x.AutoWithdrawalProgress
	}
	return 0
}

func (x *UpdateWithdrawalStatusRequest) GetProcessingStatus() int32 {
	if x != nil {
		return x.ProcessingStatus
	}
	return 0
}

func (x *UpdateWithdrawalStatusRequest) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *UpdateWithdrawalStatusRequest) GetRefuseReasonZh() string {
	if x != nil {
		return x.RefuseReasonZh
	}
	return ""
}

func (x *UpdateWithdrawalStatusRequest) GetRefuseReasonEn() string {
	if x != nil {
		return x.RefuseReasonEn
	}
	return ""
}

func (x *UpdateWithdrawalStatusRequest) GetTxHash() string {
	if x != nil {
		return x.TxHash
	}
	return ""
}

func (x *UpdateWithdrawalStatusRequest) GetRetries() int32 {
	if x != nil {
		return x.Retries
	}
	return 0
}

func (x *UpdateWithdrawalStatusRequest) GetAdminRemark() string {
	if x != nil {
		return x.AdminRemark
	}
	return ""
}

// 通用 API 响应结构 (使用 oneof)
type ApiResponse struct {
	state   protoimpl.MessageState `protogen:"open.v1"`
	Code    int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`      // 业务状态码
	Message string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"` // 响应消息
	// Types that are valid to be assigned to DataPayload:
	//
	//	*ApiResponse_ListWithdrawalsData
	//	*ApiResponse_SuccessNoData
	DataPayload   isApiResponse_DataPayload `protobuf_oneof:"data_payload"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ApiResponse) Reset() {
	*x = ApiResponse{}
	mi := &file_task_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApiResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApiResponse) ProtoMessage() {}

func (x *ApiResponse) ProtoReflect() protoreflect.Message {
	mi := &file_task_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApiResponse.ProtoReflect.Descriptor instead.
func (*ApiResponse) Descriptor() ([]byte, []int) {
	return file_task_service_proto_rawDescGZIP(), []int{4}
}

func (x *ApiResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ApiResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ApiResponse) GetDataPayload() isApiResponse_DataPayload {
	if x != nil {
		return x.DataPayload
	}
	return nil
}

func (x *ApiResponse) GetListWithdrawalsData() *ListWithdrawalsResponse {
	if x != nil {
		if x, ok := x.DataPayload.(*ApiResponse_ListWithdrawalsData); ok {
			return x.ListWithdrawalsData
		}
	}
	return nil
}

func (x *ApiResponse) GetSuccessNoData() *emptypb.Empty {
	if x != nil {
		if x, ok := x.DataPayload.(*ApiResponse_SuccessNoData); ok {
			return x.SuccessNoData
		}
	}
	return nil
}

type isApiResponse_DataPayload interface {
	isApiResponse_DataPayload()
}

type ApiResponse_ListWithdrawalsData struct {
	ListWithdrawalsData *ListWithdrawalsResponse `protobuf:"bytes,3,opt,name=list_withdrawals_data,json=listWithdrawalsData,proto3,oneof"` // 用于 ListWithdrawals 成功响应
}

type ApiResponse_SuccessNoData struct {
	SuccessNoData *emptypb.Empty `protobuf:"bytes,4,opt,name=success_no_data,json=successNoData,proto3,oneof"` // 用于 UpdateWithdrawalStatus 等无数据成功响应
}

func (*ApiResponse_ListWithdrawalsData) isApiResponse_DataPayload() {}

func (*ApiResponse_SuccessNoData) isApiResponse_DataPayload() {}

var File_task_service_proto protoreflect.FileDescriptor

const file_task_service_proto_rawDesc = "" +
	"\n" +
	"\x12task_service.proto\x12\atask.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1bgoogle/protobuf/empty.proto\"\xe6\b\n" +
	"\n" +
	"Withdrawal\x12*\n" +
	"\x11user_withdraws_id\x18\x01 \x01(\x03R\x0fuserWithdrawsId\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\x03R\x06userId\x12\x19\n" +
	"\btoken_id\x18\x03 \x01(\x03R\atokenId\x12\x1b\n" +
	"\twallet_id\x18\x04 \x01(\tR\bwalletId\x12\x12\n" +
	"\x04name\x18\x05 \x01(\tR\x04name\x12\x12\n" +
	"\x04chan\x18\x06 \x01(\tR\x04chan\x12\x19\n" +
	"\border_no\x18\a \x01(\tR\aorderNo\x12\x18\n" +
	"\aaddress\x18\b \x01(\tR\aaddress\x12%\n" +
	"\x0erecipient_name\x18\t \x01(\tR\rrecipientName\x12+\n" +
	"\x11recipient_account\x18\n" +
	" \x01(\tR\x10recipientAccount\x12\x16\n" +
	"\x06amount\x18\v \x01(\x01R\x06amount\x12!\n" +
	"\fhandling_fee\x18\f \x01(\x01R\vhandlingFee\x12#\n" +
	"\ractual_amount\x18\r \x01(\x01R\factualAmount\x12!\n" +
	"\faudit_status\x18\x0e \x01(\x05R\vauditStatus\x128\n" +
	"\x18auto_withdrawal_progress\x18\x0f \x01(\x05R\x16autoWithdrawalProgress\x12+\n" +
	"\x11processing_status\x18\x10 \x01(\x05R\x10processingStatus\x12(\n" +
	"\x10refuse_reason_zh\x18\x11 \x01(\tR\x0erefuseReasonZh\x12(\n" +
	"\x10refuse_reason_en\x18\x12 \x01(\tR\x0erefuseReasonEn\x12\x17\n" +
	"\atx_hash\x18\x13 \x01(\tR\x06txHash\x12#\n" +
	"\rerror_message\x18\x14 \x01(\tR\ferrorMessage\x12\x1f\n" +
	"\vuser_remark\x18\x15 \x01(\tR\n" +
	"userRemark\x12!\n" +
	"\fadmin_remark\x18\x16 \x01(\tR\vadminRemark\x129\n" +
	"\n" +
	"created_at\x18\x17 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"checked_at\x18\x18 \x01(\v2\x1a.google.protobuf.TimestampR\tcheckedAt\x12?\n" +
	"\rprocessing_at\x18\x19 \x01(\v2\x1a.google.protobuf.TimestampR\fprocessingAt\x12=\n" +
	"\fcompleted_at\x18\x1a \x01(\v2\x1a.google.protobuf.TimestampR\vcompletedAt\x129\n" +
	"\n" +
	"updated_at\x18\x1b \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\x12\x18\n" +
	"\aretries\x18\x1c \x01(\x05R\aretries\x12\x1f\n" +
	"\vnergy_state\x18\x1d \x01(\x05R\n" +
	"nergyState\"\x81\x02\n" +
	"\x16ListWithdrawalsRequest\x12\x1b\n" +
	"\tpage_size\x18\x01 \x01(\x05R\bpageSize\x12\x12\n" +
	"\x04page\x18\x02 \x01(\x05R\x04page\x12.\n" +
	"\x13filter_audit_status\x18\x03 \x01(\x05R\x11filterAuditStatus\x128\n" +
	"\x18filter_processing_status\x18\x04 \x01(\x05R\x16filterProcessingStatus\x12$\n" +
	"\x0efilter_user_id\x18\x05 \x01(\x03R\ffilterUserId\x12&\n" +
	"\x0ffilter_order_no\x18\x06 \x01(\tR\rfilterOrderNo\"\xb5\x01\n" +
	"\x17ListWithdrawalsResponse\x125\n" +
	"\vwithdrawals\x18\x01 \x03(\v2\x13.task.v1.WithdrawalR\vwithdrawals\x12\x1f\n" +
	"\vtotal_count\x18\x02 \x01(\x05R\n" +
	"totalCount\x12!\n" +
	"\fcurrent_page\x18\x03 \x01(\x05R\vcurrentPage\x12\x1f\n" +
	"\vtotal_pages\x18\x04 \x01(\x05R\n" +
	"totalPages\"\x9d\x03\n" +
	"\x1dUpdateWithdrawalStatusRequest\x12#\n" +
	"\rwithdrawal_id\x18\x01 \x01(\x03R\fwithdrawalId\x12!\n" +
	"\faudit_status\x18\x02 \x01(\x05R\vauditStatus\x128\n" +
	"\x18auto_withdrawal_progress\x18\x03 \x01(\x05R\x16autoWithdrawalProgress\x12+\n" +
	"\x11processing_status\x18\x04 \x01(\x05R\x10processingStatus\x12#\n" +
	"\rerror_message\x18\x05 \x01(\tR\ferrorMessage\x12(\n" +
	"\x10refuse_reason_zh\x18\x06 \x01(\tR\x0erefuseReasonZh\x12(\n" +
	"\x10refuse_reason_en\x18\a \x01(\tR\x0erefuseReasonEn\x12\x17\n" +
	"\atx_hash\x18\b \x01(\tR\x06txHash\x12\x18\n" +
	"\aretries\x18\t \x01(\x05R\aretries\x12!\n" +
	"\fadmin_remark\x18\n" +
	" \x01(\tR\vadminRemark\"\xe5\x01\n" +
	"\vApiResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12V\n" +
	"\x15list_withdrawals_data\x18\x03 \x01(\v2 .task.v1.ListWithdrawalsResponseH\x00R\x13listWithdrawalsData\x12@\n" +
	"\x0fsuccess_no_data\x18\x04 \x01(\v2\x16.google.protobuf.EmptyH\x00R\rsuccessNoDataB\x0e\n" +
	"\fdata_payload2\xaf\x01\n" +
	"\vTaskService\x12H\n" +
	"\x0fListWithdrawals\x12\x1f.task.v1.ListWithdrawalsRequest\x1a\x14.task.v1.ApiResponse\x12V\n" +
	"\x16UpdateWithdrawalStatus\x12&.task.v1.UpdateWithdrawalStatusRequest\x1a\x14.task.v1.ApiResponseB Z\x1etask-server/api/task/v1;taskv1b\x06proto3"

var (
	file_task_service_proto_rawDescOnce sync.Once
	file_task_service_proto_rawDescData []byte
)

func file_task_service_proto_rawDescGZIP() []byte {
	file_task_service_proto_rawDescOnce.Do(func() {
		file_task_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_task_service_proto_rawDesc), len(file_task_service_proto_rawDesc)))
	})
	return file_task_service_proto_rawDescData
}

var file_task_service_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_task_service_proto_goTypes = []any{
	(*Withdrawal)(nil),                    // 0: task.v1.Withdrawal
	(*ListWithdrawalsRequest)(nil),        // 1: task.v1.ListWithdrawalsRequest
	(*ListWithdrawalsResponse)(nil),       // 2: task.v1.ListWithdrawalsResponse
	(*UpdateWithdrawalStatusRequest)(nil), // 3: task.v1.UpdateWithdrawalStatusRequest
	(*ApiResponse)(nil),                   // 4: task.v1.ApiResponse
	(*timestamppb.Timestamp)(nil),         // 5: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),                 // 6: google.protobuf.Empty
}
var file_task_service_proto_depIdxs = []int32{
	5,  // 0: task.v1.Withdrawal.created_at:type_name -> google.protobuf.Timestamp
	5,  // 1: task.v1.Withdrawal.checked_at:type_name -> google.protobuf.Timestamp
	5,  // 2: task.v1.Withdrawal.processing_at:type_name -> google.protobuf.Timestamp
	5,  // 3: task.v1.Withdrawal.completed_at:type_name -> google.protobuf.Timestamp
	5,  // 4: task.v1.Withdrawal.updated_at:type_name -> google.protobuf.Timestamp
	0,  // 5: task.v1.ListWithdrawalsResponse.withdrawals:type_name -> task.v1.Withdrawal
	2,  // 6: task.v1.ApiResponse.list_withdrawals_data:type_name -> task.v1.ListWithdrawalsResponse
	6,  // 7: task.v1.ApiResponse.success_no_data:type_name -> google.protobuf.Empty
	1,  // 8: task.v1.TaskService.ListWithdrawals:input_type -> task.v1.ListWithdrawalsRequest
	3,  // 9: task.v1.TaskService.UpdateWithdrawalStatus:input_type -> task.v1.UpdateWithdrawalStatusRequest
	4,  // 10: task.v1.TaskService.ListWithdrawals:output_type -> task.v1.ApiResponse
	4,  // 11: task.v1.TaskService.UpdateWithdrawalStatus:output_type -> task.v1.ApiResponse
	10, // [10:12] is the sub-list for method output_type
	8,  // [8:10] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_task_service_proto_init() }
func file_task_service_proto_init() {
	if File_task_service_proto != nil {
		return
	}
	file_task_service_proto_msgTypes[4].OneofWrappers = []any{
		(*ApiResponse_ListWithdrawalsData)(nil),
		(*ApiResponse_SuccessNoData)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_task_service_proto_rawDesc), len(file_task_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_task_service_proto_goTypes,
		DependencyIndexes: file_task_service_proto_depIdxs,
		MessageInfos:      file_task_service_proto_msgTypes,
	}.Build()
	File_task_service_proto = out.File
	file_task_service_proto_goTypes = nil
	file_task_service_proto_depIdxs = nil
}
