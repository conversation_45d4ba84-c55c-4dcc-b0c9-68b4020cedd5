# 配置模板与变量提取操作指令

以下是生成配置模板并提取变量的操作步骤：

1.  **生成初始模板**：
    *   读取 `manifest/config/config.yaml` 文件。
    *   基于该文件内容，生成一个新的 `manifest/config/config.yaml.template` 文件。在此过程中，将 `config.yaml` 中的具体值替换为环境变量占位符（例如 `${TASK_SERVER_VARIABLE_NAME}`）。所有环境变量占位符添加项目特定的前缀 `TASK_SERVER_`（例如，`${VARIABLE_NAME}` 变为 `${TASK_SERVER_VARIABLE_NAME}

4.  **提取变量到 JSON 文件**：
    *   解析 `manifest/config/config.yaml.template` 文件，提取所有以 `TASK_SERVER_` 开头的环境变量名称。
    *   解析 `manifest/config/config.yaml` 文件，查找与提取的变量名相对应的配置值。
    *   创建一个新的或更新现有的 `config_variables.json` 文件。
    *   将提取的变量名作为 JSON 键，将从 `config.yaml` 中找到的对应值作为 JSON 值，写入 `config_variables.json` 文件。确保 JSON 格式正确。

按照这些步骤操作，即可完成配置模板的生成和变量的提取。