database:
  logger:
    path: "${TASK_SERVER_DATABASE_LOGGER_PATH}" # 日志文件路径。默认为空，表示关闭，仅输出到终端
    level: "${TASK_SERVER_DATABASE_LOGGER_LEVEL}"
    stdout: ${TASK_SERVER_DATABASE_LOGGER_STDOUT}
  default: # 使用 MySQL
    link: "${TASK_SERVER_DATABASE_DEFAULT_LINK}" # 确认或修改为您的 MySQL 连接信息
    debug: ${TASK_SERVER_DATABASE_DEFAULT_DEBUG} # 开发环境建议开启
redis:
  default:
    address: "${TASK_SERVER_REDIS_DEFAULT_ADDRESS}" # 确认或修改为您的 Redis 地址
    db: ${TASK_SERVER_REDIS_DEFAULT_DB} # 确认或修改 DB 编号
    pass: "${TASK_SERVER_REDIS_DEFAULT_PASS}" # 确认或修改密码
    idleTimeout: "${TASK_SERVER_REDIS_DEFAULT_IDLE_TIMEOUT}" # 补充单位

logger:
  path: "${TASK_SERVER_LOGGER_PATH}" # 统一日志目录，各应用可分子目录
  level: "${TASK_SERVER_LOGGER_LEVEL}"
  stdout: ${TASK_SERVER_LOGGER_STDOUT}
  rotateSize: "${TASK_SERVER_LOGGER_ROTATE_SIZE}"
  rotateExpire: "${TASK_SERVER_LOGGER_ROTATE_EXPIRE}"
  format: "${TASK_SERVER_LOGGER_FORMAT}" # 使用 JSON 格式方便收集

# Wallet Service API 配置 (根据实际情况修改)
walletsApi:
  baseUrl: "${TASK_SERVER_WALLETS_API_BASE_URL}" # 替换为 Wallet API 的实际 Base URL


# Task v2 配置文件
# 全局设置
global:
  logLevel: "${TASK_SERVER_GLOBAL_LOG_LEVEL}"
  scanInterval: "${TASK_SERVER_GLOBAL_SCAN_INTERVAL}"          # 统一扫描间隔
  healthCheckInterval: "${TASK_SERVER_GLOBAL_HEALTH_CHECK_INTERVAL}"
  maxRetries: ${TASK_SERVER_GLOBAL_MAX_RETRIES}
  requestTimeout: "${TASK_SERVER_GLOBAL_REQUEST_TIMEOUT}"

# 区块链配置 - 每个链完整配置在一个块中
chains:
  ETH:
    enabled: ${TASK_SERVER_CHAINS_ETH_ENABLED}
    # 私有节点（正在同步的主网节点，对 eth_getLogs 有限制）
    # rpcUrl: "http://62.182.80.72:8545"
    # 备用：Google Cloud 的以太坊节点（完全同步的节点）
    rpcUrl: "${TASK_SERVER_CHAINS_ETH_RPC_URL}"

    # startBlock: 3813550  # 可选，不设置则自动从最新区块-100开始
    scanBatchSize: ${TASK_SERVER_CHAINS_ETH_SCAN_BATCH_SIZE}

    # 原生代币
    native:
      symbol: "${TASK_SERVER_CHAINS_ETH_NATIVE_SYMBOL}"
      decimals: ${TASK_SERVER_CHAINS_ETH_NATIVE_DECIMALS}
      confirmations: ${TASK_SERVER_CHAINS_ETH_NATIVE_CONFIRMATIONS}
      enabled: ${TASK_SERVER_CHAINS_ETH_NATIVE_ENABLED}

    # 代币列表
    tokens:
      - symbol: "${TASK_SERVER_CHAINS_ETH_TOKENS_0_SYMBOL}"
        contractAddress: "${TASK_SERVER_CHAINS_ETH_TOKENS_0_CONTRACT_ADDRESS}"
        decimals: ${TASK_SERVER_CHAINS_ETH_TOKENS_0_DECIMALS}
        confirmations: ${TASK_SERVER_CHAINS_ETH_TOKENS_0_CONFIRMATIONS}
        enabled: ${TASK_SERVER_CHAINS_ETH_TOKENS_0_ENABLED}

  TRON:
    enabled: ${TASK_SERVER_CHAINS_TRON_ENABLED}
    # Option 1: TronGrid (requires API key)
    # rpcUrl: "grpc.trongrid.io:50051"
    # apiKey: "your-api-key-here"

    # Option 2: Direct IP endpoints (may work without API key)
    rpcUrl: "${TASK_SERVER_CHAINS_TRON_RPC_URL}"
    apiKey: "${TASK_SERVER_CHAINS_TRON_API_KEY}"
    # startBlock: 0  # 可选，不设置则自动从最新区块-100开始
    scanBatchSize: ${TASK_SERVER_CHAINS_TRON_SCAN_BATCH_SIZE}  # 减少批量大小以降低RPC负载

    # 原生代币
    native:
      symbol: "${TASK_SERVER_CHAINS_TRON_NATIVE_SYMBOL}"
      decimals: ${TASK_SERVER_CHAINS_TRON_NATIVE_DECIMALS}
      confirmations: ${TASK_SERVER_CHAINS_TRON_NATIVE_CONFIRMATIONS}
      enabled: ${TASK_SERVER_CHAINS_TRON_NATIVE_ENABLED}

    # 代币列表
    tokens:
      - symbol: "${TASK_SERVER_CHAINS_TRON_TOKENS_0_SYMBOL}"
        contractAddress: "${TASK_SERVER_CHAINS_TRON_TOKENS_0_CONTRACT_ADDRESS}"
        decimals: ${TASK_SERVER_CHAINS_TRON_TOKENS_0_DECIMALS}
        confirmations: ${TASK_SERVER_CHAINS_TRON_TOKENS_0_CONFIRMATIONS}
        enabled: ${TASK_SERVER_CHAINS_TRON_TOKENS_0_ENABLED}

# 存储配置
storage:
  redis:
    keyPrefix: "${TASK_SERVER_STORAGE_REDIS_KEY_PREFIX}"
    lockTimeout: "${TASK_SERVER_STORAGE_REDIS_LOCK_TIMEOUT}"

# 任务配置
tasks:
  confirmation:
    enabled: ${TASK_SERVER_TASKS_CONFIRMATION_ENABLED}
    interval: "${TASK_SERVER_TASKS_CONFIRMATION_INTERVAL}"
    batchSize: ${TASK_SERVER_TASKS_CONFIRMATION_BATCH_SIZE}
    maxRetries: ${TASK_SERVER_TASKS_CONFIRMATION_MAX_RETRIES}
    timeout: "${TASK_SERVER_TASKS_CONFIRMATION_TIMEOUT}"

    # 最小金额阈值配置（最小单位）
    thresholds:
      ETH: "${TASK_SERVER_TASKS_CONFIRMATION_THRESHOLDS_ETH}"   # 0.001 ETH
      USDT: "${TASK_SERVER_TASKS_CONFIRMATION_THRESHOLDS_USDT}"             # 1 USDT (6 decimals)
      TRX: "${TASK_SERVER_TASKS_CONFIRMATION_THRESHOLDS_TRX}"              # 1 TRX (6 decimals)

    # 每个链的确认配置
    chains:
      ETH:
        enabled: ${TASK_SERVER_TASKS_CONFIRMATION_CHAINS_ETH_ENABLED}
      TRON:
        enabled: ${TASK_SERVER_TASKS_CONFIRMATION_CHAINS_TRON_ENABLED}

# 监控配置
monitoring:
  metricsPort: ${TASK_SERVER_MONITORING_METRICS_PORT}
  alerting:
    errorThreshold: ${TASK_SERVER_MONITORING_ALERTING_ERROR_THRESHOLD}
    timeWindow: "${TASK_SERVER_MONITORING_ALERTING_TIME_WINDOW}"