
database:
  logger:
    path: "logs/database" # 日志文件路径。默认为空，表示关闭，仅输出到终端
    level: "all"
    stdout: true
  default: # 使用 MySQL
    link: "mysql:root:root@tcp(127.0.0.1:3306)/xpayapi?loc=Local&parseTime=true&charset=utf8mb4" # 确认或修改为您的 MySQL 连接信息
    debug: false # 开发环境建议开启
redis:
  default:
    address: "127.0.0.1:6379" # 确认或修改为您的 Redis 地址
    db: 2 # 确认或修改 DB 编号
    pass: "valkey_password" # 确认或修改密码
    idleTimeout: 20s # 补充单位

logger:
  path: "logs" # 统一日志目录，各应用可分子目录
  level: "all"
  stdout: true
  rotateSize: "100M"
  rotateExpire: "7d"
  format: "json" # 使用 JSON 格式方便收集

# Wallet Service API 配置 (根据实际情况修改)
walletsApi:
  baseUrl: "http://127.0.0.1:8080" # 替换为 Wallet API 的实际 Base URL


# Task v2 配置文件
# 全局设置
global:
  logLevel: "debug"
  scanInterval: "10s"          # 统一扫描间隔
  healthCheckInterval: "30s"
  maxRetries: 3
  requestTimeout: "30s"
  
# 区块链配置 - 每个链完整配置在一个块中
chains:
  ETH:
    enabled: true
    # 私有节点（正在同步的主网节点，对 eth_getLogs 有限制）
    rpcUrl: "http://************:8545"
    # 备用：Google Cloud 的以太坊节点（完全同步的节点）
    # rpcUrl: "https://blockchain.googleapis.com/v1/projects/kinetic-harbor-460313-n6/locations/us-central1/endpoints/ethereum-mainnet/rpc?key=AIzaSyDsLLHG_zaqWLMXXot-fKAzstGYBUwDzsM"
   
    # startBlock: 3813550  # 可选，不设置则自动从最新区块-100开始
    scanBatchSize: 5
    
    # 原生代币
    native:
      symbol: "ETH"
      decimals: 18
      confirmations: 6
      enabled: true
      
    # 代币列表
    tokens:
      - symbol: "USDT"
        contractAddress: "******************************************"
        decimals: 6
        confirmations: 6
        enabled: true
        
  TRON:
    enabled: true
    # Option 1: TronGrid (requires API key)
    # rpcUrl: "grpc.trongrid.io:50051"
    # apiKey: "your-api-key-here"
    useTLS: false
    # Option 2: Direct IP endpoints (may work without API key)
    # rpcUrl: "************:50051"
    rpcUrl: "************:50051"
    # apiKey: "2279113c33d66b6b0cc5fe2d9970c47b51b27f3e"
    # startBlock: 0  # 可选，不设置则自动从最新区块-100开始
    scanBatchSize: 5  # 减少批量大小以降低RPC负载
    
    # 原生代币
    native:
      symbol: "TRX"
      decimals: 6
      confirmations: 19
      enabled: true
      
    # 代币列表
    tokens:
      - symbol: "USDT"
        contractAddress: "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"
        decimals: 6
        confirmations: 19
        enabled: true

# 存储配置
storage:
  redis:
    keyPrefix: "taskv2"
    lockTimeout: "60s"
    
# 任务配置
tasks:
  confirmation:
    enabled: true
    interval: "10s"
    batchSize: 100
    maxRetries: 3
    timeout: "30s"
    
    # 最小金额阈值配置（最小单位）
    thresholds:
      ETH: "0.001"   # 0.001 ETH
      USDT: "1"             # 1 USDT (6 decimals)
      TRX: "1"              # 1 TRX (6 decimals)
      
    # 每个链的确认配置
    chains:
      ETH:
        enabled: true
      TRON:
        enabled: true

# 监控配置
monitoring:
  metricsPort: 9090
  alerting:
    errorThreshold: 5
    timeWindow: "5m"