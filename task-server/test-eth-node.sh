#!/bin/bash

# 测试以太坊节点状态的脚本
RPC_URL="http://62.182.80.72:8545"

echo "=== 测试以太坊节点: $RPC_URL ==="
echo ""

# 1. 获取客户端版本
echo "1. 客户端版本 (web3_clientVersion):"
curl -s -X POST $RPC_URL \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","method":"web3_clientVersion","params":[],"id":1}' | grep -o '"result":"[^"]*"' || echo "失败"
echo ""

# 2. 获取网络ID
echo "2. 网络ID (net_version):"
curl -s -X POST $RPC_URL \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","method":"net_version","params":[],"id":1}' | grep -o '"result":"[^"]*"'
echo ""

# 3. 获取链ID
echo "3. 链ID (eth_chainId):"
curl -s -X POST $RPC_URL \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","method":"eth_chainId","params":[],"id":1}' | grep -o '"result":"[^"]*"'
echo ""

# 4. 获取对等节点数量
echo "4. 对等节点数量 (net_peerCount):"
curl -s -X POST $RPC_URL \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","method":"net_peerCount","params":[],"id":1}' | grep -o '"result":"[^"]*"'
echo ""

# 5. 检查同步状态
echo "5. 同步状态 (eth_syncing):"
curl -s -X POST $RPC_URL \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","method":"eth_syncing","params":[],"id":1}'
echo -e "\n"

# 6. 获取当前区块号
echo "6. 当前区块号 (eth_blockNumber):"
BLOCK_HEX=$(curl -s -X POST $RPC_URL \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}' | grep -o '"result":"[^"]*"' | cut -d'"' -f4)
echo "十六进制: $BLOCK_HEX"
if [ ! -z "$BLOCK_HEX" ]; then
  echo "十进制: $((16#${BLOCK_HEX#0x}))"
fi
echo ""

# 7. 获取最新区块信息
echo "7. 最新区块信息 (eth_getBlockByNumber 'latest'):"
curl -s -X POST $RPC_URL \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","method":"eth_getBlockByNumber","params":["latest", false],"id":1}' | python3 -c "
import json, sys
data = json.load(sys.stdin)
if 'result' in data and data['result']:
    block = data['result']
    print(f\"区块号: {int(block.get('number', '0x0'), 16)}\")
    print(f\"时间戳: {int(block.get('timestamp', '0x0'), 16)}\")
    print(f\"交易数: {len(block.get('transactions', []))}\")
    print(f\"矿工: {block.get('miner', 'N/A')}\")
else:
    print('无结果')
" 2>/dev/null || echo "解析失败"
echo ""

# 8. 获取创世区块
echo "8. 创世区块信息 (eth_getBlockByNumber '0x0'):"
curl -s -X POST $RPC_URL \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","method":"eth_getBlockByNumber","params":["0x0", false],"id":1}' | python3 -c "
import json, sys
data = json.load(sys.stdin)
if 'result' in data and data['result']:
    block = data['result']
    print(f\"哈希: {block.get('hash', 'N/A')}\")
    print(f\"时间戳: {int(block.get('timestamp', '0x0'), 16)}\")
else:
    print('无结果')
" 2>/dev/null || echo "解析失败"
echo ""

# 9. 测试一些主网区块
echo "9. 测试主网区块存在性:"
echo -n "  区块 1 (主网第一个区块): "
RESULT=$(curl -s -X POST $RPC_URL \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","method":"eth_getBlockByNumber","params":["0x1", false],"id":1}' | grep -o '"result":null\|"result":{')
if [[ $RESULT == *"result\":{"* ]]; then echo "存在"; else echo "不存在"; fi

echo -n "  区块 15000000 (主网较新区块): "
RESULT=$(curl -s -X POST $RPC_URL \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","method":"eth_getBlockByNumber","params":["0xE4E1C0", false],"id":1}' | grep -o '"result":null\|"result":{')
if [[ $RESULT == *"result\":{"* ]]; then echo "存在"; else echo "不存在"; fi
echo ""

# 10. 获取Gas价格
echo "10. Gas价格 (eth_gasPrice):"
curl -s -X POST $RPC_URL \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","method":"eth_gasPrice","params":[],"id":1}' | grep -o '"result":"[^"]*"'
echo ""

# 11. 测试eth_getLogs
echo "11. 测试 eth_getLogs:"
echo -n "  使用 'latest' 区块: "
RESULT=$(curl -s -X POST $RPC_URL \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","method":"eth_getLogs","params":[{"fromBlock":"latest","toBlock":"latest"}],"id":1}' | grep -o '"error"\|"result"')
if [[ $RESULT == *"result"* ]]; then echo "成功"; else echo "失败"; fi

echo -n "  使用区块 0: "
RESULT=$(curl -s -X POST $RPC_URL \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","method":"eth_getLogs","params":[{"fromBlock":"0x0","toBlock":"0x0"}],"id":1}' | grep -o '"error"\|"result"')
if [[ $RESULT == *"result"* ]]; then echo "成功"; else echo "失败"; fi

echo ""
echo "=== 测试完成 ==="