# === Builder Stage ===
FROM golang:1.24-alpine AS builder

# Set GO111MODULE=on, disable <PERSON><PERSON><PERSON> to avoid potential cross-compilation issues with gcc in Alpine
ENV GO111MODULE=on \
    CGO_ENABLED=0

# Install build dependencies (gcc/libc-dev might not be strictly needed if <PERSON><PERSON><PERSON> is disabled, but keep make/git)
RUN apk update && apk add make git

# Set timezone
ENV TZ=Asia/Shanghai
RUN apk --no-cache add tzdata && \
    ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && \
    echo $TZ > /etc/timezone

WORKDIR /build
COPY . .

RUN go mod download
RUN go mod tidy

# Directly use go build, disable <PERSON><PERSON><PERSON>, and specify target OS/ARCH
# Output directly to /build/bin
RUN mkdir -p /build/bin
RUN GOOS=linux GOARCH=amd64 go build -o /build/bin/grpc_server cmd/grpc_server/main.go
RUN GOOS=linux GOARCH=amd64 go build -o /build/bin/taskv2 cmd/taskv2/main.go

# entrypoint.sh and config.yaml.template are already in /build due to "COPY . ."
# No need for the extra cp commands here. Subsequent stages will copy from /build.
# RUN cp ./entrypoint.sh /build/entrypoint.sh # REMOVED
# RUN mkdir -p /build/manifest/config # Keep mkdir if needed by template path
# RUN cp ./manifest/config/config.yaml.template /build/manifest/config/config.yaml.template # REMOVED


# === gRPC Server Final Stage ===
FROM alpine:latest AS grpc-server-final

WORKDIR /home

# Copy binary from builder stage
COPY --from=builder /build/bin/grpc_server /home/<USER>
RUN chmod +x /home/<USER>

# Copy entrypoint script and configuration template from builder stage
# These files are directly in /build in the builder stage
COPY --from=builder /build/entrypoint.sh /home/<USER>
COPY --from=builder /build/manifest/config/config.yaml.template /home/<USER>/config/config.yaml.template

# Install runtime dependencies (keep bash, jq, gettext, tzdata)
RUN apk update && apk add --no-cache bash jq gettext tzdata && \
    chmod 500 /home/<USER>
    rm -rf /var/cache/apk/* # Clean apk cache

# Set the entrypoint script
ENTRYPOINT [ "/home/<USER>" ]

# Default command to run the grpc_server application
CMD ["/home/<USER>"]


# === Task Scheduler Final Stage ===
FROM alpine:latest AS task-scheduler-final

WORKDIR /home

# Copy binary from builder stage
COPY --from=builder /build/bin/taskv2 /home/<USER>
RUN chmod +x /home/<USER>

# Copy entrypoint script and configuration template from builder stage
# These files are directly in /build in the builder stage
COPY --from=builder /build/entrypoint.sh /home/<USER>
COPY --from=builder /build/manifest/config/config.yaml.template /home/<USER>/config/config.yaml.template

# Install runtime dependencies (keep bash, jq, gettext, tzdata)
RUN apk update && apk add --no-cache bash jq gettext tzdata && \
    chmod 500 /home/<USER>
    rm -rf /var/cache/apk/* # Clean apk cache

# Set the entrypoint script
ENTRYPOINT [ "/home/<USER>" ]

# Default command to run the taskv2 application
CMD ["/home/<USER>"]