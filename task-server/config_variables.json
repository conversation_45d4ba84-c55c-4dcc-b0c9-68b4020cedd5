{"TASK_SERVER_DATABASE_LOGGER_PATH": "logs/database", "TASK_SERVER_DATABASE_LOGGER_LEVEL": "all", "TASK_SERVER_DATABASE_LOGGER_STDOUT": true, "TASK_SERVER_DATABASE_DEFAULT_LINK": "mysql:root:root@tcp(mysql:3306)/xpayapi?loc=Local&parseTime=true&charset=utf8mb4", "TASK_SERVER_DATABASE_DEFAULT_DEBUG": false, "TASK_SERVER_REDIS_DEFAULT_ADDRESS": "valkey:6379", "TASK_SERVER_REDIS_DEFAULT_DB": 2, "TASK_SERVER_REDIS_DEFAULT_PASS": "valkey_password", "TASK_SERVER_REDIS_DEFAULT_IDLE_TIMEOUT": "20s", "TASK_SERVER_LOGGER_PATH": "logs", "TASK_SERVER_LOGGER_LEVEL": "all", "TASK_SERVER_LOGGER_STDOUT": true, "TASK_SERVER_LOGGER_ROTATE_SIZE": "100M", "TASK_SERVER_LOGGER_ROTATE_EXPIRE": "7d", "TASK_SERVER_LOGGER_FORMAT": "json", "TASK_SERVER_WALLETS_API_BASE_URL": "http://wallets:8080", "TASK_SERVER_GLOBAL_LOG_LEVEL": "debug", "TASK_SERVER_GLOBAL_SCAN_INTERVAL": "10s", "TASK_SERVER_GLOBAL_HEALTH_CHECK_INTERVAL": "30s", "TASK_SERVER_GLOBAL_MAX_RETRIES": 3, "TASK_SERVER_GLOBAL_REQUEST_TIMEOUT": "30s", "TASK_SERVER_CHAINS_ETH_ENABLED": true, "TASK_SERVER_CHAINS_ETH_RPC_URL": "https://blockchain.googleapis.com/v1/projects/kinetic-harbor-460313-n6/locations/us-central1/endpoints/ethereum-mainnet/rpc?key=AIzaSyDsLLHG_zaqWLMXXot-fKAzstGYBUwDzsM", "TASK_SERVER_CHAINS_ETH_SCAN_BATCH_SIZE": 5, "TASK_SERVER_CHAINS_ETH_NATIVE_SYMBOL": "ETH", "TASK_SERVER_CHAINS_ETH_NATIVE_DECIMALS": 18, "TASK_SERVER_CHAINS_ETH_NATIVE_CONFIRMATIONS": 6, "TASK_SERVER_CHAINS_ETH_NATIVE_ENABLED": true, "TASK_SERVER_CHAINS_ETH_TOKENS_0_SYMBOL": "USDT", "TASK_SERVER_CHAINS_ETH_TOKENS_0_CONTRACT_ADDRESS": "******************************************", "TASK_SERVER_CHAINS_ETH_TOKENS_0_DECIMALS": 6, "TASK_SERVER_CHAINS_ETH_TOKENS_0_CONFIRMATIONS": 6, "TASK_SERVER_CHAINS_ETH_TOKENS_0_ENABLED": true, "TASK_SERVER_CHAINS_TRON_ENABLED": true, "TASK_SERVER_CHAINS_TRON_RPC_URL": "cool-responsive-brook.tron-mainnet.quiknode.pro:50051", "TASK_SERVER_CHAINS_TRON_API_KEY": "2279113c33d66b6b0cc5fe2d9970c47b51b27f3e", "TASK_SERVER_CHAINS_TRON_SCAN_BATCH_SIZE": 5, "TASK_SERVER_CHAINS_TRON_NATIVE_SYMBOL": "TRX", "TASK_SERVER_CHAINS_TRON_NATIVE_DECIMALS": 6, "TASK_SERVER_CHAINS_TRON_NATIVE_CONFIRMATIONS": 19, "TASK_SERVER_CHAINS_TRON_NATIVE_ENABLED": true, "TASK_SERVER_CHAINS_TRON_TOKENS_0_SYMBOL": "USDT", "TASK_SERVER_CHAINS_TRON_TOKENS_0_CONTRACT_ADDRESS": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "TASK_SERVER_CHAINS_TRON_TOKENS_0_DECIMALS": 6, "TASK_SERVER_CHAINS_TRON_TOKENS_0_CONFIRMATIONS": 19, "TASK_SERVER_CHAINS_TRON_TOKENS_0_ENABLED": true, "TASK_SERVER_STORAGE_REDIS_KEY_PREFIX": "taskv2", "TASK_SERVER_STORAGE_REDIS_LOCK_TIMEOUT": "60s", "TASK_SERVER_TASKS_CONFIRMATION_ENABLED": true, "TASK_SERVER_TASKS_CONFIRMATION_INTERVAL": "10s", "TASK_SERVER_TASKS_CONFIRMATION_BATCH_SIZE": 100, "TASK_SERVER_TASKS_CONFIRMATION_MAX_RETRIES": 3, "TASK_SERVER_TASKS_CONFIRMATION_TIMEOUT": "30s", "TASK_SERVER_TASKS_CONFIRMATION_THRESHOLDS_ETH": "0.001", "TASK_SERVER_TASKS_CONFIRMATION_THRESHOLDS_USDT": "1", "TASK_SERVER_TASKS_CONFIRMATION_THRESHOLDS_TRX": "1", "TASK_SERVER_TASKS_CONFIRMATION_CHAINS_ETH_ENABLED": true, "TASK_SERVER_TASKS_CONFIRMATION_CHAINS_TRON_ENABLED": true, "TASK_SERVER_MONITORING_METRICS_PORT": 9090, "TASK_SERVER_MONITORING_ALERTING_ERROR_THRESHOLD": 5, "TASK_SERVER_MONITORING_ALERTING_TIME_WINDOW": "5m"}