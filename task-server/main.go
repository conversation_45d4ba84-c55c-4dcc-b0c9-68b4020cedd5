package main

import (
	"context"               // 保留 context 包导入
	"task-api/internal/cmd" // 恢复导入 cmd 包以访问 Main

	_ "task-api/internal/logic/redis"
	_ "task-api/internal/logic/task"  // Import task logic to ensure registration
	_ "task-api/internal/logic/token" // Explicitly import token logic to ensure registration
	_ "task-api/internal/logic/user_recharges"

	_ "task-api/internal/packed"

	_ "github.com/gogf/gf/contrib/drivers/mysql/v2"
	_ "github.com/gogf/gf/contrib/nosql/redis/v2" // Add Redis adapter import
	// "github.com/gogf/gf/v2/os/gcmd" // 移除 gcmd 包导入
	// "github.com/gogf/gf/v2/os/gctx" // 确认 gctx 导入已移除
)

func main() {
	// 添加 Task, RunTask 和新的 ServeGrpc 命令
	cmd.Main.AddCommand(
		cmd.ServeGrpc, // 添加新的 gRPC 服务启动命令
	)
	// 运行主命令，它会自动处理子命令的解析和执行
	cmd.Main.Run(context.Background())
}
