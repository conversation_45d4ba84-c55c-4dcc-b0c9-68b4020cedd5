#构建 gRPC 服务镜像:
docker build --target grpc-server-final -t grpc-server-app:latest .



#构建 Task 调度器镜像:
docker build --target task-scheduler-final -t task-scheduler-app:latest .




启动 gRPC 服务容器:
docker run --rm  \
--name grpc-server-container \
-p 50051:50051 \
-v "$(pwd)/config_variables.json:/home/<USER>" \
-e PATH_TO_SECRET_FILE="/home/<USER>" \
grpc-server-app:latest


启动 Task 调度器容器:

docker run --rm  \
--name task-scheduler-container  \
-p 50051:50051 \
-v "$(pwd)/config_variables.json:/home/<USER>" \
-e PATH_TO_SECRET_FILE="/home/<USER>" \
task-scheduler-app:latest







(base) songjianping@songjianpingdeMac-mini task-server % grpcurl -plaintext -H "x-api-key: 01JT41XVG16HNHZ2FCEJECT7AH" -d '{}' localhost:50051 task.v1.TaskService/ListWithdrawals
{
  "code": 200,
  "message": "Success",
  "listWithdrawalsData": {
    "withdrawals": [
      {
        "userWithdrawsId": "5",
        "userId": "1",
        "tokenId": "1",
        "name": "ETH",
        "chan": "ETH",
        "orderNo": "W8a317db9",
        "address": "******************************************",
        "amount": 0.024,
        "handlingFee": 0.001,
        "actualAmount": 0.023,
        "status": "PENDING",
        "createdAt": "2025-05-01T05:27:58Z",
        "processingAt": "2025-05-01T05:27:58Z",
        "updatedAt": "2025-05-01T05:27:58Z"
      }
    ],
    "totalCount": 1,
    "currentPage": 1,
    "totalPages": 1
  }
}



(base) songjianping@songjianpingdeMac-mini task-server % grpcurl -plaintext -H "x-api-key: 01JT41XVG16HNHZ2FCEJECT7AH" -d '{"withdrawal_id": "5", "status": "PROCESSING"}' localhost:50051 task.v1.TaskService/UpdateWithdrawalStatus
{
  "code": 200,
  "message": "Status updated successfully",
  "successNoData": {}
}
(base) songjianping@songjianpingdeMac-mini task-server % 