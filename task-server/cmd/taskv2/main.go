package main

import (
	"context"
	"flag"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	_ "task-api/internal/boot"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/database/gredis"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/sirupsen/logrus"

	_ "github.com/gogf/gf/contrib/drivers/mysql/v2"
	_ "github.com/gogf/gf/contrib/nosql/redis/v2"

	"task-api/internal/service"
	"task-api/internal/taskv2/adapter"
	taskConfig "task-api/internal/taskv2/config"
	"task-api/internal/taskv2/interfaces"
	"task-api/internal/taskv2/manager"
	"task-api/internal/taskv2/monitoring"
	"task-api/internal/taskv2/scanners"
	"task-api/internal/taskv2/scanners/base"
	"task-api/internal/taskv2/storage"
	"task-api/internal/taskv2/tasks/confirmation"
	"task-api/internal/taskv2/types"

	_ "task-api/internal/logic/user_recharges"
)

var (
	configPath = flag.String("config", "manifest/config/config.yaml", "配置文件路径")
	logLevel   = flag.String("log-level", "info", "日志级别")
	dryRun     = flag.Bool("dry-run", false, "只打印配置，不运行")
)

func main() {
	flag.Parse()

	// 设置日志
	logger := setupLogger(*logLevel)
	logger.Info("starting taskv2", "version", "1.0.0")

	// 加载配置
	cfg, err := taskConfig.LoadConfig(*configPath)
	if err != nil {
		logger.Fatal("load config failed", "error", err)
	}
	err = gtime.SetTimeZone("Asia/Shanghai")
	if err != nil {
		panic(err)
	}
	// 合并默认值
	cfg = taskConfig.MergeWithDefaults(cfg)

	// 如果是dry-run模式，打印配置并退出
	if *dryRun {
		printConfig(cfg)
		return
	}

	// 创建依赖
	deps, err := createDependencies(cfg, logger)
	if err != nil {
		logger.Fatal("create dependencies failed", "error", err)
	}
	defer deps.Close()

	// 创建扫描器工厂
	factory := scanners.NewScannerFactory(
		cfg,
		deps.depositManager,
		deps.checkpointStorage,
		deps.dao,
		logger,
		deps.metrics,
	)

	// 创建所有扫描器
	allScanners, err := factory.CreateAllScanners()
	if err != nil {
		logger.Fatal("create scanners failed", "error", err)
	}

	// 创建任务管理器
	taskManager := manager.NewTaskManager(
		cfg,
		allScanners,
		deps.checkpointStorage,
		logger,
	)

	// 启动监控服务器
	go startMetricsServer(cfg.Monitoring.MetricsPort, logger)

	// 创建上下文
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 启动任务管理器
	if err := taskManager.Start(ctx); err != nil {
		logger.Fatal("start task manager failed", "error", err)
	}

	// 启动确认任务
	if deps.confirmationTask != nil {
		if err := deps.confirmationTask.Start(ctx); err != nil {
			logger.Fatal("start confirmation task failed", "error", err)
		}
		logger.Info("confirmation task started")
	}

	// 等待信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 等待退出信号或上下文取消
	select {
	case sig := <-sigChan:
		logger.Info("received signal", "signal", sig)
	case <-ctx.Done():
		logger.Info("context cancelled")
	}

	// 取消主上下文，触发所有任务停止
	cancel()

	// 优雅关闭
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()

	// 停止确认任务
	if deps.confirmationTask != nil {
		if err := deps.confirmationTask.Stop(shutdownCtx); err != nil {
			logger.Error("stop confirmation task failed", "error", err)
		}
	}

	// 停止任务管理器
	if err := taskManager.Stop(shutdownCtx); err != nil {
		logger.Error("stop task manager failed", "error", err)
	}

	logger.Info("taskv2 stopped")
}

// Dependencies 依赖项
type Dependencies struct {
	db                gdb.DB
	redisClient       *gredis.Redis
	dao               interfaces.DepositDAO
	depositManager    *base.DepositManager
	checkpointStorage *storage.RedisStorage
	metrics           *monitoring.Metrics
	confirmationTask  *confirmation.ConfirmationTask
}

// Close 关闭依赖
func (d *Dependencies) Close() {
	if d.db != nil {
		d.db.Close(context.Background())
	}
	if d.redisClient != nil {
		d.redisClient.Close(context.Background())
	}
}

// setupLogger 设置日志器
func setupLogger(level string) *logrus.Logger {
	logger := logrus.New()
	logger.SetFormatter(&logrus.TextFormatter{
		TimestampFormat: "2006-01-02 15:04:05",
		FullTimestamp:   true,
	})

	logLevel, err := logrus.ParseLevel(level)
	if err != nil {
		logLevel = logrus.InfoLevel
	}
	logger.SetLevel(logLevel)

	return logger
}

// createDependencies 创建依赖项
func createDependencies(cfg *types.Config, logger *logrus.Logger) (*Dependencies, error) {
	deps := &Dependencies{}

	db := g.DB()
	if db == nil {
		panic("database connection failed")
	}
	deps.db = db
	// 使用适配器包装现有的 user_recharges 服务
	deps.dao = adapter.NewUserRechargeDepositDAO(service.UserRecharges())

	// 直接使用GoFrame Redis，它会从配置文件读取配置
	// 或者可以通过环境变量设置
	deps.redisClient = g.Redis()

	// 测试Redis连接
	if deps.redisClient == nil {
		logger.Warn("redis client is nil, using memory storage")
		// 这里可以使用内存存储作为后备
	}

	// 创建存储
	deps.checkpointStorage = storage.NewRedisStorage(deps.redisClient, &cfg.Storage.Redis)

	// 创建存款管理器
	deps.depositManager = base.NewDepositManager(deps.dao, logger)

	// 创建监控指标
	deps.metrics = monitoring.NewMetrics()

	// 创建区块链RPC客户端
	blockchainRPCs, err := confirmation.CreateBlockchainRPCs(cfg.Chains, logger)
	if err != nil {
		logger.WithError(err).Warn("failed to create all blockchain RPCs")
	}

	// 创建确认任务（如果配置启用）
	if cfg.Tasks.Confirmation.Enabled {
		deps.confirmationTask = confirmation.NewConfirmationTask(
			&cfg.Tasks.Confirmation,
			cfg.Chains,
			deps.dao,
			blockchainRPCs,
			deps.metrics,
			logger,
		)
	}

	return deps, nil
}

// startMetricsServer 启动监控服务器
func startMetricsServer(port int, logger *logrus.Logger) {
	http.Handle("/metrics", promhttp.Handler())

	addr := fmt.Sprintf(":%d", port)
	logger.Info("starting metrics server", "address", addr)

	if err := http.ListenAndServe(addr, nil); err != nil {
		logger.Error("metrics server failed", "error", err)
	}
}

// printConfig 打印配置
func printConfig(cfg *types.Config) {
	fmt.Println("=== Task v2 Configuration ===")
	fmt.Printf("Global:\n")
	fmt.Printf("  Log Level: %s\n", cfg.Global.LogLevel)
	fmt.Printf("  Scan Interval: %s\n", cfg.Global.ScanInterval)
	fmt.Printf("  Health Check Interval: %s\n", cfg.Global.HealthCheckInterval)
	fmt.Printf("  Max Retries: %d\n", cfg.Global.MaxRetries)
	fmt.Printf("  Request Timeout: %s\n", cfg.Global.RequestTimeout)

	fmt.Printf("\nChains:\n")
	for name, chain := range cfg.Chains {
		fmt.Printf("  %s:\n", name)
		fmt.Printf("    Enabled: %v\n", chain.Enabled)
		fmt.Printf("    RPC URL: %s\n", chain.RpcURL)
		fmt.Printf("    Scan Batch Size: %d\n", chain.ScanBatchSize)
		fmt.Printf("    Native Token: %s (decimals: %d, confirmations: %d)\n",
			chain.Native.Symbol, chain.Native.Decimals, chain.Native.Confirmations)
		fmt.Printf("    Tokens: %d\n", len(chain.Tokens))
	}

	fmt.Printf("\nStorage:\n")
	fmt.Printf("  Redis Key Prefix: %s\n", cfg.Storage.Redis.KeyPrefix)
	fmt.Printf("  Lock Timeout: %s\n", cfg.Storage.Redis.LockTimeout)

	fmt.Printf("\nMonitoring:\n")
	fmt.Printf("  Metrics Port: %d\n", cfg.Monitoring.MetricsPort)
	fmt.Printf("  Error Threshold: %d\n", cfg.Monitoring.Alerting.ErrorThreshold)
	fmt.Printf("  Time Window: %s\n", cfg.Monitoring.Alerting.TimeWindow)

	fmt.Printf("\nTasks:\n")
	fmt.Printf("  Confirmation Task:\n")
	fmt.Printf("    Enabled: %v\n", cfg.Tasks.Confirmation.Enabled)
	fmt.Printf("    Interval: %s\n", cfg.Tasks.Confirmation.Interval)
	fmt.Printf("    Batch Size: %d\n", cfg.Tasks.Confirmation.BatchSize)
	fmt.Printf("    Max Retries: %d\n", cfg.Tasks.Confirmation.MaxRetries)
	fmt.Printf("    Timeout: %s\n", cfg.Tasks.Confirmation.Timeout)
	fmt.Printf("    Thresholds: %d tokens configured\n", len(cfg.Tasks.Confirmation.Thresholds))
}
