package main

import (
	_ "task-api/internal/boot"
	"task-api/internal/cmd" // 确保这是正确的模块路径
	_ "task-api/internal/logic/redis"
	_ "task-api/internal/logic/task" // Import task logic to ensure registration

	// "github.com/gogf/gf/v2/os/gcmd" // 移除未使用的导入

	_ "task-api/internal/logic/token" // Explicitly import token logic to ensure registration

	_ "task-api/internal/logic/user_recharges"

	_ "task-api/internal/packed"

	// "github.com/gogf/gf/v2/os/gcmd" // 移除 gcmd 包导入
	// "github.com/gogf/gf/v2/os/gctx" // 确认 gctx 导入已移除

	_ "github.com/gogf/gf/contrib/drivers/mysql/v2"
	_ "github.com/gogf/gf/contrib/nosql/redis/v2" // Add Redis adapter import

	// "github.com/gogf/gf/v2/os/gcmd" // 移除未使用的导入
	"github.com/gogf/gf/v2/os/gctx"
)

func main() {
	// 直接使用 ServeGrpc 命令定义。
	// 运行这个 main 函数将执行 ServeGrpc 命令的逻辑。
	// gcmd 会自动处理命令行参数解析。
	command := cmd.ServeGrpc
	command.Run(gctx.New())
}
