# Scanner 修复说明

## 问题分析

### ETH 扫描器问题
1. **区块不存在错误**: `get block 22997818: not found`
2. **根本原因**: 扫描器获取的"当前区块"可能由于网络延迟或节点同步问题而不可用
3. **影响**: 扫描器继续尝试不存在的区块，浪费资源并产生大量错误日志

### TRON 扫描器问题  
1. **严重滞后**: `block lag too high: 67647`
2. **根本原因**: 
   - TRON 区块生成速度快（3秒一个区块）
   - 批量大小可能不足以追赶积压
   - 频率限制可能过于严格
3. **影响**: 无法及时处理充值，用户体验差

## 修复方案

### ETH 扫描器修复

1. **添加区块验证**:
   ```go
   // 验证当前区块的合理性
   if err := s.validateCurrentBlock(ctx, currentBlock); err != nil {
       s.Logger().Warn("current block validation failed, using conservative approach", "error", err)
       // 使用更保守的方法：减去安全边距
       if currentBlock > 5 {
           currentBlock = currentBlock - 5
       }
   }
   ```

2. **改进错误处理**:
   ```go
   // 检查是否是区块不存在的错误
   if strings.Contains(err.Error(), "not found") || strings.Contains(err.Error(), "does not exist") {
       s.Logger().Warn("block not found, stopping scan to avoid further errors", 
           "block", blockNum,
           "lastProcessed", lastProcessedBlock)
       // 区块不存在，停止扫描并返回，避免继续尝试不存在的区块
       return nil
   }
   ```

3. **添加区块验证方法**:
   ```go
   func (s *ETHScanner) validateCurrentBlock(ctx context.Context, blockNumber uint64) error {
       // 尝试获取区块头来验证区块是否真的存在
       _, err := s.client.HeaderByNumber(ctx, big.NewInt(int64(blockNumber)))
       if err != nil {
           return fmt.Errorf("block %d not available: %w", blockNumber, err)
       }
       return nil
   }
   ```

### TRON 扫描器修复

1. **改进动态批量大小**:
   ```go
   switch {
   case totalBlocks > 50000:
       return baseBatchSize * 20  // 极严重积压：大幅增大批量
   case totalBlocks > 10000:
       return baseBatchSize * 10  // 严重积压：大幅增大批量
   case totalBlocks > 5000:
       return baseBatchSize * 5   // 中等积压：增大批量
   // ...
   }
   ```

2. **添加滞后检测**:
   ```go
   if currentBlock > lastProcessedBlock {
       lag := currentBlock - lastProcessedBlock
       if lag > 100000 { // 如果滞后超过10万个区块
           s.Logger().Warn("severe block lag detected, using catch-up strategy", 
               "lag", lag, 
               "currentBlock", currentBlock,
               "lastProcessed", lastProcessedBlock)
       }
   }
   ```

3. **添加进度报告**:
   ```go
   // 定期报告进度
   if blockNum%100 == 0 {
       progress := float64(blockNum-scanFromBlock+1) / float64(toBlock-scanFromBlock+1) * 100
       s.Logger().Info("scan progress", 
           "block", blockNum,
           "progress", fmt.Sprintf("%.1f%%", progress),
           "remaining", toBlock-blockNum)
   }
   ```

### 健康检查修复

1. **调整滞后阈值**:
   ```go
   // 根据链类型设置不同的阈值
   var lagThreshold uint64 = 100 // 默认阈值
   if s.chainName == "TRON" {
       lagThreshold = 1000 // TRON 允许更大的滞后，因为区块生成更快
   }
   ```

## 预期效果

1. **ETH 扫描器**: 
   - 减少"区块不存在"错误
   - 更稳定的扫描过程
   - 避免无效的重试

2. **TRON 扫描器**:
   - 更快的追赶速度
   - 更好的进度可见性
   - 减少健康检查警告

3. **整体系统**:
   - 更合理的健康检查阈值
   - 更好的错误处理
   - 更稳定的运行状态

## 建议的后续优化

1. **监控改进**: 添加更详细的指标监控
2. **配置优化**: 根据实际网络情况调整批量大小和频率限制
3. **告警机制**: 添加更智能的告警规则
4. **自动恢复**: 实现自动重启机制处理严重错误
