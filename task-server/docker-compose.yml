services:
  grpc-server:
    image: grpc-server-app:latest
    build:
      context: .
      target: grpc-server-final
    restart: unless-stopped
    volumes:
      - ./config_variables.json:/home/<USER>
      - ./logs:/home/<USER>
    environment:
      - PATH_TO_SECRET_FILE=/home/<USER>
    ports:
      - "50051:50051"
    networks:
      - xpay_app-network

  task-scheduler:
    image: task-scheduler-app:latest
    build:
      context: .
      target: task-scheduler-final
    restart: unless-stopped
    volumes:
      - ./config_variables.json:/home/<USER>
      - ./logs:/home/<USER>
    environment:
      - PATH_TO_SECRET_FILE=/home/<USER>
    networks:
      - xpay_app-network

networks:
  xpay_app-network:
    external: true