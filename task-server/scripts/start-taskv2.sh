#!/bin/bash

# Task v2 启动脚本

# 设置环境变量
export TASKV2_DB_DSN="${TASKV2_DB_DSN:-root:password@tcp(localhost:3306)/taskv2?charset=utf8mb4&parseTime=True&loc=Local}"
export TASKV2_REDIS_ADDR="${TASKV2_REDIS_ADDR:-localhost:6379}"
export TASKV2_REDIS_PASSWORD="${TASKV2_REDIS_PASSWORD:-}"

# 配置文件路径
CONFIG_FILE="${CONFIG_FILE:-manifest/config/taskv2.yaml}"

# 日志级别
LOG_LEVEL="${LOG_LEVEL:-info}"

# 构建程序
echo "Building taskv2..."
go build -o bin/taskv2 cmd/taskv2/main.go

if [ $? -ne 0 ]; then
    echo "Build failed!"
    exit 1
fi

echo "Build successful!"

# 检查是否是dry-run模式
if [ "$1" == "--dry-run" ]; then
    echo "Running in dry-run mode..."
    ./bin/taskv2 -config="$CONFIG_FILE" -log-level="$LOG_LEVEL" -dry-run
    exit 0
fi

# 运行程序
echo "Starting taskv2..."
echo "Config: $CONFIG_FILE"
echo "Log Level: $LOG_LEVEL"
echo "DB DSN: $TASKV2_DB_DSN"
echo "Redis: $TASKV2_REDIS_ADDR"

exec ./bin/taskv2 -config="$CONFIG_FILE" -log-level="$LOG_LEVEL"