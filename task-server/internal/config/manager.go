package config

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gogf/gf/v2/container/gvar"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/hashicorp/consul/api"
	"github.com/hashicorp/consul/api/watch"
	"github.com/shopspring/decimal"
)

// ConfigValueType 配置值类型
type ConfigValueType string

const (
	TypeText     ConfigValueType = "text"
	TypePassword ConfigValueType = "password"
	TypeBoolean  ConfigValueType = "boolean"
	TypeNumber   ConfigValueType = "number"
	TypeJSON     ConfigValueType = "json"
	TypeMap      ConfigValueType = "map"
	TypeArray    ConfigValueType = "array"
)

// ConfigData 存储在 Consul 中的配置数据结构
type ConfigData struct {
	Value     string          `json:"value"`
	ValueType ConfigValueType `json:"value_type"`
}

// ConfigManager manages configuration with Consul backend, caching, and watching
type ConfigManager struct {
	client      *api.Client
	kv          *api.KV
	prefix      string
	address     string      // Store address for watch
	token       string      // Store token for watch
	config      *api.Config // Store config for watch
	cache       sync.Map    // thread-safe cache
	updateHooks sync.Map    // map[string][]UpdateHook
	logger      glog.ILogger
	ctx         context.Context
	cancel      context.CancelFunc
}

// CachedValue represents a cached configuration value
type CachedValue struct {
	Value      interface{}
	RawValue   string
	UpdatedAt  time.Time
	ConfigData *ConfigData // For structured config values
}

// UpdateHook is a function called when a config value changes
type UpdateHook func(key string, oldValue, newValue interface{})

var (
	manager *ConfigManager
	once    sync.Once
)

// Initialize initializes the configuration system
func Initialize(ctx context.Context) error {
	var initErr error
	once.Do(func() {
		initErr = initManager(ctx)
	})
	return initErr
}

func initManager(ctx context.Context) error {
	// Get configuration from config file
	address := g.Cfg().MustGet(ctx, "consul.address", "127.0.0.1:8500").String()
	token := g.Cfg().MustGet(ctx, "consul.token", "").String()
	prefix := g.Cfg().MustGet(ctx, "consul.config_prefix", "xpay/config").String()

	// Create Consul client configuration
	config := api.DefaultConfig()
	config.Address = address
	if token != "" {
		config.Token = token
	}

	// Create Consul client
	client, err := api.NewClient(config)
	if err != nil {
		return fmt.Errorf("failed to create Consul client: %w", err)
	}

	// Create context for the manager
	ctx, cancel := context.WithCancel(ctx)

	// Create the manager
	manager = &ConfigManager{
		client:  client,
		kv:      client.KV(),
		prefix:  prefix,
		address: address,
		token:   token,
		config:  config,
		logger:  glog.New(),
		ctx:     ctx,
		cancel:  cancel,
	}

	// Load all configurations from the prefix
	if err := manager.loadAll(ctx); err != nil {
		cancel()
		return fmt.Errorf("failed to load initial configurations: %w", err)
	}

	// Start watching for changes
	go manager.watchPrefix()

	manager.logger.Infof(ctx, "Config manager initialized successfully with prefix: %s", prefix)
	return nil
}

// loadAll loads all configurations under the prefix
func (m *ConfigManager) loadAll(ctx context.Context) error {
	// List all keys under the prefix
	keys, _, err := m.kv.Keys(m.prefix+"/", "", nil)
	if err != nil {
		return fmt.Errorf("failed to list keys: %w", err)
	}

	m.logger.Debugf(ctx, "Found %d keys under prefix %s", len(keys), m.prefix)

	// Load each key
	for _, key := range keys {
		pair, _, err := m.kv.Get(key, nil)
		if err != nil {
			m.logger.Errorf(ctx, "Failed to get key %s: %v", key, err)
			continue
		}

		if pair != nil && pair.Value != nil {
			// Remove prefix from key for storage
			storeKey := strings.TrimPrefix(key, m.prefix+"/")
			m.storeValue(ctx, storeKey, string(pair.Value))
		}
	}

	return nil
}

// storeValue stores a value in cache and parses it if needed
func (m *ConfigManager) storeValue(ctx context.Context, key string, rawValue string) {
	// Get old value for comparison
	var oldValue interface{}
	if cached, ok := m.cache.Load(key); ok {
		if cv, ok := cached.(*CachedValue); ok {
			oldValue = cv.Value
		}
	}

	// Parse value
	var parsedValue interface{}
	var configData *ConfigData

	// Try to parse as ConfigData
	var cd ConfigData
	if err := json.Unmarshal([]byte(rawValue), &cd); err == nil {
		configData = &cd
		if parsed, err := ParseConfigValue(&cd); err == nil {
			parsedValue = parsed
		} else {
			parsedValue = rawValue
		}
	} else {
		parsedValue = rawValue
	}

	// Store in cache
	m.cache.Store(key, &CachedValue{
		Value:      parsedValue,
		RawValue:   rawValue,
		UpdatedAt:  time.Now(),
		ConfigData: configData,
	})

	// Trigger update hooks if value changed
	if oldValue != nil && fmt.Sprintf("%v", oldValue) != fmt.Sprintf("%v", parsedValue) {
		m.triggerUpdateHooks(ctx, key, oldValue, parsedValue)
	}

	m.logger.Debugf(ctx, "Stored config: %s = %s", key, rawValue)
}

// triggerUpdateHooks triggers update hooks for a key
func (m *ConfigManager) triggerUpdateHooks(ctx context.Context, key string, oldValue, newValue interface{}) {
	m.logger.Infof(ctx, "Config value changed for key %s: %v -> %v", key, oldValue, newValue)

	if hooks, ok := m.updateHooks.Load(key); ok {
		if hookList, ok := hooks.([]UpdateHook); ok {
			for _, hook := range hookList {
				go func(h UpdateHook) {
					defer func() {
						if r := recover(); r != nil {
							m.logger.Errorf(ctx, "Update hook panic for key %s: %v", key, r)
						}
					}()
					h(key, oldValue, newValue)
				}(hook)
			}
		}
	}
}

// watchPrefix watches for changes in the configuration prefix
func (m *ConfigManager) watchPrefix() {
	// Create a key prefix watch
	params := map[string]interface{}{
		"type":   "keyprefix",
		"prefix": m.prefix + "/",
		"token":  m.token,
	}

	plan, err := watch.Parse(params)
	if err != nil {
		m.logger.Errorf(m.ctx, "Failed to create watch plan: %v", err)
		return
	}

	// Set handler for changes
	plan.Handler = func(idx uint64, data interface{}) {
		if kvPairs, ok := data.(api.KVPairs); ok {
			m.handleWatchUpdate(kvPairs)
		}
	}

	// Create a context for the watch
	watchCtx, watchCancel := context.WithCancel(m.ctx)
	defer watchCancel()

	// Run the watch in a loop with retry logic
	for {
		select {
		case <-watchCtx.Done():
			m.logger.Info(m.ctx, "Watch context cancelled, stopping watch")
			return
		default:
			m.logger.Debug(m.ctx, "Starting watch on prefix:", m.prefix)
			if err := plan.RunWithConfig(m.address, m.config); err != nil {
				m.logger.Errorf(m.ctx, "Watch stopped with error: %v, retrying in 5 seconds", err)
				time.Sleep(5 * time.Second)
			}
		}
	}
}

// handleWatchUpdate handles updates from watch
func (m *ConfigManager) handleWatchUpdate(kvPairs api.KVPairs) {
	m.logger.Debugf(m.ctx, "Received %d updates from watch", len(kvPairs))

	// Update cache with new values
	for _, pair := range kvPairs {
		if pair != nil && pair.Value != nil {
			storeKey := strings.TrimPrefix(pair.Key, m.prefix+"/")
			m.storeValue(m.ctx, storeKey, string(pair.Value))
		}
	}
}

// Get retrieves a configuration value (returns raw JSON)
func (m *ConfigManager) Get(ctx context.Context, key string, def ...interface{}) (*gvar.Var, error) {
	if m == nil {
		return nil, fmt.Errorf("config manager not initialized")
	}

	// Check cache first
	if cached, ok := m.cache.Load(key); ok {
		if cv, ok := cached.(*CachedValue); ok {
			m.logger.Debugf(ctx, "Cache hit for key: %s", key)
			return gvar.New(cv.RawValue), nil
		}
	}

	// If not in cache, try to fetch from Consul
	fullKey := fmt.Sprintf("%s/%s", m.prefix, key)
	pair, _, err := m.kv.Get(fullKey, nil)
	if err != nil {
		m.logger.Errorf(ctx, "Failed to get key %s from Consul: %v", fullKey, err)
		if len(def) > 0 {
			return gvar.New(def[0]), nil
		}
		return nil, err
	}

	if pair == nil || pair.Value == nil {
		m.logger.Debugf(ctx, "Key %s not found in Consul", fullKey)
		if len(def) > 0 {
			return gvar.New(def[0]), nil
		}
		return gvar.New(nil), nil
	}

	// Store in cache
	value := string(pair.Value)
	m.storeValue(ctx, key, value)

	return gvar.New(value), nil
}

// GetValue retrieves the parsed value directly (not JSON)
func (m *ConfigManager) GetValue(ctx context.Context, key string, def ...interface{}) (interface{}, error) {
	if m == nil {
		return nil, fmt.Errorf("config manager not initialized")
	}

	// Check cache first
	if cached, ok := m.cache.Load(key); ok {
		if cv, ok := cached.(*CachedValue); ok {
			m.logger.Debugf(ctx, "Cache hit for key: %s (parsed value)", key)
			return cv.Value, nil
		}
	}

	// If not in cache, fetch and return parsed value
	_, err := m.Get(ctx, key, def...)
	if err != nil {
		return nil, err
	}

	// Now it should be in cache
	if cached, ok := m.cache.Load(key); ok {
		if cv, ok := cached.(*CachedValue); ok {
			return cv.Value, nil
		}
	}

	// Fallback to default
	if len(def) > 0 {
		return def[0], nil
	}
	return nil, nil
}

// GetBool retrieves a boolean configuration value
func (m *ConfigManager) GetBool(ctx context.Context, key string, def ...bool) (bool, error) {
	value, err := m.Get(ctx, key, def)
	if err != nil {
		return false, err
	}

	// Try to parse as ConfigData first
	rawStr := value.String()
	var configData ConfigData
	if err := json.Unmarshal([]byte(rawStr), &configData); err == nil {
		// It's a structured config value
		return GetBoolValue(&configData)
	}

	// Otherwise, convert directly
	return value.Bool(), nil
}

// GetString retrieves a string configuration value
func (m *ConfigManager) GetString(ctx context.Context, key string, def ...string) (string, error) {
	value, err := m.Get(ctx, key, def)
	if err != nil {
		return "", err
	}

	// Try to parse as ConfigData first
	rawStr := value.String()
	var configData ConfigData
	if err := json.Unmarshal([]byte(rawStr), &configData); err == nil {
		// It's a structured config value
		return GetStringValue(&configData), nil
	}

	// Otherwise, return as is
	return rawStr, nil
}

// GetInt retrieves an integer configuration value
func (m *ConfigManager) GetInt(ctx context.Context, key string, def ...int) (int, error) {
	value, err := m.Get(ctx, key, def)
	if err != nil {
		if len(def) > 0 {
			return def[0], nil
		}
		return 0, err
	}

	// Try to parse as ConfigData first
	rawStr := value.String()
	var configData ConfigData
	if err := json.Unmarshal([]byte(rawStr), &configData); err == nil {
		// It's a structured config value
		return GetIntValue(&configData)
	}

	// Otherwise, convert directly
	return value.Int(), nil
}

// GetFloat64 retrieves a float64 configuration value
func (m *ConfigManager) GetFloat64(ctx context.Context, key string, def ...float64) (float64, error) {
	value, err := m.Get(ctx, key, def)
	if err != nil {
		if len(def) > 0 {
			return def[0], nil
		}
		return 0, err
	}

	// Try to parse as ConfigData first
	rawStr := value.String()
	var configData ConfigData
	if err := json.Unmarshal([]byte(rawStr), &configData); err == nil {
		// It's a structured config value
		return GetFloat64Value(&configData)
	}

	// Otherwise, convert directly
	return value.Float64(), nil
}

// GetDecimal retrieves a decimal configuration value
func (m *ConfigManager) GetDecimal(ctx context.Context, key string, def ...decimal.Decimal) (decimal.Decimal, error) {
	value, err := m.Get(ctx, key, def)
	if err != nil {
		if len(def) > 0 {
			return def[0], nil
		}
		return decimal.Zero, err
	}

	// Try to parse as ConfigData first
	rawStr := value.String()
	var configData ConfigData
	if err := json.Unmarshal([]byte(rawStr), &configData); err == nil {
		// It's a structured config value
		return GetDecimalValue(&configData)
	}

	// Otherwise, parse directly
	return decimal.NewFromString(rawStr)
}

// GetMap retrieves a map configuration value
func (m *ConfigManager) GetMap(ctx context.Context, key string, def ...map[string]interface{}) (map[string]interface{}, error) {
	value, err := m.Get(ctx, key, def)
	if err != nil {
		if len(def) > 0 {
			return def[0], nil
		}
		return nil, err
	}

	// Try to parse as ConfigData first
	rawStr := value.String()
	var configData ConfigData
	if err := json.Unmarshal([]byte(rawStr), &configData); err == nil {
		// It's a structured config value
		return GetMapValue(&configData)
	}

	// Otherwise, parse as JSON
	var result map[string]interface{}
	if err := json.Unmarshal([]byte(rawStr), &result); err != nil {
		if len(def) > 0 {
			return def[0], nil
		}
		return nil, err
	}
	return result, nil
}

// Set sets a configuration value in Consul
func (m *ConfigManager) Set(ctx context.Context, key string, value interface{}) error {
	if m == nil {
		return fmt.Errorf("config manager not initialized")
	}

	// Convert value to string
	var strValue string
	switch v := value.(type) {
	case string:
		strValue = v
	case []byte:
		strValue = string(v)
	default:
		// Try JSON encoding for complex types
		data, err := json.Marshal(value)
		if err != nil {
			strValue = fmt.Sprintf("%v", value)
		} else {
			strValue = string(data)
		}
	}

	// Write to Consul
	fullKey := fmt.Sprintf("%s/%s", m.prefix, key)
	pair := &api.KVPair{
		Key:   fullKey,
		Value: []byte(strValue),
	}

	_, err := m.kv.Put(pair, nil)
	if err != nil {
		return fmt.Errorf("failed to set key %s: %w", fullKey, err)
	}

	// Update cache
	m.storeValue(ctx, key, strValue)
	m.logger.Infof(ctx, "Set config: %s = %s", key, strValue)

	return nil
}

// RegisterUpdateHook registers a callback for when a config value changes
func (m *ConfigManager) RegisterUpdateHook(key string, hook UpdateHook) {
	if m == nil {
		return
	}

	// Load existing hooks
	var hooks []UpdateHook
	if existing, ok := m.updateHooks.Load(key); ok {
		if existingHooks, ok := existing.([]UpdateHook); ok {
			hooks = existingHooks
		}
	}

	// Append new hook
	hooks = append(hooks, hook)
	m.updateHooks.Store(key, hooks)
}

// Refresh forces a reload of all configurations
func (m *ConfigManager) Refresh(ctx context.Context) error {
	if m == nil {
		return fmt.Errorf("config manager not initialized")
	}

	return m.loadAll(ctx)
}

// Close gracefully shuts down the manager
func (m *ConfigManager) Close() {
	if m != nil && m.cancel != nil {
		m.cancel()
		m.logger.Info(m.ctx, "Config manager closed")
	}
}

// Global convenience functions

// Get retrieves a configuration value using the global manager
func Get(ctx context.Context, key string, def ...interface{}) (*gvar.Var, error) {
	if err := Initialize(ctx); err != nil {
		return nil, err
	}
	value, err := manager.Get(ctx, key, def...)

	return value, err
}

// GetBool retrieves a boolean configuration value
func GetBool(ctx context.Context, key string, def ...bool) (bool, error) {
	if err := Initialize(ctx); err != nil {
		return false, err
	}
	return manager.GetBool(ctx, key, def...)
}

// GetString retrieves a string configuration value
func GetString(ctx context.Context, key string, def ...string) (string, error) {
	if err := Initialize(ctx); err != nil {
		return "", err
	}
	return manager.GetString(ctx, key, def...)
}

// GetInt retrieves an integer configuration value
func GetInt(ctx context.Context, key string, def ...int) (int, error) {
	if err := Initialize(ctx); err != nil {
		if len(def) > 0 {
			return def[0], nil
		}
		return 0, err
	}
	return manager.GetInt(ctx, key, def...)
}

// GetFloat64 retrieves a float64 configuration value
func GetFloat64(ctx context.Context, key string, def ...float64) (float64, error) {
	if err := Initialize(ctx); err != nil {
		if len(def) > 0 {
			return def[0], nil
		}
		return 0, err
	}
	return manager.GetFloat64(ctx, key, def...)
}

// GetDecimal retrieves a decimal configuration value
func GetDecimal(ctx context.Context, key string, def ...decimal.Decimal) (decimal.Decimal, error) {
	if err := Initialize(ctx); err != nil {
		if len(def) > 0 {
			return def[0], nil
		}
		return decimal.Zero, err
	}
	return manager.GetDecimal(ctx, key, def...)
}

// GetMap retrieves a map configuration value
func GetMap(ctx context.Context, key string, def ...map[string]interface{}) (map[string]interface{}, error) {
	if err := Initialize(ctx); err != nil {
		if len(def) > 0 {
			return def[0], nil
		}
		return nil, err
	}
	return manager.GetMap(ctx, key, def...)
}

// GetValue retrieves the parsed value directly (not JSON)
func GetValue(ctx context.Context, key string, def ...interface{}) (interface{}, error) {
	if err := Initialize(ctx); err != nil {
		return nil, err
	}
	return manager.GetValue(ctx, key, def...)
}

// Set sets a configuration value using the global manager
func Set(ctx context.Context, key string, value interface{}) error {
	if err := Initialize(ctx); err != nil {
		return err
	}
	return manager.Set(ctx, key, value)
}

// MustGet retrieves a configuration value and panics on error
func MustGet(ctx context.Context, key string, def ...interface{}) *gvar.Var {
	v, err := Get(ctx, key, def...)
	if err != nil {
		panic(err)
	}
	return v
}

// GetConsulManager returns the global manager instance (for compatibility)
func GetConsulManager() *ConfigManager {
	return manager
}

// GetEnhancedManager returns the global manager instance (for compatibility)
func GetEnhancedManager() *ConfigManager {
	return manager
}

// GetClient returns a compatibility layer (for legacy code)
func GetClient(ctx context.Context) (interface{}, error) {
	if err := Initialize(ctx); err != nil {
		return nil, err
	}
	return manager, nil
}

// ParseConfigValue 解析配置值并返回正确的类型
func ParseConfigValue(data *ConfigData) (interface{}, error) {
	if data == nil {
		return nil, gerror.New("配置数据为空")
	}

	switch data.ValueType {
	case TypeBoolean:
		return strconv.ParseBool(data.Value)

	case TypeNumber:
		// 使用 decimal 解析，保证精度
		dec, err := decimal.NewFromString(data.Value)
		if err != nil {
			return nil, gerror.Wrapf(err, "解析数字失败: %s", data.Value)
		}
		return dec, nil

	case TypeJSON, TypeMap, TypeArray:
		var result interface{}
		err := json.Unmarshal([]byte(data.Value), &result)
		return result, err

	case TypeText, TypePassword:
		fallthrough
	default:
		return data.Value, nil
	}
}

// GetStringValue 获取字符串值（所有类型都可以转为字符串）
func GetStringValue(data *ConfigData) string {
	if data == nil {
		return ""
	}
	return data.Value
}

// GetBoolValue 获取布尔值
func GetBoolValue(data *ConfigData) (bool, error) {
	if data == nil {
		return false, gerror.New("配置数据为空")
	}

	if data.ValueType == TypeBoolean {
		return strconv.ParseBool(data.Value)
	}

	// 尝试从其他类型转换
	switch data.Value {
	case "1", "true", "True", "TRUE", "yes", "Yes", "YES", "on", "On", "ON":
		return true, nil
	case "0", "false", "False", "FALSE", "no", "No", "NO", "off", "Off", "OFF":
		return false, nil
	default:
		return false, gerror.Newf("无法将 %s 转换为布尔值", data.Value)
	}
}

// GetIntValue 获取整数值
func GetIntValue(data *ConfigData) (int, error) {
	if data == nil {
		return 0, gerror.New("配置数据为空")
	}

	if data.ValueType == TypeNumber {
		// 优先尝试解析为整数
		if intVal, err := strconv.Atoi(data.Value); err == nil {
			return intVal, nil
		}
		// 如果是浮点数，转换为整数
		if floatVal, err := strconv.ParseFloat(data.Value, 64); err == nil {
			return int(floatVal), nil
		}
	}

	// 尝试从字符串转换
	return strconv.Atoi(data.Value)
}

// GetMapValue 获取 Map 值
func GetMapValue(data *ConfigData) (map[string]interface{}, error) {
	if data == nil {
		return nil, gerror.New("配置数据为空")
	}

	var result map[string]interface{}

	if data.ValueType == TypeMap || data.ValueType == TypeJSON {
		err := json.Unmarshal([]byte(data.Value), &result)
		return result, err
	}

	return nil, gerror.Newf("配置类型 %s 不是 Map", data.ValueType)
}

// GetDecimalValue 获取 Decimal 值（高精度数字）
func GetDecimalValue(data *ConfigData) (decimal.Decimal, error) {
	if data == nil {
		return decimal.Zero, gerror.New("配置数据为空")
	}

	// 不管什么类型，都尝试解析为 decimal
	dec, err := decimal.NewFromString(data.Value)
	if err != nil {
		return decimal.Zero, gerror.Wrapf(err, "无法解析为 Decimal: %s", data.Value)
	}

	return dec, nil
}

// GetFloat64Value 获取浮点数值
func GetFloat64Value(data *ConfigData) (float64, error) {
	if data == nil {
		return 0, gerror.New("配置数据为空")
	}

	if data.ValueType == TypeNumber {
		dec, err := decimal.NewFromString(data.Value)
		if err != nil {
			return 0, gerror.Wrapf(err, "解析数字失败: %s", data.Value)
		}
		return dec.InexactFloat64(), nil
	}

	// 尝试直接解析
	return strconv.ParseFloat(data.Value, 64)
}

// FormatDecimalValue 格式化 Decimal 值为字符串
func FormatDecimalValue(value decimal.Decimal) string {
	return value.String()
}
