package utility

import (
	// Keep context import
	"regexp"
	"strconv"
	"strings"
)

// ValidateAmountString 校验金额字符串是否有效。
// 有效的金额字符串必须：
// 1. 非空。
// 2. 长度不超过 30 个字符。
// 3. 只包含数字 '0'-'9' 和小数点。
// 4. 不能有无效的前导零（例如 "01", "007" 无效，但 "0" 和 "0.1" 有效）。
// 5. 金额不能小于 0.001。
// 6. 金额不能超过 1000000。
// 7. 金额不能为 0。
// 8. 小数位数不能超过 3 位。
// ValidateAmountString 校验金额字符串是否有效。
// 返回 isValid 和一个错误 key（如果无效）。
func ValidateAmountString(input string) (isValid bool, errorKey string) {
	if input == "" {
		return false, "validation.amount.required" // Input cannot be empty
	}

	if len(input) > 30 {
		return false, "validation.amount.tooLong" // Input is too long
	}

	// 检查是否只包含数字和小数点
	match, _ := regexp.MatchString("^[0-9]+(\\.[0-9]+)?$", input)
	if !match {
		return false, "validation.amount.invalidChars" // Must contain only digits and decimal point
	}

	// 检查无效的前导零
	// 允许单个 "0" 或者 "0." 开头的小数，但不允许 "0" 开头的多位整数
	if len(input) > 1 && strings.HasPrefix(input, "0") && !strings.HasPrefix(input, "0.") {
		return false, "validation.amount.leadingZero" // Invalid leading zero
	}

	// 转换为浮点数进行数值验证
	amount, err := strconv.ParseFloat(input, 64)
	if err != nil {
		return false, "validation.amount.invalidFormat" // Invalid format
	}

	// 检查是否为零
	if amount == 0 {
		return false, "validation.amount.zero" // Amount cannot be zero
	}

	// 检查是否为负数
	if amount < 0 {
		return false, "validation.amount.negative" // Amount cannot be negative
	}

	// 检查最小值
	if amount < 0.0001 {
		return false, "validation.amount.tooSmall" // Amount too small
	}

	// 检查最大值
	if amount > 100000000 {
		return false, "validation.amount.tooLarge" // Amount too large
	}

	// 检查小数位数
	parts := strings.Split(input, ".")
	if len(parts) > 1 && len(parts[1]) > 3 {
		return false, "validation.amount.tooManyDecimals" // Too many decimal places
	}

	// 注意：ctx 参数暂时未使用，但保留以备将来扩展
	return true, ""
}
