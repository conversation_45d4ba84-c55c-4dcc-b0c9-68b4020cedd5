package crypto

import (
	"context"
	"sync"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/shopspring/decimal"
)

var (
	managerInstance *Manager
	managerOnce     sync.Once
)

// Manager implements BlockchainManager interface
type Manager struct {
	clients map[ChainType]BlockchainClient
	configs map[ChainType]*ClientConfig
	mutex   sync.RWMutex
}

// GetManager returns the singleton instance of Manager
func GetManager() *Manager {
	managerOnce.Do(func() {
		managerInstance = &Manager{
			clients: make(map[ChainType]BlockchainClient),
			configs: make(map[ChainType]*ClientConfig),
		}
		
		// Initialize with default configurations
		managerInstance.loadConfigurations()
	})
	return managerInstance
}

// loadConfigurations loads blockchain configurations from config
func (m *Manager) loadConfigurations() {
	ctx := context.Background()
	
	// Load ETH configuration
	ethConfig := &ClientConfig{
		ChainType: ChainETH,
		RPCUrl:    g.Cfg().MustGet(ctx, "blockchain.ETH.rpcUrl", "").String(),
		ContractAddress: g.Cfg().MustGet(ctx, "blockchain.ETH.erc20ContractAddress", "").String(),
	}
	if ethConfig.RPCUrl != "" {
		m.configs[ChainETH] = ethConfig
	}
	
	// Load TRON configuration
	tronConfig := &ClientConfig{
		ChainType: ChainTRON,
		RPCUrl:    g.Cfg().MustGet(ctx, "blockchain.TRON.rpcUrl", "").String(),
		BackupRPCUrls: g.Cfg().MustGet(ctx, "tron_grpc_backup_urls", []string{}).Strings(),
		APIKey:    g.Cfg().MustGet(ctx, "blockchain.TRON.apiKey", "").String(),
		ContractAddress: g.Cfg().MustGet(ctx, "blockchain.TRON.trc20ContractAddress", "").String(),
		UseTLS:    g.Cfg().MustGet(ctx, "blockchain.TRON.useTLS", true).Bool(), // Default to true for mainnet
	}
	if tronConfig.RPCUrl != "" {
		m.configs[ChainTRON] = tronConfig
	}
}

// GetClient returns a blockchain client for the specified chain type
func (m *Manager) GetClient(chainType ChainType) (BlockchainClient, error) {
	m.mutex.RLock()
	client, exists := m.clients[chainType]
	m.mutex.RUnlock()
	
	if exists {
		return client, nil
	}
	
	// Create client if not exists
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	// Double-check in case another goroutine created it
	client, exists = m.clients[chainType]
	if exists {
		return client, nil
	}
	
	// Get configuration
	config, configExists := m.configs[chainType]
	if !configExists {
		return nil, NewBlockchainError(
			ErrCodeUnsupportedOperation,
			"blockchain not configured",
			chainType,
		)
	}
	
	// Create new client based on chain type
	var newClient BlockchainClient
	var err error
	
	switch chainType {
	case ChainETH:
		newClient, err = NewETHClient(config)
	case ChainTRON:
		newClient, err = NewTRONClient(config)
	default:
		return nil, NewBlockchainError(
			ErrCodeUnsupportedOperation,
			"unsupported blockchain type",
			chainType,
		)
	}
	
	if err != nil {
		return nil, err
	}
	
	m.clients[chainType] = newClient
	glog.Infof(context.Background(), "Created new blockchain client for: %s", chainType)
	
	return newClient, nil
}

// RegisterClient registers a blockchain client
func (m *Manager) RegisterClient(chainType ChainType, client BlockchainClient) error {
	if client == nil {
		return NewBlockchainError(
			ErrCodeInvalidAddress,
			"client cannot be nil",
			chainType,
		)
	}
	
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	m.clients[chainType] = client
	glog.Infof(context.Background(), "Registered blockchain client for: %s", chainType)
	
	return nil
}

// GetBalance gets balance for a specific address on a specific chain
func (m *Manager) GetBalance(ctx context.Context, chainType ChainType, address string, tokenType TokenType, contractAddress ...string) (*Balance, error) {
	client, err := m.GetClient(chainType)
	if err != nil {
		return nil, err
	}
	
	switch tokenType {
	case TokenNative:
		return client.GetNativeBalance(ctx, address)
	case TokenERC20, TokenTRC20:
		contractAddr := ""
		if len(contractAddress) > 0 {
			contractAddr = contractAddress[0]
		} else {
			// Use default contract address from config
			config, exists := m.configs[chainType]
			if exists && config.ContractAddress != "" {
				contractAddr = config.ContractAddress
			}
		}
		if contractAddr == "" {
			return nil, NewBlockchainError(
				ErrCodeInvalidAddress,
				"contract address is required for token balance",
				chainType,
			)
		}
		return client.GetTokenBalance(ctx, address, contractAddr)
	default:
		return nil, NewBlockchainError(
			ErrCodeUnsupportedOperation,
			"unsupported token type",
			chainType,
		)
	}
}

// SendTransaction sends a transaction on a specific chain
func (m *Manager) SendTransaction(ctx context.Context, chainType ChainType, req *SendTransactionRequest) (*Transaction, error) {
	client, err := m.GetClient(chainType)
	if err != nil {
		return nil, err
	}
	
	// Validate request
	if err := m.validateSendRequest(chainType, req); err != nil {
		return nil, err
	}
	
	return client.SendTransaction(ctx, req)
}

// EstimateFee estimates transaction fee
func (m *Manager) EstimateFee(ctx context.Context, chainType ChainType, req *SendTransactionRequest) (*FeeEstimate, error) {
	client, err := m.GetClient(chainType)
	if err != nil {
		return nil, err
	}
	
	// Validate request
	if err := m.validateSendRequest(chainType, req); err != nil {
		return nil, err
	}
	
	return client.EstimateFee(ctx, req)
}

// validateSendRequest validates a send transaction request
func (m *Manager) validateSendRequest(chainType ChainType, req *SendTransactionRequest) error {
	if req == nil {
		return NewBlockchainError(
			ErrCodeInvalidAddress,
			"send request cannot be nil",
			chainType,
		)
	}
	
	if req.PrivateKey == "" {
		return NewBlockchainError(
			ErrCodeInvalidPrivateKey,
			"private key is required",
			chainType,
		)
	}
	
	if req.ToAddress == "" {
		return NewBlockchainError(
			ErrCodeInvalidAddress,
			"to address is required",
			chainType,
		)
	}
	
	if req.Amount.LessThanOrEqual(decimal.Zero) {
		return NewBlockchainError(
			ErrCodeInvalidAddress,
			"amount must be greater than zero",
			chainType,
		)
	}
	
	// Validate token-specific requirements
	if req.TokenType == TokenERC20 || req.TokenType == TokenTRC20 {
		if req.ContractAddress == "" {
			// Try to use default contract address
			config, exists := m.configs[chainType]
			if !exists || config.ContractAddress == "" {
				return NewBlockchainError(
					ErrCodeInvalidAddress,
					"contract address is required for token transactions",
					chainType,
				)
			}
			req.ContractAddress = config.ContractAddress
		}
	}
	
	return nil
}

// SupportedChains returns list of supported chain types
func (m *Manager) SupportedChains() []ChainType {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	
	chains := make([]ChainType, 0, len(m.configs))
	for chainType := range m.configs {
		chains = append(chains, chainType)
	}
	
	return chains
}

// ValidateChainAddress validates an address for a specific chain
func (m *Manager) ValidateChainAddress(chainType ChainType, address string) bool {
	client, err := m.GetClient(chainType)
	if err != nil {
		return false
	}
	
	return client.ValidateAddress(address)
}

// GetConfig returns configuration for a specific chain
func (m *Manager) GetConfig(chainType ChainType) (*ClientConfig, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	
	config, exists := m.configs[chainType]
	if !exists {
		return nil, NewBlockchainError(
			ErrCodeUnsupportedOperation,
			"blockchain not configured",
			chainType,
		)
	}
	
	return config, nil
}

// UpdateConfig updates configuration for a specific chain
func (m *Manager) UpdateConfig(chainType ChainType, config *ClientConfig) error {
	if config == nil {
		return NewBlockchainError(
			ErrCodeInvalidAddress,
			"config cannot be nil",
			chainType,
		)
	}
	
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	m.configs[chainType] = config
	
	// Remove existing client to force recreation with new config
	delete(m.clients, chainType)
	
	glog.Infof(context.Background(), "Updated configuration for blockchain: %s", chainType)
	
	return nil
}

// CloseClient closes and removes a client
func (m *Manager) CloseClient(chainType ChainType) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	client, exists := m.clients[chainType]
	if !exists {
		return nil
	}
	
	// If client implements io.Closer, close it
	if closer, ok := client.(interface{ Close() error }); ok {
		if err := closer.Close(); err != nil {
			glog.Warningf(context.Background(), "Error closing client for %s: %v", chainType, err)
		}
	}
	
	delete(m.clients, chainType)
	glog.Infof(context.Background(), "Closed client for blockchain: %s", chainType)
	
	return nil
}

// CloseAllClients closes all clients
func (m *Manager) CloseAllClients() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	for chainType, client := range m.clients {
		if closer, ok := client.(interface{ Close() error }); ok {
			if err := closer.Close(); err != nil {
				glog.Warningf(context.Background(), "Error closing client for %s: %v", chainType, err)
			}
		}
		glog.Infof(context.Background(), "Closed client for blockchain: %s", chainType)
	}
	
	m.clients = make(map[ChainType]BlockchainClient)
	
	return nil
}

