package eth

import (
	"context"
	"fmt"
	"math/big"

	"github.com/gogf/gf/v2/frame/g"

	walletCrypto "task-api/internal/utility/crypto" // Added import

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/common"
	"github.com/shopspring/decimal"
	"golang.org/x/crypto/sha3"
)

// EstimateERC20TransferGas estimates the gas required for an ERC20 token transfer
// and returns the estimated fee in ETH, gas price in Gwei, and gas limit
func EstimateERC20TransferGas(ctx context.Context, fromAddress, toAddress, contractAddress string) (totalEth decimal.Decimal, priceGwei decimal.Decimal, limit decimal.Decimal, err error) {
	const (
		// GweiDecimals is the number of decimals for Gwei
		GweiDecimals = 9 // 10^9 Gwei = 1 ETH, 1 Wei * 10^9 = 1 Gwei
	)

	client, err := walletCrypto.GetInstance().GetDefaultEthClient(ctx)
	if err != nil {
		return decimal.Zero, decimal.Zero, decimal.Zero, fmt.Errorf("failed to get Ethereum client: %w", err)
	}

	// Validate addresses
	if !common.IsHexAddress(fromAddress) {
		return decimal.Zero, decimal.Zero, decimal.Zero, fmt.Errorf("invalid from address: %s", fromAddress)
	}
	if !common.IsHexAddress(toAddress) {
		return decimal.Zero, decimal.Zero, decimal.Zero, fmt.Errorf("invalid to address: %s", toAddress)
	}
	if !common.IsHexAddress(contractAddress) {
		return decimal.Zero, decimal.Zero, decimal.Zero, fmt.Errorf("invalid contract address: %s", contractAddress)
	}

	fromAddr := common.HexToAddress(fromAddress)
	toAddr := common.HexToAddress(toAddress)
	contractAddr := common.HexToAddress(contractAddress)

	transferFnSignature := []byte("transfer(address,uint256)")
	hash := sha3.NewLegacyKeccak256()
	hash.Write(transferFnSignature)
	methodID := hash.Sum(nil)[:4]

	paddedAddress := common.LeftPadBytes(toAddr.Bytes(), 32)
	dummyAmount := big.NewInt(1)
	paddedAmount := common.LeftPadBytes(dummyAmount.Bytes(), 32)

	var data []byte
	data = append(data, methodID...)
	data = append(data, paddedAddress...)
	data = append(data, paddedAmount...)

	gasPriceWei, err := client.SuggestGasPrice(ctx)
	if err != nil {
		return decimal.Zero, decimal.Zero, decimal.Zero, fmt.Errorf("failed to get gas price: %w", err)
	}

	// Conversion factor for Wei to Gwei (10^9)
	// Wei * 10^GweiDecimals = 1 Gwei, so GasPriceInGwei = GasPriceInWei / 10^GweiDecimals
	// 1 ETH = 10^GweiDecimals Gwei
	// Corrected line:
	weiToGweiFactor := decimal.NewFromInt(1_000_000_000) // This is 10^GweiDecimals (10^9)

	// Convert gas price from Wei to Gwei
	gasPriceInGwei := decimal.NewFromBigInt(gasPriceWei, 0).Div(weiToGweiFactor)

	estimatedGasLimit, err := client.EstimateGas(ctx, ethereum.CallMsg{
		From: fromAddr,
		To:   &contractAddr,
		Data: data,
	})
	if err != nil {
		return decimal.Zero, decimal.Zero, decimal.Zero, fmt.Errorf("failed to estimate gas for ERC20 transfer: %w", err)
	}

	// Log raw estimated values before buffering
	logger := g.Log() // Get default logger instance
	logger.Infof(ctx, "[EstimateERC20TransferGas] Raw Suggested Gas Price (Wei): %s, Raw Estimated Gas Limit (units): %d", gasPriceWei.String(), estimatedGasLimit)

	bufferedGasLimit := decimal.NewFromUint64(estimatedGasLimit).Mul(decimal.NewFromFloat(1.9)) // Changed buffer from 1.2 to 1.0

	// Calculate total fee in ETH
	// TotalFeeInETH = bufferedGasLimit (GasUnits) * gasPriceInGwei (Gwei/GasUnit) / (Gwei/ETH factor)
	// The factor to convert Gwei to ETH is 10^9 (since 1 ETH = 10^9 Gwei)
	// This is the same as weiToGweiFactor in this context because GweiDecimals = 9
	gweiToEthFactor := weiToGweiFactor
	totalFeeInEth := bufferedGasLimit.Mul(gasPriceInGwei).Div(gweiToEthFactor) // Convert Gwei to ETH

	return totalFeeInEth, gasPriceInGwei, bufferedGasLimit, nil
}

// SimulateERC20Transfer simulates an ERC20 transfer to check if it would succeed.
// This is primarily used to verify if the transaction would revert due to on-chain conditions
// (e.g., insufficient token balance from the sender, or other contract-specific logic).
// The 'amount' parameter is a *big.Int as it directly corresponds to the uint256 type expected by the ERC20 transfer function in Solidity.
func SimulateERC20Transfer(ctx context.Context, fromAddress, toAddress, contractAddress string, amount *big.Int) error {
	// Get Ethereum client from ClientManager
	client, err := walletCrypto.GetInstance().GetDefaultEthClient(ctx)
	if err != nil {
		return fmt.Errorf("failed to get Ethereum client: %w", err)
	}
	// Client lifecycle is assumed to be managed by ClientManager, so no defer client.Close() here.

	// Validate addresses
	if !common.IsHexAddress(fromAddress) {
		return fmt.Errorf("invalid from address: %s", fromAddress)
	}
	if !common.IsHexAddress(toAddress) {
		return fmt.Errorf("invalid to address: %s", toAddress)
	}
	if !common.IsHexAddress(contractAddress) {
		return fmt.Errorf("invalid contract address: %s", contractAddress)
	}

	// Convert addresses to common.Address
	fromAddr := common.HexToAddress(fromAddress)
	toAddr := common.HexToAddress(toAddress)
	contractAddr := common.HexToAddress(contractAddress)

	// Prepare ERC20 transfer calldata
	// Method signature: transfer(address,uint256)
	transferFnSignature := []byte("transfer(address,uint256)")
	hash := sha3.NewLegacyKeccak256()
	hash.Write(transferFnSignature)
	methodID := hash.Sum(nil)[:4] // First 4 bytes of the hash

	// Pad the recipient address to 32 bytes
	paddedToAddress := common.LeftPadBytes(toAddr.Bytes(), 32)

	// Ensure amount is not nil
	if amount == nil {
		return fmt.Errorf("amount cannot be nil")
	}
	// Pad the amount to 32 bytes. amount.Bytes() gives big-endian byte representation.
	paddedAmount := common.LeftPadBytes(amount.Bytes(), 32)

	// Concatenate method ID, padded recipient address, and padded amount to form the data
	var data []byte
	data = append(data, methodID...)
	data = append(data, paddedToAddress...)
	data = append(data, paddedAmount...)

	// Perform the read-only call to the contract (simulation)
	// CallContract executes a message call transaction, which is directly executed in the VM
	// of the node, but never mined into the blockchain.
	// The final parameter `nil` means the call will be executed at the latest known block.
	_, err = client.CallContract(ctx, ethereum.CallMsg{
		From: fromAddr,      // The sender of the simulated transaction
		To:   &contractAddr, // The contract to call
		Data: data,          // The transaction data (method and parameters)
		// Gas, GasPrice, Value can be zero for typical eth_call simulations of token transfers,
		// unless the contract specifically requires ETH to be sent or has gas-dependent logic
		// that needs to be precisely simulated with specific gas values.
	}, nil) // Block number, nil for latest

	if err != nil {
		// The error from CallContract often includes reasons for revert if the call would fail.
		return fmt.Errorf("ERC20 transfer simulation failed: %w", err)
	}

	// If err is nil, the simulation was successful (the call would not revert).
	return nil
}

// EstimateNativeEthTransferFee estimates the gas fee for a native ETH transfer.
// It returns the estimated total fee in ETH, the gas price in Gwei,
// and the fixed gas limit for native ETH transfers.
func EstimateNativeEthTransferFee(ctx context.Context) (totalFeeEth decimal.Decimal, gasPriceGwei decimal.Decimal, gasLimitUnits decimal.Decimal, err error) {
	const (
		// NativeEthGasLimit is the standard gas limit for a basic native ETH transfer.
		NativeEthGasLimit int64 = 21000
		// GweiDecimals is the number of decimal places for Gwei relative to Wei (1 Gwei = 10^9 Wei).
		// Also, 1 ETH = 10^9 Gwei.
		GweiDecimals = 9
	)

	// Get Ethereum client
	client, err := walletCrypto.GetInstance().GetDefaultEthClient(ctx)
	if err != nil {
		return decimal.Zero, decimal.Zero, decimal.Zero, fmt.Errorf("failed to get Ethereum client: %w", err)
	}

	// Get current suggested gas price from the network (in Wei)
	gasPriceWeiBigInt, err := client.SuggestGasPrice(ctx)
	if err != nil {
		return decimal.Zero, decimal.Zero, decimal.Zero, fmt.Errorf("failed to suggest gas price: %w", err)
	}

	// Factor for converting Wei to Gwei, and Gwei to ETH
	conversionFactor := decimal.NewFromInt(1_000_000_000) // 10^9

	// Convert gas price from Wei to Gwei using decimal arithmetic
	gasPriceWeiDecimal := decimal.NewFromBigInt(gasPriceWeiBigInt, 0)
	// gasPriceGwei = gasPriceWei / 10^9
	calculatedGasPriceGwei := gasPriceWeiDecimal.Div(conversionFactor)

	// Gas limit for a native ETH transfer is fixed
	calculatedGasLimitUnits := decimal.NewFromInt(NativeEthGasLimit)

	// Calculate total fee in Gwei first:
	// totalFeeGwei = gasLimitUnits * gasPriceGwei
	totalFeeGwei := calculatedGasLimitUnits.Mul(calculatedGasPriceGwei)

	// Convert total fee from Gwei to ETH:
	// totalFeeEth = totalFeeGwei / 10^9 (since 1 ETH = 10^9 Gwei)
	calculatedTotalFeeEth := totalFeeGwei.Div(conversionFactor)

	return calculatedTotalFeeEth, calculatedGasPriceGwei, calculatedGasLimitUnits, nil
}
