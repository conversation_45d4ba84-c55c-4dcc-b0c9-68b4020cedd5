package eth

// import (
// 	"context"
// 	"fmt"
// 	// "github.com/gogf/gf/v2/frame/g" // No longer needed directly here
// )

// // SendETHForGasFee sends ETH for gas fees
// // It retrieves the private key from config and sends ETH to the specified address
// func SendETHForGasFee(ctx context.Context, toAddress, amountStr string, configPrefix string) (string, error) {
// 	// Call SendETHWithConfig, which handles fetching private key and other parameters from config
// 	txHash, err := SendETHWithConfig(ctx, configPrefix, toAddress, amountStr)
// 	if err != nil {
// 		return "", fmt.Errorf("SendETHForGasFee failed: %w", err)
// 	}
// 	return txHash, nil
// }
