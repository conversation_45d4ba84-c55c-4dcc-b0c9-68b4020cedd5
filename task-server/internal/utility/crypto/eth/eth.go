package eth

import (
	"crypto/ecdsa"
	"encoding/hex"
	"fmt"
	"math/big"
	"strings"

	"task-api/internal/utility/utils/bip39" // Added for NewSeed

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/crypto"
)

func GenerateMnemonic() (string, error) {
	// 生成助记词
	entropy, err := bip39.NewEntropy(256)
	if err != nil {
		return "", err
	}
	mnemonic, err := bip39.NewMnemonic(entropy)
	if err != nil {
		return "", err
	}
	return mnemonic, nil
}
func ValidateMnemonic(mnemonic string) bool {
	// 验证助记词是否有效
	return bip39.IsMnemonicValid(mnemonic)
}

func GetDerivedPath(index int) string {
	return fmt.Sprintf(`m/44'/60'/0'/0/%d`, index)
}

func ValidatePrivateKey(privateKey string) bool {
	// 验证私钥是否有效

	// 移除可能的0x前缀
	cleanPrivateKey := strings.TrimPrefix(privateKey, "0x")

	// 私钥必须是64个十六进制字符（32字节）
	if len(cleanPrivateKey) != 64 {
		return false
	}

	// 检查是否是有效的十六进制字符串
	_, err := hex.DecodeString(cleanPrivateKey)
	if err != nil {
		return false
	}

	// 检查私钥是否在有效范围内（小于secp256k1曲线的阶）
	n := new(big.Int)
	n, ok := n.SetString(cleanPrivateKey, 16)
	if !ok {
		return false
	}

	// secp256k1曲线的阶
	// N = FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141
	curveOrder, _ := new(big.Int).SetString("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141", 16)

	// 私钥必须大于0且小于曲线的阶
	if n.Cmp(big.NewInt(0)) <= 0 || n.Cmp(curveOrder) >= 0 {
		return false
	}

	// 尝试从私钥导出公钥，如果成功则私钥有效
	_, err = crypto.HexToECDSA(cleanPrivateKey)
	return err == nil
}

func ValidateAddress(address string) bool {
	// 验证地址是否有效
	if !common.IsHexAddress(address) {
		return false
	}

	// 规范化地址
	checksumAddr := common.HexToAddress(address).Hex()

	// 如果输入的地址包含大小写混合（表示包含校验和），验证校验和是否正确
	if address != checksumAddr && !strings.EqualFold(address, checksumAddr) {
		return false
	}

	return true
}

func GenerateAddress(privateKey *ecdsa.PrivateKey) string {
	// 生成随机的私钥
	// privateKey, _ := crypto.GenerateKey()

	// 从私钥直接获取地址
	address := crypto.PubkeyToAddress(privateKey.PublicKey)

	return address.Hex()
}

func GeneratePrivateKey() string {
	// 生成随机的私钥
	privateKey, _ := crypto.GenerateKey()

	// 将私钥转换为十六进制字符串
	privateKeyBytes := crypto.FromECDSA(privateKey)
	return hex.EncodeToString(privateKeyBytes)
}

// // todo 获取ETH余额
// func GetBalance(address string) (string, error) {
// 	// 获取地址余额

// 	return "0.131451554", nil
// }

// // todo 获取erc20Usdt余额
// func GetErc20UsdtBalance(address string) (string, error) {
// 	// 获取erc20Usdt余额

// 	return "10", nil
// }

func GetEthFeeAddress(privateKey string) (string, error) {
	// 获取ETH矿工费地址

	// 移除可能的0x前缀
	cleanPrivateKey := strings.TrimPrefix(privateKey, "0x")

	// 将私钥转换为ECDSA私钥对象
	privateKeyECDSA, err := crypto.HexToECDSA(cleanPrivateKey)
	if err != nil {
		return "", err
	}

	// 从私钥获取公钥
	publicKey := privateKeyECDSA.PublicKey

	// 从公钥生成以太坊地址
	address := crypto.PubkeyToAddress(publicKey)

	// 返回带校验和的地址
	return address.Hex(), nil
}

// PrivateKeyFromString 将字符串私钥转换为 *ecdsa.PrivateKey
func PrivateKeyFromString(privateKeyStr string) (*ecdsa.PrivateKey, error) {
	// 移除可能的0x前缀
	cleanPrivateKey := strings.TrimPrefix(privateKeyStr, "0x")

	// 将私钥字符串转换为ECDSA私钥对象
	privateKey, err := crypto.HexToECDSA(cleanPrivateKey)
	if err != nil {
		return nil, err
	}

	return privateKey, nil
}

func GetAddressFromPrivateKey(privateKeyStr string) (string, error) {
	// 使用已有的 PrivateKeyFromString 函数转换私钥
	privateKey, err := PrivateKeyFromString(privateKeyStr)
	if err != nil {
		return "", fmt.Errorf("无效的私钥: %v", err)
	}

	// 使用已有的 GenerateAddress 函数获取地址
	address := GenerateAddress(privateKey)

	return address, nil
}
