package eth

import (
	"context"
	"fmt"
	"math/big"

	walletCrypto "task-api/internal/utility/crypto" // Assuming this path is correct for your project

	"github.com/ethereum/go-ethereum" // Required for ethereum.CallMsg
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto" // This import is necessary
	"github.com/ethereum/go-ethereum/params" // For ETH to Wei conversion (params.Ether)
	"github.com/shopspring/decimal"          // For precise ETH string to Wei conversion
	"golang.org/x/crypto/sha3"               // Required for Keccak256 hashing for method ID
)

const (
	defaultGasLimit uint64 = 21000 // Default gas limit for standard ETH transfers
)

// parseEthToWei converts an ETH string amount to a Wei *big.Int amount.
func parseEthToWei(ethStr string) (*big.Int, error) {
	if ethStr == "" {
		// This case should ideally be handled by the caller by not calling this func if ethStr is empty.
		// However, adding a safeguard here.
		return nil, fmt.Errorf("ETH string for max gas fee cannot be empty when parsing")
	}
	valueDecimal, err := decimal.NewFromString(ethStr)
	if err != nil {
		return nil, fmt.Errorf("invalid ETH string format for max gas fee: '%s', error: %w", ethStr, err)
	}
	if valueDecimal.Sign() < 0 {
		return nil, fmt.Errorf("max gas fee ETH amount cannot be negative: %s", ethStr)
	}

	weiMultiplier := decimal.NewFromBigInt(big.NewInt(params.Ether), 0) // 10^18
	amountInWeiDecimal := valueDecimal.Mul(weiMultiplier)

	// Wei must be an integer
	if !amountInWeiDecimal.IsInteger() {
		return nil, fmt.Errorf("ETH amount '%s' for max gas fee results in fractional Wei, which is not allowed", ethStr)
	}
	return amountInWeiDecimal.BigInt(), nil
}

// weiToEthString converts a Wei *big.Int amount to an ETH string representation.
// Used for clearer error messages.
func weiToEthString(weiAmount *big.Int) string {
	if weiAmount == nil {
		return "0"
	}
	if weiAmount.Sign() == 0 {
		return "0"
	}
	// Use decimal for precision: Wei / 10^18 = ETH
	amountDecimal := decimal.NewFromBigInt(weiAmount, 0)
	etherDivisor := decimal.NewFromBigInt(big.NewInt(params.Ether), 0) // 10^18

	ethValue := amountDecimal.Div(etherDivisor)
	return ethValue.String() // Returns a clean string, e.g., "0.01"
}

// SendETH sends ETH from the private key to the specified address.
// privateKeyHex: The sender's private key in hex format.
// toAddress: The recipient address for the ETH.
// amountStr: The amount of ETH to send, in Wei (string format, base 10).
// gasLimitIn: Gas limit in units. If 0, defaultGasLimit (21000) is used.
// gasPriceInWei: Gas price in Wei (uint64). If 0, client.SuggestGasPrice is used.
// maxGasFeeEth: Maximum total gas fee allowed for the transaction, in ETH (string format, e.g., "0.01").
//
//	If an empty string is provided, this check is skipped.
func SendETH(
	ctx context.Context,
	privateKeyHex string,
	toAddress string,
	amountStr string,
	gasLimitIn uint64,
	gasPriceInWei uint64,
	maxGasFeeEth string,
) (string, error) {

	client, err := walletCrypto.GetInstance().GetDefaultEthClient(ctx)
	if err != nil {
		return "", fmt.Errorf("failed to get Ethereum client: %w", err)
	}

	chainID, err := client.NetworkID(ctx)
	if err != nil {
		return "", fmt.Errorf("failed to get network ID: %w", err)
	}

	privateKey, err := crypto.HexToECDSA(privateKeyHex)
	if err != nil {
		return "", fmt.Errorf("invalid private key: %v", err)
	}

	fromAddress := crypto.PubkeyToAddress(privateKey.PublicKey)

	nonce, err := client.PendingNonceAt(ctx, fromAddress)
	if err != nil {
		return "", fmt.Errorf("failed to get nonce: %w", err)
	}

	value := new(big.Int)
	_, success := value.SetString(amountStr, 10)
	if !success {
		return "", fmt.Errorf("failed to parse amount string: %s", amountStr)
	}

	var gasPriceToUse *big.Int
	if gasPriceInWei > 0 {
		gasPriceToUse = new(big.Int).SetUint64(gasPriceInWei)
	} else {
		suggestedGasPrice, errSugg := client.SuggestGasPrice(ctx)
		if errSugg != nil {
			return "", fmt.Errorf("failed to get suggested gas price: %w", errSugg)
		}
		gasPriceToUse = suggestedGasPrice
	}

	var gasLimitToUse uint64
	if gasLimitIn > 0 {
		gasLimitToUse = gasLimitIn
	} else {
		gasLimitToUse = defaultGasLimit // Default for ETH transfer
	}

	// Max gas fee check
	if maxGasFeeEth != "" {
		maxGasFeeWei, errParse := parseEthToWei(maxGasFeeEth)
		if errParse != nil {
			return "", fmt.Errorf("invalid maxGasFeeEth ('%s'): %w", maxGasFeeEth, errParse)
		}

		// Ensure gasPriceToUse is not nil (it should be set by now)
		if gasPriceToUse == nil {
			return "", fmt.Errorf("gasPriceToUse is nil before calculating gas fee, this should not happen")
		}

		calculatedGasFeeWei := new(big.Int).Mul(new(big.Int).SetUint64(gasLimitToUse), gasPriceToUse)
		if calculatedGasFeeWei.Cmp(maxGasFeeWei) > 0 {
			return "", fmt.Errorf("calculated gas fee (%s ETH) exceeds maximum allowed gas fee (%s ETH)",
				weiToEthString(calculatedGasFeeWei), weiToEthString(maxGasFeeWei))
		}
	}

	tx := types.NewTransaction(
		nonce,
		common.HexToAddress(toAddress),
		value,
		gasLimitToUse,
		gasPriceToUse,
		nil, // Data for ETH transfer is nil
	)

	signer := types.NewEIP155Signer(chainID)
	signedTx, err := types.SignTx(tx, signer, privateKey)
	if err != nil {
		return "", fmt.Errorf("failed to sign transaction: %w", err)
	}

	err = client.SendTransaction(ctx, signedTx)
	if err != nil {
		return "", fmt.Errorf("failed to send transaction: %w", err)
	}

	return signedTx.Hash().Hex(), nil
}

// SendUSDT sends ERC20 tokens (like USDT) from the private key to the specified address.
// privateKeyHex: The sender's private key in hex format.
// toAddress: The recipient address for the tokens.
// amountStr: The amount of tokens to send, in the smallest unit of the token (e.g., if 6 decimals, "1000000" for 1 token).
// gasLimitIn: Gas limit in units. If 0, it will be estimated.
// gasPriceInWei: Gas price in Wei (uint64). If 0, client.SuggestGasPrice is used.
// maxGasFeeEth: Maximum total gas fee allowed for the transaction, in ETH (string format, e.g., "0.01").
//
//	If an empty string is provided, this check is skipped.
func SendUSDT(
	ctx context.Context,
	privateKeyHex string,
	toAddress string,
	amountStr string,
	gasLimitIn uint64,
	gasPriceInWei uint64,
	maxGasFeeEth string,
) (string, error) {

	client, err := walletCrypto.GetInstance().GetDefaultEthClient(ctx)
	if err != nil {
		return "", fmt.Errorf("failed to get Ethereum client: %w", err)
	}

	tokenContractAddress, err := walletCrypto.GetErc20ContractAddress(ctx)
	if err != nil {
		return "", fmt.Errorf("failed to get ERC20 contract address: %w", err)
	}

	chainID, err := client.NetworkID(ctx)
	if err != nil {
		return "", fmt.Errorf("failed to get network ID: %w", err)
	}

	privateKey, err := crypto.HexToECDSA(privateKeyHex)
	if err != nil {
		return "", fmt.Errorf("invalid private key: %v", err)
	}

	fromAddress := crypto.PubkeyToAddress(privateKey.PublicKey)

	nonce, err := client.PendingNonceAt(ctx, fromAddress)
	if err != nil {
		return "", fmt.Errorf("failed to get nonce: %w", err)
	}

	parsedTokenRecipientAddress := common.HexToAddress(toAddress)
	parsedTokenContractAddress := common.HexToAddress(tokenContractAddress)

	transferFnSignature := []byte("transfer(address,uint256)")
	hash := sha3.NewLegacyKeccak256()
	hash.Write(transferFnSignature)
	methodID := hash.Sum(nil)[:4]

	paddedAddress := common.LeftPadBytes(parsedTokenRecipientAddress.Bytes(), 32)

	tokenAmount := new(big.Int)
	_, success := tokenAmount.SetString(amountStr, 10)
	if !success {
		return "", fmt.Errorf("failed to parse token amount string: %s", amountStr)
	}
	paddedAmount := common.LeftPadBytes(tokenAmount.Bytes(), 32)

	var data []byte
	data = append(data, methodID...)
	data = append(data, paddedAddress...)
	data = append(data, paddedAmount...)

	var gasPriceToUse *big.Int
	if gasPriceInWei > 0 {
		gasPriceToUse = new(big.Int).SetUint64(gasPriceInWei)
	} else {
		suggestedGasPrice, errSugg := client.SuggestGasPrice(ctx)
		if errSugg != nil {
			return "", fmt.Errorf("failed to get suggested gas price: %w", errSugg)
		}
		gasPriceToUse = suggestedGasPrice
	}

	var gasLimitToUse uint64
	if gasLimitIn > 0 {
		gasLimitToUse = gasLimitIn
	} else {
		msg := ethereum.CallMsg{
			From:  fromAddress,
			To:    &parsedTokenContractAddress,
			Data:  data,
			Value: big.NewInt(0), // No ETH sent with this specific call (for estimation)
		}
		estimatedGas, errEst := client.EstimateGas(ctx, msg)
		if errEst != nil {
			return "", fmt.Errorf("failed to estimate gas limit for token transfer: %w", errEst)
		}
		gasLimitToUse = estimatedGas
	}

	// Max gas fee check
	if maxGasFeeEth != "" {
		maxGasFeeWei, errParse := parseEthToWei(maxGasFeeEth)
		if errParse != nil {
			return "", fmt.Errorf("invalid maxGasFeeEth ('%s'): %w", maxGasFeeEth, errParse)
		}

		// Ensure gasPriceToUse is not nil (it should be set by now)
		if gasPriceToUse == nil {
			return "", fmt.Errorf("gasPriceToUse is nil before calculating gas fee, this should not happen")
		}

		calculatedGasFeeWei := new(big.Int).Mul(new(big.Int).SetUint64(gasLimitToUse), gasPriceToUse)
		if calculatedGasFeeWei.Cmp(maxGasFeeWei) > 0 {
			return "", fmt.Errorf("calculated gas fee (%s ETH) exceeds maximum allowed gas fee (%s ETH)",
				weiToEthString(calculatedGasFeeWei), weiToEthString(maxGasFeeWei))
		}
	}

	tx := types.NewTransaction(
		nonce,
		parsedTokenContractAddress, // Transaction is sent TO the token contract
		big.NewInt(0),              // No ETH is sent with the transaction itself (gas is separate)
		gasLimitToUse,
		gasPriceToUse,
		data, // Data payload for the 'transfer' function call
	)

	signer := types.NewEIP155Signer(chainID)
	signedTx, err := types.SignTx(tx, signer, privateKey)
	if err != nil {
		return "", fmt.Errorf("failed to sign transaction: %w", err)
	}

	err = client.SendTransaction(ctx, signedTx)
	if err != nil {
		return "", fmt.Errorf("failed to send transaction: %w", err)
	}

	return signedTx.Hash().Hex(), nil
}
