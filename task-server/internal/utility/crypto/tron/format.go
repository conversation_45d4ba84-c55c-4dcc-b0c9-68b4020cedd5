package tron

import (
	"fmt"
	"math/big"
	"strings"

	"github.com/gogf/gf/v2/errors/gerror"

	"github.com/shopspring/decimal"
)

// FormatTokenValue 格式化 TRC20 代币值。
// 将表示最小单位代币数量的字符串和代币的小数位数，转换为带小数点的代币数量字符串。
// value: 最小单位代币数量的字符串形式。
// decimal: 代币的小数位数。
// 返回格式化后的代币数量字符串和可能的错误。
func FormatTokenValue(value string, decimal int) (string, error) {
	// 将最小单位字符串转换为 big.Int
	valueInt := new(big.Int)
	_, success := valueInt.SetString(value, 10)
	if !success {
		return "0", gerror.Newf("invalid token value: %s", value)
	}

	// 创建 10^decimal 的 big.Int 作为除数
	decimalInt := new(big.Int).Exp(big.NewInt(10), big.NewInt(int64(decimal)), nil)

	// 使用 big.Float 进行精确的浮点数除法
	valueFloat := new(big.Float).SetInt(valueInt)
	decimalFloat := new(big.Float).SetInt(decimalInt)
	tokenFloat := new(big.Float).Quo(valueFloat, decimalFloat)

	// 将 big.Float 转换为字符串，使用 %f 格式化符
	tokenStr := fmt.Sprintf("%f", tokenFloat)

	// 移除末尾多余的零和小数点
	tokenStr = strings.TrimRight(strings.TrimRight(tokenStr, "0"), ".")
	if tokenStr == "" { // 如果移除后为空（例如输入为 "0"），则返回 "0"
		return "0", nil
	}

	return tokenStr, nil
}

func TrxtoSunDecimal(trx decimal.Decimal, tokenDecimals int) (decimal.Decimal, error) {
	// 将 TRX 转换为 SUN
	sun := trx.Mul(decimal.NewFromInt(10).Pow(decimal.NewFromInt(int64(tokenDecimals))))
	return sun, nil
}

func SunToTrxDecimal(sun decimal.Decimal, tokenDecimals int) (decimal.Decimal, error) {
	// 将 SUN 转换为 TRX
	trx := sun.Div(decimal.NewFromInt(10).Pow(decimal.NewFromInt(int64(tokenDecimals))))
	return trx, nil
}
