package tron

import (
	"context"
	"fmt"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/shopspring/decimal"
)

// EnergyRentalResponse represents the response from the energy rental API
type EnergyRentalResponse struct {
	Serial string `json:"serial"` // Order ID
	Amount int    `json:"amount"` // Energy amount
}

// RentEnergyWithConfig rents energy for a TRON address using configuration from the specified prefix
func RentEnergyWithConfig(ctx context.Context, address string, amount int, duration string, configPrefix string, trc20MaxEnergyFee decimal.Decimal) (*EnergyRentalResponse, error) {
	// Get API credentials from config
	apiKey := g.Cfg().MustGet(ctx, configPrefix+".apiKey", "").String()
	apiSecret := g.Cfg().MustGet(ctx, configPrefix+".apiSecret", "").String()
	apiBaseUrl := g.Cfg().MustGet(ctx, configPrefix+".apiBaseUrl", "https://itrx.io").String() // Default to itrx.io if not specified

	if apiKey == "" || apiSecret == "" {
		return nil, fmt.Errorf("TRON energy rental API key or secret not configured under prefix: %s", configPrefix)
	}
	if apiBaseUrl == "" {
		return nil, fmt.Errorf("TRON energy rental API base URL not configured under prefix: %s", configPrefix)
	}

	// Call TRON energy rental API
	g.Log().Infof(ctx, "[LOG_DEBUG RentEnergyWithConfig] Calling RentEnergy with address: %s, amount: %d, duration: %s, apiBaseUrl: %s", address, amount, duration, apiBaseUrl)
	response, err := RentEnergy(ctx, apiKey, apiSecret, apiBaseUrl, address, amount, duration, trc20MaxEnergyFee)
	if response != nil {
		g.Log().Infof(ctx, "[LOG_DEBUG RentEnergyWithConfig] Received from RentEnergy: OrderId: %s, EnergyAmount (from ItrxOrderResponse): %d", response.OrderId, response.EnergyAmount)
	}
	if err != nil {
		return nil, err
	}

	// Parse response
	return &EnergyRentalResponse{
		Serial: response.OrderId,
		Amount: response.EnergyAmount,
	}, nil
}

// GetEnergyRentalStatusWithConfig gets the status of an energy rental order using configuration from the specified prefix
func GetEnergyRentalStatusWithConfig(ctx context.Context, orderID string, configPrefix string) (string, error) {
	// Get API credentials from config
	apiKey := g.Cfg().MustGet(ctx, configPrefix+".apiKey", "").String()
	apiSecret := g.Cfg().MustGet(ctx, configPrefix+".apiSecret", "").String()
	apiBaseUrl := g.Cfg().MustGet(ctx, configPrefix+".apiBaseUrl", "https://itrx.io").String() // Default to itrx.io if not specified

	if apiKey == "" || apiSecret == "" {
		return "", fmt.Errorf("TRON energy rental API key or secret not configured under prefix: %s", configPrefix)
	}
	if apiBaseUrl == "" {
		return "", fmt.Errorf("TRON energy rental API base URL not configured under prefix: %s", configPrefix)
	}

	// Call TRON energy rental API to check order status
	status, err := GetEnergyRentalStatus(ctx, apiKey, apiSecret, apiBaseUrl, orderID)
	if err != nil {
		return "", err
	}

	return status, nil
}
