package tron

import (
	"bytes"
	"context"

	// "crypto/hmac" // No longer used directly
	// "crypto/sha256" // No longer used directly
	// "encoding/hex" // No longer used directly
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"

	util "task-api/internal/utility/utils" // Added for CreateHmacSha256

	"github.com/gogf/gf/v2/frame/g"
	"github.com/shopspring/decimal"
)

// ItrxPlatformData represents the response from the iTRX platform data API.
type ItrxPlatformData struct {
	PlatformAvailEnergy int                      `json:"platform_avail_energy"`
	PlatformMaxEnergy   int                      `json:"platform_max_energy"`
	MinimumOrderEnergy  int                      `json:"minimum_order_energy"`
	MaximumOrderEnergy  int                      `json:"maximum_order_energy"`
	SmallAmount         int                      `json:"small_amount"`
	SmallAddition       float64                  `json:"small_addition"`
	UsdtEnergyNeedOld   int                      `json:"usdt_energy_need_old"`
	UsdtEnergyNeedNew   int                      `json:"usdt_energy_need_new"`
	TieredPricing       []map[string]interface{} `json:"tiered_pricing"`
	Balance             int64                    `json:"balance"`
}

// ItrxOrderResponse represents the response from the iTRX order API.
type ItrxOrderResponse struct {
	Errno   int    `json:"errno"`
	Message string `json:"message,omitempty"`
	Serial  string `json:"serial,omitempty"`
	Amount  int64  `json:"amount,omitempty"`
	Balance int64  `json:"balance,omitempty"`
}

// ItrxOrderDetail represents the details of an iTRX energy order.
type ItrxOrderDetail struct {
	DelegateHash    string `json:"delegate_hash"`
	DelegateTime    string `json:"delegate_time"`
	ReclaimHash     string `json:"reclaim_hash"`
	ReclaimTime     string `json:"reclaim_time"`
	ReclaimTimeReal string `json:"reclaim_time_real"`
	Status          int    `json:"status"`
}

// ItrxOrderQueryResponse represents the response from the iTRX API for order query.
type ItrxOrderQueryResponse struct {
	Errno          int               `json:"errno"`
	Message        string            `json:"message"`
	Serial         string            `json:"order_no"` // Note: field name in JSON is order_no
	ReceiveAddress string            `json:"receive_address"`
	EnergyAmount   int               `json:"energy_amount"`
	PayAmount      float64           `json:"pay_amount"`
	Amount         int               `json:"amount"`
	CreateTime     string            `json:"create_time"`
	ApiName        string            `json:"api_name"`
	Period         int               `json:"period"`
	Status         int               `json:"status"`
	RefundAmount   int               `json:"refund_amount"`
	Details        []ItrxOrderDetail `json:"details"`
}

// GetItrxPlatformData retrieves platform data from the iTRX API.
func GetItrxPlatformData(ctx context.Context, apiKey string, apiBaseUrl string) (*ItrxPlatformData, error) {
	url := fmt.Sprintf("%s/api/v1/frontend/index-data", apiBaseUrl)
	client := &http.Client{Timeout: 10 * time.Second}

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create GetItrxPlatformData request: %w", err)
	}
	req.Header.Set("API-KEY", apiKey)

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send GetItrxPlatformData request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read GetItrxPlatformData response body: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("GetItrxPlatformData API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	var platformData ItrxPlatformData
	err = json.Unmarshal(body, &platformData)
	if err != nil {
		return nil, fmt.Errorf("failed to parse GetItrxPlatformData response: %w. Body: %s", err, string(body))
	}
	return &platformData, nil
}

// GetItrxEnergyPrice retrieves the price for renting energy from the iTRX API.
func GetItrxEnergyPrice(ctx context.Context, apiKey string, apiBaseUrl string, energyAmount int, period string) (decimal.Decimal, error) {
	url := fmt.Sprintf("%s/api/v1/frontend/order/price?period=%s&energy_amount=%d", apiBaseUrl, period, energyAmount)
	client := &http.Client{Timeout: 10 * time.Second}

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to create GetItrxEnergyPrice request: %w", err)
	}
	req.Header.Set("API-KEY", apiKey)

	resp, err := client.Do(req)
	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to send GetItrxEnergyPrice request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to read GetItrxEnergyPrice response body: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return decimal.Zero, fmt.Errorf("GetItrxEnergyPrice API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	var priceResp struct {
		TotalPrice float64 `json:"total_price"`
		// Other fields are not used by this function directly
	}
	err = json.Unmarshal(body, &priceResp)
	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to parse GetItrxEnergyPrice response: %w. Body: %s", err, string(body))
	}

	priceInTrx := decimal.NewFromFloat(priceResp.TotalPrice).Div(decimal.NewFromInt(1000000)) // Convert sun to TRX
	return priceInTrx, nil
}

// RentItrxEnergy rents energy for a TRON address using the iTRX API.
// Note: Business logic (e.g., checking platform data, balances, fee limits) should be handled by the caller.
func RentItrxEnergy(ctx context.Context, apiKey string, apiSecret string, apiBaseUrl string, receiveAddress string, energyAmount int, period string) (*ItrxOrderResponse, error) {
	url := fmt.Sprintf("%s/api/v1/frontend/order", apiBaseUrl)

	orderData := map[string]interface{}{
		"energy_amount":   energyAmount,
		"period":          period,
		"receive_address": receiveAddress,
	}

	jsonData, err := json.Marshal(orderData)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal RentItrxEnergy request data: %w", err)
	}
	g.Log().Infof(ctx, "[LOG_DEBUG RentItrxEnergy] Request JSON data to be sent: %s", string(jsonData))

	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	signature := util.CreateHmacSha256(timestamp+"&"+string(jsonData), apiSecret)

	client := &http.Client{Timeout: 10 * time.Second}
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create RentItrxEnergy request: %w", err)
	}

	req.Header.Set("API-KEY", apiKey)
	req.Header.Set("TIMESTAMP", timestamp)
	req.Header.Set("SIGNATURE", signature)
	req.Header.Set("Content-Type", "application/json")

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send RentItrxEnergy request: %w", err)
	}
	defer resp.Body.Close()

	// g.Log().Infof(ctx, "[LOG_DEBUG RentItrxEnergy] Raw response body from itrx.io: %s", string(body)) // Temporarily commented out due to scope issue
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		g.Log().Infof(ctx, "[LOG_DEBUG RentItrxEnergy] Raw response body from itrx.io: %s", string(body))
		return nil, fmt.Errorf("failed to read RentItrxEnergy response body: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		// Attempt to parse error response if possible, otherwise return raw body
		var errResp ItrxOrderResponse
		if json.Unmarshal(body, &errResp) == nil && errResp.Errno != 0 {
			return nil, fmt.Errorf("RentItrxEnergy API request failed with status %d, errno %d: %s", resp.StatusCode, errResp.Errno, errResp.Message)
		}
		return nil, fmt.Errorf("RentItrxEnergy API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	var orderResp ItrxOrderResponse
	g.Log().Infof(ctx, "[LOG_DEBUG RentItrxEnergy] Parsed orderResp: Serial: %s, Amount (int64 from API): %d, Errno: %d, Message: %s", orderResp.Serial, orderResp.Amount, orderResp.Errno, orderResp.Message)
	err = json.Unmarshal(body, &orderResp)
	if err != nil {
		return nil, fmt.Errorf("failed to parse RentItrxEnergy response: %w. Body: %s", err, string(body))
	}

	// Check for API-level error in the response body even if status is 200 OK
	if orderResp.Errno != 0 {
		return nil, fmt.Errorf("RentItrxEnergy API returned error (errno %d): %s", orderResp.Errno, orderResp.Message)
	}

	return &orderResp, nil
}

// ValidateItrxPlatformData validates the platform data for energy rental.
func ValidateItrxPlatformData(data *ItrxPlatformData, requestedEnergy int) error {
	if data.PlatformAvailEnergy < requestedEnergy {
		return fmt.Errorf("平台可用能量不足: 请求 %d, 可用 %d", requestedEnergy, data.PlatformAvailEnergy)
	}
	if requestedEnergy < data.MinimumOrderEnergy {
		return fmt.Errorf("请求能量低于最小下单量: 请求 %d, 最小 %d", requestedEnergy, data.MinimumOrderEnergy)
	}
	if requestedEnergy > data.MaximumOrderEnergy {
		return fmt.Errorf("请求能量超过最大下单量: 请求 %d, 最大 %d", requestedEnergy, data.MaximumOrderEnergy)
	}
	if requestedEnergy > data.PlatformMaxEnergy { // Assuming PlatformMaxEnergy is for a single order if not split
		return fmt.Errorf("请求能量超过平台单次委托最大下单量: 请求 %d, 最大 %d", requestedEnergy, data.PlatformMaxEnergy)
	}
	// The original check for data.Balance <= 0 was commented out with "todo 上线放开".
	// Keeping it commented or making it configurable might be desired.
	// For a utility, it's often better to be strict or allow the caller to decide.
	// if data.Balance <= 0 {
	// 	return fmt.Errorf("平台余额不足: %d", data.Balance)
	// }
	return nil
}

// QueryItrxOrderStatus queries the iTRX API for energy order status.
func QueryItrxOrderStatus(ctx context.Context, apiKey string, apiBaseUrl string, orderSerial string) (*ItrxOrderQueryResponse, error) {
	url := fmt.Sprintf("%s/api/v1/frontend/order/query?serial=%s", apiBaseUrl, orderSerial)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create QueryItrxOrderStatus request: %w", err)
	}

	req.Header.Set("API-KEY", apiKey)
	req.Header.Set("Accept", "application/json")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send QueryItrxOrderStatus request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read QueryItrxOrderStatus response body: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("QueryItrxOrderStatus API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	var orderResp ItrxOrderQueryResponse
	err = json.Unmarshal(body, &orderResp)
	if err != nil {
		return nil, fmt.Errorf("failed to parse QueryItrxOrderStatus response: %w. Body: %s", err, string(body))
	}

	// Check for API-level error in the response body
	if orderResp.Errno != 0 {
		return nil, fmt.Errorf("QueryItrxOrderStatus API returned error (errno %d): %s", orderResp.Errno, orderResp.Message)
	}

	return &orderResp, nil
}
