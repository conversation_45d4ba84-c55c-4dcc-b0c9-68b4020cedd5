package crypto

import (
	"context"

	"github.com/shopspring/decimal"
)

// DefaultManager returns the default blockchain manager instance
func DefaultManager() BlockchainManager {
	return GetManager()
}

// GetClient is a convenience function to get a blockchain client
func GetClient(chainType ChainType) (BlockchainClient, error) {
	return GetManager().GetClient(chainType)
}

// GetBalance is a convenience function to get balance for an address
func GetBalance(ctx context.Context, chainType ChainType, address string, tokenType TokenType, contractAddress ...string) (*Balance, error) {
	return GetManager().GetBalance(ctx, chainType, address, tokenType, contractAddress...)
}

// SendTransaction is a convenience function to send a transaction
func SendTransaction(ctx context.Context, chainType ChainType, req *SendTransactionRequest) (*Transaction, error) {
	return GetManager().SendTransaction(ctx, chainType, req)
}

// EstimateFee is a convenience function to estimate transaction fee
func EstimateFee(ctx context.Context, chainType ChainType, req *SendTransactionRequest) (*FeeEstimate, error) {
	return GetManager().EstimateFee(ctx, chainType, req)
}

// ValidateAddress validates an address for a specific chain
func ValidateAddress(chainType ChainType, address string) bool {
	return GetManager().ValidateChainAddress(chainType, address)
}

// SupportedChains returns list of supported chain types
func SupportedChains() []ChainType {
	return GetManager().SupportedChains()
}

// Helper functions for creating transaction requests

// NewETHTransferRequest creates a new ETH transfer request
func NewETHTransferRequest(privateKey, toAddress string, amount decimal.Decimal) *SendTransactionRequest {
	return &SendTransactionRequest{
		PrivateKey: privateKey,
		ToAddress:  toAddress,
		Amount:     amount,
		TokenType:  TokenNative,
	}
}

// NewERC20TransferRequest creates a new ERC20 transfer request
func NewERC20TransferRequest(privateKey, toAddress, contractAddress string, amount decimal.Decimal) *SendTransactionRequest {
	return &SendTransactionRequest{
		PrivateKey:      privateKey,
		ToAddress:       toAddress,
		Amount:          amount,
		TokenType:       TokenERC20,
		ContractAddress: contractAddress,
	}
}

// NewTRXTransferRequest creates a new TRX transfer request
func NewTRXTransferRequest(privateKey, toAddress string, amount decimal.Decimal) *SendTransactionRequest {
	return &SendTransactionRequest{
		PrivateKey: privateKey,
		ToAddress:  toAddress,
		Amount:     amount,
		TokenType:  TokenNative,
	}
}

// NewTRC20TransferRequest creates a new TRC20 transfer request
func NewTRC20TransferRequest(privateKey, toAddress, contractAddress string, amount decimal.Decimal) *SendTransactionRequest {
	return &SendTransactionRequest{
		PrivateKey:      privateKey,
		ToAddress:       toAddress,
		Amount:          amount,
		TokenType:       TokenTRC20,
		ContractAddress: contractAddress,
	}
}

// Utility functions for common operations

// GetETHBalance gets ETH balance for an address
func GetETHBalance(ctx context.Context, address string) (*Balance, error) {
	return GetBalance(ctx, ChainETH, address, TokenNative)
}

// GetERC20Balance gets ERC20 token balance for an address
func GetERC20Balance(ctx context.Context, address, contractAddress string) (*Balance, error) {
	return GetBalance(ctx, ChainETH, address, TokenERC20, contractAddress)
}

// GetTRXBalance gets TRX balance for an address
func GetTRXBalance(ctx context.Context, address string) (*Balance, error) {
	return GetBalance(ctx, ChainTRON, address, TokenNative)
}

// GetTRC20Balance gets TRC20 token balance for an address
func GetTRC20Balance(ctx context.Context, address, contractAddress string) (*Balance, error) {
	return GetBalance(ctx, ChainTRON, address, TokenTRC20, contractAddress)
}

// SendETH sends ETH to an address
func SendETH(ctx context.Context, privateKey, toAddress string, amount decimal.Decimal) (*Transaction, error) {
	req := NewETHTransferRequest(privateKey, toAddress, amount)
	return SendTransaction(ctx, ChainETH, req)
}

// SendERC20 sends ERC20 tokens to an address
func SendERC20(ctx context.Context, privateKey, toAddress, contractAddress string, amount decimal.Decimal) (*Transaction, error) {
	req := NewERC20TransferRequest(privateKey, toAddress, contractAddress, amount)
	return SendTransaction(ctx, ChainETH, req)
}

// SendTRX sends TRX to an address
func SendTRX(ctx context.Context, privateKey, toAddress string, amount decimal.Decimal) (*Transaction, error) {
	req := NewTRXTransferRequest(privateKey, toAddress, amount)
	return SendTransaction(ctx, ChainTRON, req)
}

// SendTRC20 sends TRC20 tokens to an address
func SendTRC20(ctx context.Context, privateKey, toAddress, contractAddress string, amount decimal.Decimal) (*Transaction, error) {
	req := NewTRC20TransferRequest(privateKey, toAddress, contractAddress, amount)
	return SendTransaction(ctx, ChainTRON, req)
}

// Address generation utilities

// GenerateETHAddress generates an ETH address from private key
func GenerateETHAddress(privateKeyHex string) (string, error) {
	client, err := GetClient(ChainETH)
	if err != nil {
		return "", err
	}
	return client.GetAddressFromPrivateKey(privateKeyHex)
}

// GenerateTRONAddress generates a TRON address from private key
func GenerateTRONAddress(privateKeyHex string) (string, error) {
	client, err := GetClient(ChainTRON)
	if err != nil {
		return "", err
	}
	return client.GetAddressFromPrivateKey(privateKeyHex)
}

// ValidateETHAddress validates an Ethereum address
func ValidateETHAddress(address string) bool {
	return ValidateAddress(ChainETH, address)
}

// ValidateTRONAddress validates a TRON address
func ValidateTRONAddress(address string) bool {
	return ValidateAddress(ChainTRON, address)
}

// ValidatePrivateKey validates a private key for a specific chain
func ValidatePrivateKey(chainType ChainType, privateKey string) bool {
	client, err := GetClient(chainType)
	if err != nil {
		return false
	}
	return client.ValidatePrivateKey(privateKey)
}

// GeneratePrivateKey generates a new private key for a specific chain
func GeneratePrivateKey(chainType ChainType) (string, error) {
	client, err := GetClient(chainType)
	if err != nil {
		return "", err
	}
	return client.GeneratePrivateKey()
}

// GenerateMnemonic generates a new mnemonic phrase for a specific chain
func GenerateMnemonic(chainType ChainType) (string, error) {
	client, err := GetClient(chainType)
	if err != nil {
		return "", err
	}
	return client.GenerateMnemonic()
}

// ValidateMnemonic validates a mnemonic phrase for a specific chain
func ValidateMnemonic(chainType ChainType, mnemonic string) bool {
	client, err := GetClient(chainType)
	if err != nil {
		return false
	}
	return client.ValidateMnemonic(mnemonic)
}

// GetDerivedPath returns the derivation path for a specific chain and index
func GetDerivedPath(chainType ChainType, index int) (string, error) {
	client, err := GetClient(chainType)
	if err != nil {
		return "", err
	}
	return client.GetDerivedPath(index), nil
}

// FormatAmount formats amount for display
func FormatAmount(chainType ChainType, amount decimal.Decimal, tokenType TokenType) (string, error) {
	client, err := GetClient(chainType)
	if err != nil {
		return "", err
	}
	return client.FormatAmount(amount, tokenType), nil
}

// ParseAmount parses amount string to decimal
func ParseAmount(chainType ChainType, amountStr string, tokenType TokenType) (decimal.Decimal, error) {
	client, err := GetClient(chainType)
	if err != nil {
		return decimal.Zero, err
	}
	return client.ParseAmount(amountStr, tokenType)
}

// GetFeeAddress gets fee address for private key
func GetFeeAddress(chainType ChainType, privateKey string) (string, error) {
	client, err := GetClient(chainType)
	if err != nil {
		return "", err
	}
	return client.GetFeeAddress(privateKey)
}