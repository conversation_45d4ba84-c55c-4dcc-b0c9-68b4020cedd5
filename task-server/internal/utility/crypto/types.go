package crypto

import (
	"context"
	"crypto/ecdsa"
	"fmt"
	"math/big"
	"time"

	"github.com/shopspring/decimal"
)

// ChainType represents the blockchain type
type ChainType string

const (
	ChainETH  ChainType = "ETH"
	ChainTRON ChainType = "TRON"
)

// TokenType represents the token type
type TokenType string

const (
	TokenNative TokenType = "native" // ETH, TRX
	TokenERC20  TokenType = "erc20"  // USDT on Ethereum
	TokenTRC20  TokenType = "trc20"  // USDT on TRON
)

// Address represents a blockchain address with metadata
type Address struct {
	Address    string    `json:"address"`
	PrivateKey string    `json:"private_key,omitempty"`
	ChainType  ChainType `json:"chain_type"`
	CreatedAt  time.Time `json:"created_at"`
}

// Balance represents account balance information
type Balance struct {
	Address      string          `json:"address"`
	ChainType    ChainType       `json:"chain_type"`
	NativeToken  decimal.Decimal `json:"native_token"`  // ETH or TRX balance
	TokenBalance decimal.Decimal `json:"token_balance"` // ERC20/TRC20 balance
	TokenSymbol  string          `json:"token_symbol"`  // e.g., "USDT"
	UpdatedAt    time.Time       `json:"updated_at"`
}

// Transaction represents transaction information
type Transaction struct {
	Hash            string          `json:"hash"`
	ChainType       ChainType       `json:"chain_type"`
	TokenType       TokenType       `json:"token_type"`
	FromAddress     string          `json:"from_address"`
	ToAddress       string          `json:"to_address"`
	Amount          decimal.Decimal `json:"amount"`
	Fee             decimal.Decimal `json:"fee"`
	GasUsed         *big.Int        `json:"gas_used,omitempty"`
	GasPrice        *big.Int        `json:"gas_price,omitempty"`
	BlockNumber     *big.Int        `json:"block_number,omitempty"`
	Status          TxStatus        `json:"status"`
	ContractAddress string          `json:"contract_address,omitempty"`
	CreatedAt       time.Time       `json:"created_at"`
	ConfirmedAt     *time.Time      `json:"confirmed_at,omitempty"`
}

// TxStatus represents transaction status
type TxStatus string

const (
	TxStatusPending   TxStatus = "pending"
	TxStatusConfirmed TxStatus = "confirmed"
	TxStatusFailed    TxStatus = "failed"
)

// SendTransactionRequest represents a transaction send request
type SendTransactionRequest struct {
	PrivateKey      string          `json:"private_key"`
	FromAddress     string          `json:"from_address"`
	ToAddress       string          `json:"to_address"`
	Amount          decimal.Decimal `json:"amount"`
	TokenType       TokenType       `json:"token_type"`
	ContractAddress string          `json:"contract_address,omitempty"`
	GasLimit        *big.Int        `json:"gas_limit,omitempty"`
	GasPrice        *big.Int        `json:"gas_price,omitempty"`
	MaxFee          *decimal.Decimal `json:"max_fee,omitempty"`
	LogPrefix       string          `json:"log_prefix,omitempty"`
}

// FeeEstimate represents fee estimation information
type FeeEstimate struct {
	ChainType       ChainType       `json:"chain_type"`
	TokenType       TokenType       `json:"token_type"`
	EstimatedFee    decimal.Decimal `json:"estimated_fee"`
	GasLimit        *big.Int        `json:"gas_limit,omitempty"`
	GasPrice        *big.Int        `json:"gas_price,omitempty"`
	NetworkCongestion string        `json:"network_congestion,omitempty"`
	EstimatedAt     time.Time       `json:"estimated_at"`
}

// WalletInfo represents HD wallet information
type WalletInfo struct {
	Mnemonic   string            `json:"mnemonic,omitempty"`
	Seed       []byte            `json:"seed,omitempty"`
	ChainType  ChainType         `json:"chain_type"`
	Addresses  map[int]*Address  `json:"addresses"` // index -> address
	CreatedAt  time.Time         `json:"created_at"`
}

// BlockchainClient defines the unified interface for blockchain operations
type BlockchainClient interface {
	// Chain information
	GetChainType() ChainType
	GetChainID(ctx context.Context) (*big.Int, error)
	
	// Address operations
	GenerateAddress(privateKey *ecdsa.PrivateKey) (*Address, error)
	ValidateAddress(address string) bool
	ValidatePrivateKey(privateKey string) bool
	GetAddressFromPrivateKey(privateKeyStr string) (string, error)
	
	// HD Wallet operations
	GenerateMnemonic() (string, error)
	ValidateMnemonic(mnemonic string) bool
	GeneratePrivateKey() (string, error)
	GetDerivedPath(index int) string
	DeriveKeyFromPath(mnemonic string, path string) (*ecdsa.PrivateKey, error)
	
	// Balance operations
	GetNativeBalance(ctx context.Context, address string) (*Balance, error)
	GetTokenBalance(ctx context.Context, address string, contractAddress string) (*Balance, error)
	GetAllBalances(ctx context.Context, address string) (*Balance, error)
	
	// Transaction operations
	SendTransaction(ctx context.Context, req *SendTransactionRequest) (*Transaction, error)
	EstimateFee(ctx context.Context, req *SendTransactionRequest) (*FeeEstimate, error)
	GetTransaction(ctx context.Context, hash string) (*Transaction, error)
	GetTransactionStatus(ctx context.Context, hash string) (TxStatus, error)
	
	// Utility functions
	FormatAmount(amount decimal.Decimal, tokenType TokenType) string
	ParseAmount(amountStr string, tokenType TokenType) (decimal.Decimal, error)
	GetFeeAddress(privateKey string) (string, error)
}

// BlockchainManager manages multiple blockchain clients
type BlockchainManager interface {
	// Client management
	GetClient(chainType ChainType) (BlockchainClient, error)
	RegisterClient(chainType ChainType, client BlockchainClient) error
	
	// Unified operations across chains
	GetBalance(ctx context.Context, chainType ChainType, address string, tokenType TokenType, contractAddress ...string) (*Balance, error)
	SendTransaction(ctx context.Context, chainType ChainType, req *SendTransactionRequest) (*Transaction, error)
	EstimateFee(ctx context.Context, chainType ChainType, req *SendTransactionRequest) (*FeeEstimate, error)
	
	// Cross-chain utilities
	SupportedChains() []ChainType
	ValidateChainAddress(chainType ChainType, address string) bool
}

// ClientConfig represents blockchain client configuration
type ClientConfig struct {
	ChainType        ChainType `json:"chain_type"`
	RPCUrl           string    `json:"rpc_url"`
	BackupRPCUrls    []string  `json:"backup_rpc_urls,omitempty"`
	APIKey           string    `json:"api_key,omitempty"`
	ContractAddress  string    `json:"contract_address,omitempty"`
	NetworkID        *big.Int  `json:"network_id,omitempty"`
	Timeout          time.Duration `json:"timeout,omitempty"`
	MaxRetries       int       `json:"max_retries,omitempty"`
	RetryDelay       time.Duration `json:"retry_delay,omitempty"`
	UseTLS           bool      `json:"use_tls,omitempty"`  // Explicitly control TLS usage
}

// Error types for consistent error handling
type BlockchainError struct {
	Code      string    `json:"code"`
	Message   string    `json:"message"`
	ChainType ChainType `json:"chain_type"`
	Details   map[string]interface{} `json:"details,omitempty"`
}

func (e *BlockchainError) Error() string {
	return fmt.Sprintf("[%s] %s: %s", e.ChainType, e.Code, e.Message)
}

// Common error codes
const (
	ErrCodeInvalidAddress       = "INVALID_ADDRESS"
	ErrCodeInvalidPrivateKey    = "INVALID_PRIVATE_KEY"
	ErrCodeInsufficientBalance  = "INSUFFICIENT_BALANCE"
	ErrCodeInsufficientGas      = "INSUFFICIENT_GAS"
	ErrCodeTransactionFailed    = "TRANSACTION_FAILED"
	ErrCodeNetworkError         = "NETWORK_ERROR"
	ErrCodeContractError        = "CONTRACT_ERROR"
	ErrCodeUnsupportedOperation = "UNSUPPORTED_OPERATION"
)

// NewBlockchainError creates a new blockchain error
func NewBlockchainError(code, message string, chainType ChainType, details ...map[string]interface{}) *BlockchainError {
	var detailsMap map[string]interface{}
	if len(details) > 0 {
		detailsMap = details[0]
	}
	
	return &BlockchainError{
		Code:      code,
		Message:   message,
		ChainType: chainType,
		Details:   detailsMap,
	}
}