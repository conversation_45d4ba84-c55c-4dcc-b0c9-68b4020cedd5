package config

import (
	"context"
	"fmt"
	"strings"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

// GetContractAddress returns the contract address for a given token on a specific chain
// chainSymbol: The chain symbol (e.g., "ETH", "TRON")
// tokenSymbol: The token symbol (e.g., "USDT")
// Returns the contract address and any error that occurred
func GetContractAddress(ctx context.Context, chainSymbol, tokenSymbol string) (string, error) {
	// Convert to uppercase for consistency
	chainSymbolUpper := strings.ToUpper(chainSymbol)
	tokenSymbolUpper := strings.ToUpper(tokenSymbol)

	// Normalize token symbol to lowercase for config path
	tokenConfigKey := strings.ToLower(tokenSymbolUpper)

	// Construct the path to the contract address in the config
	contractAddressPath := fmt.Sprintf("depositCheck.chains.%s.tokens.%s.contractAddress", chainSymbolUpper, tokenConfigKey)

	// Get the contract address from the config
	contractAddressVar, err := g.Cfg().Get(ctx, contractAddressPath)
	if err != nil {
		return "", gerror.Wrapf(err, "Error getting contract address for token %s on chain %s from path %s",
			tokenSymbolUpper, chainSymbolUpper, contractAddressPath)
	}

	if contractAddressVar.IsNil() {
		return "", gerror.Newf("Contract address configuration not found for token %s on chain %s at path %s",
			tokenSymbolUpper, chainSymbolUpper, contractAddressPath)
	}

	contractAddress := contractAddressVar.String()
	if contractAddress == "" {
		return "", gerror.Newf("Contract address for token %s on chain %s is configured as empty string at %s",
			tokenSymbolUpper, chainSymbolUpper, contractAddressPath)
	}

	return contractAddress, nil
}

// // tokenSymbol: The token symbol (e.g., "USDT")
// // Returns the token decimals and any error that occurred
// func GetTokenDecimals(ctx context.Context, chainSymbol, tokenSymbol string) (int, error) {
// 	// Convert to uppercase for consistency
// 	chainSymbolUpper := strings.ToUpper(chainSymbol)
// 	tokenSymbolUpper := strings.ToUpper(tokenSymbol)

// 	// Normalize token symbol to lowercase for config path
// 	tokenConfigKey := strings.ToLower(tokenSymbolUpper)

// 	// Construct the path to the decimals in the config
// 	decimalsPath := fmt.Sprintf("depositCheck.chains.%s.tokens.%s.decimals", chainSymbolUpper, tokenConfigKey)

// 	// Get the decimals from the config
// 	decimalsVar, err := g.Cfg().Get(ctx, decimalsPath)
// 	if err != nil {
// 		return 0, gerror.Wrapf(err, "Error getting decimals for token %s on chain %s from path %s",
// 			tokenSymbolUpper, chainSymbolUpper, decimalsPath)
// 	}

// 	if decimalsVar.IsNil() {
// 		return 0, gerror.Newf("Decimals configuration not found for token %s on chain %s at path %s",
// 			tokenSymbolUpper, chainSymbolUpper, decimalsPath)
// 	}

// 	return decimalsVar.Int(), nil
// }
