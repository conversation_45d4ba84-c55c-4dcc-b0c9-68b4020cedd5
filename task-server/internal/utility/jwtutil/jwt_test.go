package jwtutil_test

import (
	"errors"
	"fmt"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/test/gtest"
	"github.com/golang-jwt/jwt/v4"

	"task-api/internal/utility/jwtutil"
)

const (
	testUserID                    = "test-user-123"
	testJwtSecret                 = "a-very-secure-test-secret-for-jwt-util"
	testAccessTokenExpireSeconds  = 60
	testRefreshTokenExpireSeconds = 120
	testIssuer                    = "task-api"
	invalidJwtSecret              = "this-is-a-wrong-secret-key-for-sure"

	envJwtSecret                 = "GF_JWT_SECRET"
	envAccessExpireSeconds       = "GF_AUTH_EXPIRESECONDS"
	envRefreshTokenExpireSeconds = "GF_AUTH_REFRESHTOKENEXPIRESECONDS"
)

func setEnv(t *gtest.T, key, value string) {
	originalValue, wasSet := os.LookupEnv(key)
	err := os.Setenv(key, value)
	t.AssertNil(err) // Removed message

	t.Cleanup(func() {
		if wasSet {
			os.Setenv(key, originalValue)
		} else {
			os.Unsetenv(key)
		}
	})
}

func unsetEnv(t *gtest.T, key string) {
	originalValue, wasSet := os.LookupEnv(key)
	_ = os.Unsetenv(key)

	t.Cleanup(func() {
		if wasSet {
			os.Setenv(key, originalValue)
		}
	})
}

func setupTestEnvConfig(t *gtest.T) {
	setEnv(t, envJwtSecret, testJwtSecret)
	setEnv(t, envAccessExpireSeconds, fmt.Sprintf("%d", testAccessTokenExpireSeconds))
	setEnv(t, envRefreshTokenExpireSeconds, fmt.Sprintf("%d", testRefreshTokenExpireSeconds))

	ctx := gctx.New()
	secretVal, err := g.Cfg().Get(ctx, "jwt.secret")
	t.AssertNil(err)
	t.AssertEQ(secretVal.String(), testJwtSecret)

	accessExpVal, err := g.Cfg().Get(ctx, "auth.expireSeconds")
	t.AssertNil(err)
	t.AssertEQ(accessExpVal.Int(), testAccessTokenExpireSeconds)

	refreshExpVal, err := g.Cfg().Get(ctx, "auth.refreshTokenExpireSeconds")
	t.AssertNil(err)
	t.AssertEQ(refreshExpVal.Int(), testRefreshTokenExpireSeconds)
}

func TestGenerateAccessToken(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		setupTestEnvConfig(t)

		tokenString, jti, err := jwtutil.GenerateAccessToken(testUserID)

		t.AssertNil(err)
		t.AssertNE(tokenString, "")
		t.AssertNE(jti, "")

		claims, err := jwtutil.VerifyToken(tokenString)
		t.AssertNil(err)
		t.AssertNE(claims, nil)

		t.AssertEQ(claims.UserID, testUserID)
		t.AssertEQ(claims.ID, jti)
		t.AssertEQ(claims.Issuer, testIssuer)
		t.AssertEQ(claims.Subject, testUserID)

		expectedExpiresAt := time.Now().Add(time.Duration(testAccessTokenExpireSeconds) * time.Second)
		delta := 7 * time.Second
		t.AssertLT(claims.ExpiresAt.Time.Unix(), expectedExpiresAt.Add(delta).Unix())
		t.AssertGT(claims.ExpiresAt.Time.Unix(), expectedExpiresAt.Add(-delta).Unix())

		t.AssertGT(claims.IssuedAt.Time.Unix(), time.Now().Add(-delta).Unix())
		t.AssertLE(claims.IssuedAt.Time.Unix(), time.Now().Add(delta).Unix())
		t.AssertEQ(claims.NotBefore.Time.Unix(), claims.IssuedAt.Time.Unix())
	})
}

func TestGenerateRefreshToken(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		setupTestEnvConfig(t)

		tokenString, jti, err := jwtutil.GenerateRefreshToken(testUserID)

		t.AssertNil(err)
		t.AssertNE(tokenString, "")
		t.AssertNE(jti, "")

		claims, err := jwtutil.VerifyToken(tokenString)
		t.AssertNil(err)
		t.AssertNE(claims, nil)

		t.AssertEQ(claims.UserID, testUserID)
		t.AssertEQ(claims.ID, jti)
		t.AssertEQ(claims.Issuer, testIssuer)
		t.AssertEQ(claims.Subject, testUserID)

		expectedExpiresAt := time.Now().Add(time.Duration(testRefreshTokenExpireSeconds) * time.Second)
		delta := 7 * time.Second
		t.AssertLT(claims.ExpiresAt.Time.Unix(), expectedExpiresAt.Add(delta).Unix())
		t.AssertGT(claims.ExpiresAt.Time.Unix(), expectedExpiresAt.Add(-delta).Unix())
	})
}

func TestVerifyToken(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		setupTestEnvConfig(t)

		validAccessToken, _, err := jwtutil.GenerateAccessToken(testUserID)
		t.AssertNil(err)
		claims, err := jwtutil.VerifyToken(validAccessToken)
		t.AssertNil(err)
		t.AssertNE(claims, nil)
		t.AssertEQ(claims.UserID, testUserID)

		setEnv(t, envAccessExpireSeconds, "-1")
		setEnv(t, envJwtSecret, testJwtSecret)
		setEnv(t, envRefreshTokenExpireSeconds, fmt.Sprintf("%d", testRefreshTokenExpireSeconds))

		expiredToken, _, err := jwtutil.GenerateAccessToken("user-for-expired-token")
		t.AssertNil(err)

		_, err = jwtutil.VerifyToken(expiredToken)
		t.AssertNE(err, nil)
		t.Assert(errors.Is(err, jwt.ErrTokenExpired) || strings.Contains(err.Error(), "token is expired"), true)
		setupTestEnvConfig(t)

		tokenWithGoodSecret, _, err := jwtutil.GenerateAccessToken(testUserID)
		t.AssertNil(err)
		setEnv(t, envJwtSecret, invalidJwtSecret)
		_, err = jwtutil.VerifyToken(tokenWithGoodSecret)
		t.AssertNE(err, nil)
		t.Assert(errors.Is(err, jwt.ErrTokenSignatureInvalid) || strings.Contains(strings.ToLower(err.Error()), "signature is invalid"), true)
		setupTestEnvConfig(t)

		parts := strings.Split(validAccessToken, ".")
		t.AssertEQ(len(parts), 3)
		tamperedToken := parts[0] + "." + parts[1] + "." + "tamperedSignaturePart"
		_, err = jwtutil.VerifyToken(tamperedToken)
		t.AssertNE(err, nil)
		t.Assert(errors.Is(err, jwt.ErrTokenSignatureInvalid) || strings.Contains(strings.ToLower(err.Error()), "signature is invalid") || strings.Contains(err.Error(), "malformed"), true)

		malformedToken := "not-a-jwt-token"
		_, err = jwtutil.VerifyToken(malformedToken)
		t.AssertNE(err, nil)
		t.Assert(strings.Contains(err.Error(), "malformed"), true)

		now := time.Now()
		futureNbfClaims := jwtutil.CustomClaims{UserID: "test-nbf-user", RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(now.Add(time.Duration(testAccessTokenExpireSeconds) * time.Second)),
			IssuedAt:  jwt.NewNumericDate(now), NotBefore: jwt.NewNumericDate(now.Add(30 * time.Second)),
			ID: "test-nbf-jti", Issuer: testIssuer, Subject: "test-nbf-user",
		}}
		tokenObj := jwt.NewWithClaims(jwt.SigningMethodHS256, futureNbfClaims)
		currentSecret := os.Getenv(envJwtSecret)
		t.AssertEQ(currentSecret, testJwtSecret)
		nbfTokenString, errToken := tokenObj.SignedString([]byte(currentSecret))
		t.AssertNil(errToken)
		_, err = jwtutil.VerifyToken(nbfTokenString)
		t.AssertNE(err, nil)
		t.Assert(strings.Contains(err.Error(), "token not active yet"), true)

		setEnv(t, envJwtSecret, "")
		setEnv(t, envAccessExpireSeconds, fmt.Sprintf("%d", testAccessTokenExpireSeconds))
		setEnv(t, envRefreshTokenExpireSeconds, fmt.Sprintf("%d", testRefreshTokenExpireSeconds))

		_, _, errGen := jwtutil.GenerateAccessToken("tempUser")
		t.AssertNE(errGen, nil)
		t.Assert(strings.Contains(errGen.Error(), "jwt secret is not configured"), true)

		_, errVerify := jwtutil.VerifyToken(validAccessToken)
		t.AssertNE(errVerify, nil)
		t.Assert(strings.Contains(errVerify.Error(), "jwt secret is not configured"), true)
	})
}

func TestConfigErrors(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		setupTestEnvConfig(t)
		unsetEnv(t, envJwtSecret)

		_, _, err := jwtutil.GenerateAccessToken(testUserID)
		t.AssertNE(err, nil)
		t.Assert(strings.Contains(err.Error(), "failed to read jwt secret configuration") || strings.Contains(err.Error(), "jwt.secret is empty in config"), true)

		setupTestEnvConfig(t)
		unsetEnv(t, envJwtSecret)

		_, _, err = jwtutil.GenerateRefreshToken(testUserID)
		t.AssertNE(err, nil)
		t.Assert(strings.Contains(err.Error(), "failed to read jwt secret configuration") || strings.Contains(err.Error(), "jwt.secret is empty in config"), true)

		setupTestEnvConfig(t)
		unsetEnv(t, envJwtSecret)

		_, err = jwtutil.VerifyToken("anytoken")
		t.AssertNE(err, nil)
		t.Assert(strings.Contains(err.Error(), "failed to read jwt secret configuration") || strings.Contains(err.Error(), "jwt.secret is empty in config"), true)

		setupTestEnvConfig(t)
		unsetEnv(t, envAccessExpireSeconds)

		_, _, err = jwtutil.GenerateAccessToken(testUserID)
		t.AssertNE(err, nil)
		t.Assert(strings.Contains(err.Error(), "failed to read token expiration configuration"), true)

		setupTestEnvConfig(t)
		unsetEnv(t, envRefreshTokenExpireSeconds)

		_, _, err = jwtutil.GenerateRefreshToken(testUserID)
		t.AssertNE(err, nil)
		t.Assert(strings.Contains(err.Error(), "failed to read refresh token expiration configuration"), true)
	})
}
