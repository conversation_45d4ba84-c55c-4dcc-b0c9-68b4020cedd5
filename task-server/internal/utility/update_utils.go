package utility

import (
	"context"
	"fmt" // Added fmt import
	// Removed: "telegram-bot-api/internal/service"

	tgbotapi "github.com/go-telegram-bot-api/telegram-bot-api/v5"
	"github.com/gogf/gf/v2/frame/g"
)

// ExtractUserIDFromUpdate 从 Update 对象中提取用户 ID
// 它会检查多种可能的 Update 类型来查找用户来源。
func ExtractUserIDFromUpdate(update tgbotapi.Update) int64 {
	if update.Message != nil && update.Message.From != nil {
		return update.Message.From.ID
	}
	if update.CallbackQuery != nil && update.CallbackQuery.From != nil {
		return update.CallbackQuery.From.ID
	}
	if update.EditedMessage != nil && update.EditedMessage.From != nil {
		return update.EditedMessage.From.ID
	}
	if update.ChannelPost != nil && update.ChannelPost.From != nil {
		// 注意：Channel Post 的 From 可能代表频道本身，而不是用户。
		// 根据业务逻辑，可能需要特殊处理或忽略。
		// 这里暂时返回 ID，但需要确认是否符合预期。
		return update.ChannelPost.From.ID
	}
	if update.EditedChannelPost != nil && update.EditedChannelPost.From != nil {
		return update.EditedChannelPost.From.ID
	}
	if update.InlineQuery != nil && update.InlineQuery.From != nil {
		return update.InlineQuery.From.ID
	}
	if update.ChosenInlineResult != nil && update.ChosenInlineResult.From != nil {
		g.Log().Info(context.Background(), "ChosenInlineResult.From.ID: %d", update.ChosenInlineResult.From.ID)
		return update.ChosenInlineResult.From.ID
	}
	if update.MyChatMember != nil && update.MyChatMember.From.ID != 0 {
		return update.MyChatMember.From.ID
	}
	if update.ChatMember != nil && update.ChatMember.From.ID != 0 {
		return update.ChatMember.From.ID
	}
	if update.ChatJoinRequest != nil && update.ChatJoinRequest.From.ID != 0 {
		return update.ChatJoinRequest.From.ID
	}
	// 如果以上都不是，返回 0，表示无法提取用户 ID
	return 0
}

// SendTextMessage sends a simple text message to the specified chat.
// It sends the message using the provided bot instance.
// replyMarkup can be nil if no keyboard is needed.
func SendTextMessage(ctx context.Context, bot *tgbotapi.BotAPI, chatID int64, text string, replyMarkup interface{}) error {
	if bot == nil {
		err := fmt.Errorf("SendTextMessage: provided bot instance is nil")
		g.Log().Error(ctx, err)
		return err
	}

	msg := tgbotapi.NewMessage(chatID, text)
	if replyMarkup != nil {
		msg.ReplyMarkup = replyMarkup
	}
	msg.ParseMode = tgbotapi.ModeHTML // Or ModeMarkdownV2, depending on your needs

	_, err := bot.Send(msg) // Declare err using :=
	if err != nil {
		g.Log().Errorf(ctx, "SendTextMessage: Failed to send message to chat %d: %v", chatID, err)
		return err
	}
	return nil
}
