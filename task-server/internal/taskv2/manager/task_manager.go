package manager

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"

	"task-api/internal/taskv2/interfaces"
	"task-api/internal/taskv2/storage"
	"task-api/internal/taskv2/types"
)

// TaskManager 任务管理器
type TaskManager struct {
	config      *types.Config
	scanners    map[string]interfaces.ChainScanner
	storage     interfaces.CheckpointStorage
	logger      *logrus.Logger
	ctx         context.Context
	cancel      context.CancelFunc
	wg          sync.WaitGroup
	mu          sync.RWMutex
	healthStats map[string]*types.HealthStatus
}

// NewTaskManager 创建任务管理器
func NewTaskManager(
	config *types.Config,
	scanners map[string]interfaces.ChainScanner,
	storage interfaces.CheckpointStorage,
	logger *logrus.Logger,
) *TaskManager {
	return &TaskManager{
		config:      config,
		scanners:    scanners,
		storage:     storage,
		logger:      logger,
		healthStats: make(map[string]*types.HealthStatus),
	}
}

// Start 启动任务管理器
func (tm *TaskManager) Start(ctx context.Context) error {
	tm.ctx, tm.cancel = context.WithCancel(ctx)

	// 启动所有扫描器
	for chainName, scanner := range tm.scanners {
		if err := scanner.Start(tm.ctx); err != nil {
			return fmt.Errorf("start scanner %s: %w", chainName, err)
		}
		tm.logger.Info("scanner started", "chain", chainName)
	}

	// 启动扫描任务
	for chainName, scanner := range tm.scanners {
		tm.wg.Add(1)
		go tm.runScanner(chainName, scanner)
	}

	// 启动健康检查
	tm.wg.Add(1)
	go tm.runHealthCheck()

	tm.logger.Info("task manager started", "chains", len(tm.scanners))
	return nil
}

// Stop 停止任务管理器
func (tm *TaskManager) Stop(ctx context.Context) error {
	tm.logger.Info("stopping task manager")

	// 取消上下文
	if tm.cancel != nil {
		tm.cancel()
	}

	// 等待所有goroutine结束
	done := make(chan struct{})
	go func() {
		tm.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		// 所有goroutine已结束
	case <-ctx.Done():
		return fmt.Errorf("timeout waiting for tasks to stop")
	}

	// 停止所有扫描器
	var errs []error
	for chainName, scanner := range tm.scanners {
		if err := scanner.Stop(ctx); err != nil {
			errs = append(errs, fmt.Errorf("stop scanner %s: %w", chainName, err))
		}
	}

	if len(errs) > 0 {
		return fmt.Errorf("stop errors: %v", errs)
	}

	tm.logger.Info("task manager stopped")
	return nil
}

// runScanner 运行单个扫描器
func (tm *TaskManager) runScanner(chainName string, scanner interfaces.ChainScanner) {
	defer tm.wg.Done()

	ticker := time.NewTicker(tm.config.Global.ScanInterval)
	defer ticker.Stop()

	// 立即执行一次扫描
	tm.executeScan(chainName, scanner)

	for {
		select {
		case <-tm.ctx.Done():
			tm.logger.Info("scanner stopped", "chain", chainName)
			return

		case <-ticker.C:
			tm.executeScan(chainName, scanner)
		}
	}
}

// executeScan 执行扫描
func (tm *TaskManager) executeScan(chainName string, scanner interfaces.ChainScanner) {
	startTime := time.Now()

	// 获取分布式锁（如果配置了Redis）
	if tm.storage != nil {
		lockDuration := tm.config.Global.ScanInterval * 2 // 锁时间为扫描间隔的2倍

		// 尝试获取锁
		locked, err := tm.acquireLock(chainName, lockDuration)
		if err != nil {
			tm.logger.Error("acquire lock failed",
				"chain", chainName,
				"error", err)
			return
		}

		if !locked {
			tm.logger.Debug("another instance is scanning", "chain", chainName)
			return
		}

		// 确保释放锁
		defer func() {
			// 检查上下文是否已取消，如果已取消则跳过锁释放
			if tm.ctx.Err() != nil {
				tm.logger.Debug("context cancelled, skipping lock release", "chain", chainName)
				return
			}

			if err := tm.releaseLock(chainName); err != nil {
				tm.logger.Error("release lock failed",
					"chain", chainName,
					"error", err)
			}
		}()

		// 定期延长锁
		go tm.extendLockPeriodically(chainName, lockDuration/2)
	}

	// 执行扫描
	if err := scanner.ScanAndProcess(tm.ctx); err != nil {
		// 如果是上下文取消错误，不记录为错误
		if tm.ctx.Err() != nil {
			tm.logger.Debug("scan cancelled due to context",
				"chain", chainName,
				"duration", time.Since(startTime))
			return
		}

		tm.logger.Error("scan failed",
			"chain", chainName,
			"error", err,
			"duration", time.Since(startTime))

		// 更新健康状态
		tm.updateHealthStatus(chainName, false, err.Error())
	} else {
		tm.logger.Debug("scan completed",
			"chain", chainName,
			"duration", time.Since(startTime))

		// 更新健康状态
		tm.updateHealthStatus(chainName, true, "")
	}
}

// runHealthCheck 运行健康检查
func (tm *TaskManager) runHealthCheck() {
	defer tm.wg.Done()

	ticker := time.NewTicker(tm.config.Global.HealthCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-tm.ctx.Done():
			return

		case <-ticker.C:
			tm.performHealthCheck()
		}
	}
}

// performHealthCheck 执行健康检查
func (tm *TaskManager) performHealthCheck() {
	for chainName, scanner := range tm.scanners {
		if err := scanner.HealthCheck(tm.ctx); err != nil {
			tm.logger.Warn("health check failed",
				"chain", chainName,
				"error", err)
		}

		// 获取扫描器指标
		metrics := scanner.GetMetrics()

		// 创建健康状态
		status := &types.HealthStatus{
			Chain:              chainName,
			Status:             string(types.HealthStatusHealthy),
			LastProcessedBlock: metrics.LastProcessedBlock,
			CurrentBlock:       metrics.CurrentBlock,
			BlockLag:           metrics.CurrentBlock - metrics.LastProcessedBlock,
		}

		// 判断健康状态
		if metrics.LastError != nil && time.Since(metrics.LastErrorTime) < 5*time.Minute {
			status.Status = string(types.HealthStatusDegraded)
			status.LastError = metrics.LastError.Error()
			status.LastErrorTime = metrics.LastErrorTime
		}

		// 根据链类型设置不同的滞后阈值
		var lagThreshold uint64 = 100
		if chainName == "TRON" {
			lagThreshold = 1000 // TRON 允许更大的滞后
		}

		if status.BlockLag > lagThreshold {
			status.Status = string(types.HealthStatusUnhealthy)
		}

		// 保存健康状态
		tm.updateHealthStatus(chainName,
			status.Status == string(types.HealthStatusHealthy),
			status.LastError)

		// 如果配置了Redis，保存到Redis
		if tm.storage != nil {
			// TODO: 实现保存到Redis的逻辑
		}
	}
}

// updateHealthStatus 更新健康状态
func (tm *TaskManager) updateHealthStatus(chainName string, healthy bool, lastError string) {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	status, exists := tm.healthStats[chainName]
	if !exists {
		status = &types.HealthStatus{
			Chain: chainName,
		}
		tm.healthStats[chainName] = status
	}

	if healthy {
		status.Status = string(types.HealthStatusHealthy)
		status.LastError = ""
	} else {
		status.Status = string(types.HealthStatusUnhealthy)
		status.LastError = lastError
		status.LastErrorTime = time.Now()
	}
}

// GetHealthStatus 获取健康状态
func (tm *TaskManager) GetHealthStatus() map[string]*types.HealthStatus {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	// 复制健康状态
	result := make(map[string]*types.HealthStatus)
	for k, v := range tm.healthStats {
		result[k] = v
	}

	return result
}

// GetScanner 获取指定链的扫描器
func (tm *TaskManager) GetScanner(chainName string) (interfaces.ChainScanner, error) {
	scanner, exists := tm.scanners[chainName]
	if !exists {
		return nil, fmt.Errorf("scanner not found: %s", chainName)
	}
	return scanner, nil
}

// acquireLock 获取分布式锁
func (tm *TaskManager) acquireLock(chainName string, duration time.Duration) (bool, error) {
	redisStorage, ok := tm.storage.(*storage.RedisStorage)
	if !ok {
		// 如果不是Redis存储，直接返回true
		return true, nil
	}

	return redisStorage.AcquireLock(tm.ctx, chainName, duration)
}

// releaseLock 释放分布式锁
func (tm *TaskManager) releaseLock(chainName string) error {
	redisStorage, ok := tm.storage.(*storage.RedisStorage)
	if !ok {
		return nil
	}

	return redisStorage.ReleaseLock(tm.ctx, chainName)
}

// extendLockPeriodically 定期延长锁
func (tm *TaskManager) extendLockPeriodically(chainName string, interval time.Duration) {
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for {
		select {
		case <-tm.ctx.Done():
			tm.logger.Debug("lock extension stopped due to context cancellation", "chain", chainName)
			return
		case <-ticker.C:
			redisStorage, ok := tm.storage.(*storage.RedisStorage)
			if !ok {
				return
			}

			// 创建带超时的上下文，避免在关闭时阻塞
			extendCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
			err := redisStorage.ExtendLock(extendCtx, chainName, interval*2)
			cancel()

			if err != nil {
				// 检查是否是上下文取消或锁不存在错误
				if tm.ctx.Err() != nil {
					tm.logger.Debug("lock extension cancelled", "chain", chainName)
					return
				}

				if storage.IsLockNotExistsError(err) {
					tm.logger.Debug("lock no longer exists, stopping extension",
						"chain", chainName)
					return
				}

				tm.logger.Warn("extend lock failed",
					"chain", chainName,
					"error", err)
				return
			}
		}
	}
}
