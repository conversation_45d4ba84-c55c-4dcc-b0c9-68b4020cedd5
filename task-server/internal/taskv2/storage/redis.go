package storage

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/database/gredis"
	"task-api/internal/taskv2/interfaces"
	"task-api/internal/taskv2/types"
)

// RedisStorage Redis存储实现
type RedisStorage struct {
	client    *gredis.Redis
	keyPrefix string
}

// 确保 RedisStorage 实现了 CheckpointStorage 接口
var _ interfaces.CheckpointStorage = (*RedisStorage)(nil)

// NewRedisStorage 创建Redis存储
func NewRedisStorage(client *gredis.Redis, config *types.RedisConfig) *RedisStorage {
	return &RedisStorage{
		client:    client,
		keyPrefix: config.KeyPrefix,
	}
}

// GetCheckpoint 获取检查点
func (r *RedisStorage) GetCheckpoint(ctx context.Context, chain string) (uint64, error) {
	key := r.checkpointKey(chain)
	val, err := r.client.Get(ctx, key)
	if err != nil {
		return 0, fmt.Errorf("get checkpoint: %w", err)
	}
	if val.IsNil() {
		return 0, nil
	}
	return val.Uint64(), nil
}

// SetCheckpoint 设置检查点
func (r *RedisStorage) SetCheckpoint(ctx context.Context, chain string, blockNumber uint64) error {
	key := r.checkpointKey(chain)
	_, err := r.client.Set(ctx, key, blockNumber)
	if err != nil {
		return fmt.Errorf("set checkpoint: %w", err)
	}
	return nil
}

// AcquireLock 获取分布式锁
func (r *RedisStorage) AcquireLock(ctx context.Context, chain string, ttl time.Duration) (bool, error) {
	key := r.lockKey(chain)
	ok, err := r.client.SetNX(ctx, key, "1")
	if err != nil {
		return false, fmt.Errorf("acquire lock: %w", err)
	}
	if ok {
		// 设置过期时间
		_, err = r.client.Expire(ctx, key, int64(ttl.Seconds()))
		if err != nil {
			return false, fmt.Errorf("set lock expiration: %w", err)
		}
		return true, nil
	}
	return false, nil
}

// ReleaseLock 释放分布式锁
func (r *RedisStorage) ReleaseLock(ctx context.Context, chain string) error {
	key := r.lockKey(chain)
	_, err := r.client.Del(ctx, key)
	if err != nil {
		return fmt.Errorf("release lock: %w", err)
	}
	return nil
}

// ExtendLock 延长锁的过期时间
func (r *RedisStorage) ExtendLock(ctx context.Context, chain string, ttl time.Duration) error {
	key := r.lockKey(chain)
	
	// 使用 EXISTS 先检查锁是否存在
	exists, err := r.client.Exists(ctx, key)
	if err != nil {
		return fmt.Errorf("check lock existence: %w", err)
	}
	
	if exists == 0 {
		// 锁不存在，这可能是正常情况（锁已过期或被释放）
		// 返回特殊错误类型以便调用者区分处理
		return &LockNotExistsError{Chain: chain}
	}
	
	// 锁存在，尝试延长过期时间
	ok, err := r.client.Expire(ctx, key, int64(ttl.Seconds()))
	if err != nil {
		return fmt.Errorf("extend lock: %w", err)
	}
	if ok == 0 {
		// 在检查和延长之间锁被删除了
		return &LockNotExistsError{Chain: chain}
	}
	return nil
}

// LockNotExistsError 锁不存在错误
type LockNotExistsError struct {
	Chain string
}

func (e *LockNotExistsError) Error() string {
	return fmt.Sprintf("lock does not exist for chain %s", e.Chain)
}

// IsLockNotExistsError 检查是否是锁不存在错误
func IsLockNotExistsError(err error) bool {
	_, ok := err.(*LockNotExistsError)
	return ok
}

// SetHealthStatus 设置健康状态
func (r *RedisStorage) SetHealthStatus(ctx context.Context, chain string, status *types.HealthStatus) error {
	key := r.healthKey(chain)
	data, err := json.Marshal(status)
	if err != nil {
		return fmt.Errorf("marshal health status: %w", err)
	}
	
	err = r.client.SetEX(ctx, key, data, int64((5*time.Minute).Seconds()))
	if err != nil {
		return fmt.Errorf("set health status: %w", err)
	}
	return nil
}

// GetHealthStatus 获取健康状态
func (r *RedisStorage) GetHealthStatus(ctx context.Context, chain string) (*types.HealthStatus, error) {
	key := r.healthKey(chain)
	val, err := r.client.Get(ctx, key)
	if err != nil {
		return nil, fmt.Errorf("get health status: %w", err)
	}
	if val.IsNil() {
		return nil, nil
	}
	
	data := val.Bytes()
	var status types.HealthStatus
	if err := json.Unmarshal(data, &status); err != nil {
		return nil, fmt.Errorf("unmarshal health status: %w", err)
	}
	
	return &status, nil
}

// 生成键名
func (r *RedisStorage) checkpointKey(chain string) string {
	return fmt.Sprintf("%s:checkpoint:%s", r.keyPrefix, chain)
}

func (r *RedisStorage) lockKey(chain string) string {
	return fmt.Sprintf("%s:lock:%s", r.keyPrefix, chain)
}

func (r *RedisStorage) healthKey(chain string) string {
	return fmt.Sprintf("%s:health:%s", r.keyPrefix, chain)
}