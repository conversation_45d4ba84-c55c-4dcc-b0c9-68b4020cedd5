package storage

import (
	"context"
	"sync"
)

// CheckpointStorage 检查点存储接口
type CheckpointStorage interface {
	GetCheckpoint(ctx context.Context, chain string) (uint64, error)
	SetCheckpoint(ctx context.Context, chain string, blockNumber uint64) error
}

// MemoryCheckpointStorage 内存检查点存储（用于测试）
type MemoryCheckpointStorage struct {
	checkpoints map[string]uint64
	mu          sync.RWMutex
}

// NewMemoryCheckpointStorage 创建内存检查点存储
func NewMemoryCheckpointStorage() *MemoryCheckpointStorage {
	return &MemoryCheckpointStorage{
		checkpoints: make(map[string]uint64),
	}
}

// GetCheckpoint 获取检查点
func (m *MemoryCheckpointStorage) GetCheckpoint(ctx context.Context, chain string) (uint64, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	checkpoint, exists := m.checkpoints[chain]
	if !exists {
		return 0, nil
	}
	return checkpoint, nil
}

// SetCheckpoint 设置检查点
func (m *MemoryCheckpointStorage) SetCheckpoint(ctx context.Context, chain string, blockNumber uint64) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.checkpoints[chain] = blockNumber
	return nil
}