package confirmation

import (
	"context"
	"fmt"
	"math/big"
	"strings"
	"time"

	"task-api/internal/model/entity"
	"task-api/internal/taskv2/types"

	"github.com/sirupsen/logrus"
)

// Processor 确认处理器
type Processor struct {
	config         *types.ConfirmationTaskConfig
	chainsConfig   map[string]types.ChainConfig
	blockchainRPCs map[string]BlockchainRPC
	logger         *logrus.Logger
}

// NewProcessor 创建处理器
func NewProcessor(
	config *types.ConfirmationTaskConfig,
	chainsConfig map[string]types.ChainConfig,
	blockchainRPCs map[string]BlockchainRPC,
	logger *logrus.Logger,
) *Processor {
	return &Processor{
		config:         config,
		chainsConfig:   chainsConfig,
		blockchainRPCs: blockchainRPCs,
		logger:         logger,
	}
}

// ProcessRecharge 处理单个充值记录
func (p *Processor) ProcessRecharge(ctx context.Context, recharge *entity.UserRecharges) (*ProcessResult, error) {
	result := &ProcessResult{
		RechargeID: uint64(recharge.UserRechargesId),
		Success:    false,
	}

	// 检查链是否启用
	chainUpper := strings.ToUpper(recharge.Chan)
	if chainConfig, ok := p.config.Chains[chainUpper]; ok && !chainConfig.Enabled {
		p.logger.WithFields(logrus.Fields{
			"rechargeId": recharge.UserRechargesId,
			"chain":      recharge.Chan,
		}).Debug("chain disabled for confirmation task")
		result.FailureReason = FailureReasonChainDisabled
		result.Message = "chain is disabled for confirmation"
		return result, nil
	}

	// 获取区块链RPC客户端
	rpc, ok := p.blockchainRPCs[chainUpper]
	if !ok {
		result.FailureReason = FailureReasonRPCError
		result.Message = fmt.Sprintf("no RPC client for chain %s", recharge.Chan)
		return result, fmt.Errorf("no RPC client for chain %s", recharge.Chan)
	}

	// 检查交易状态
	txInfo, err := p.getTransactionInfo(ctx, rpc, recharge.TxHash)
	if err != nil {
		result.FailureReason = FailureReasonRPCError
		result.Message = fmt.Sprintf("failed to get transaction info: %v", err)
		return result, err
	}

	// 交易未找到
	if txInfo.Status == TransactionStatusNotFound {
		result.FailureReason = FailureReasonTxNotFound
		result.Message = "transaction not found on blockchain"
		return result, nil
	}

	// 交易失败
	if txInfo.Status == TransactionStatusFailed {
		result.FailureReason = FailureReasonTxFailed
		result.Message = "transaction failed on blockchain"
		return result, nil
	}

	// 地址验证
	// 对于代币转账，需要检查 TokenRecipient，而不是 ToAddress
	actualRecipient := txInfo.ToAddress
	if recharge.TokenContractAddress != "" && txInfo.TokenRecipient != "" {
		// 这是一个代币转账，使用 TokenRecipient
		actualRecipient = txInfo.TokenRecipient
	}
	
	if !strings.EqualFold(actualRecipient, recharge.ToAddress) {
		result.FailureReason = FailureReasonAddressMismatch
		result.Message = fmt.Sprintf("address mismatch: expected %s, got %s", recharge.ToAddress, actualRecipient)
		return result, nil
	}

	// 更新确认数
	result.Confirmations = txInfo.Confirmations

	// 获取所需确认数
	requiredConfirmations := p.getRequiredConfirmations(recharge.Chan, recharge.Name)

	// 确认数不足
	if txInfo.Confirmations < requiredConfirmations {
		result.FailureReason = FailureReasonInsufficientConfirmations
		result.Message = fmt.Sprintf("insufficient confirmations: %d/%d", txInfo.Confirmations, requiredConfirmations)
		return result, nil
	}

	// 验证最小金额
	if err := p.validateMinimumAmount(recharge); err != nil {
		result.FailureReason = FailureReasonBelowMinimum
		result.Message = err.Error()
		return result, nil
	}

	// 所有检查通过
	result.Success = true
	result.Message = "all validations passed"
	return result, nil
}

// getTransactionInfo 获取交易信息
func (p *Processor) getTransactionInfo(ctx context.Context, rpc BlockchainRPC, txHash string) (*BlockchainTransaction, error) {
	// Add retry logic for network errors
	maxRetries := 3
	var lastErr error
	var tx *Transaction

	for attempt := 0; attempt < maxRetries; attempt++ {
		// 调用RPC获取交易
		var err error
		tx, err = rpc.GetTransaction(ctx, txHash)
		if err != nil {
			// Check if this is a "transaction not found" error (no retry needed)
			if strings.Contains(err.Error(), "not found") ||
				strings.Contains(err.Error(), "invalid transaction hash format") {
				p.logger.WithFields(logrus.Fields{
					"txHash": txHash,
					"error":  err.Error(),
				}).Debug("transaction not found or invalid format")
				return &BlockchainTransaction{
					Hash:   txHash,
					Status: TransactionStatusNotFound,
				}, nil
			}

			// Check if this is a network error that should be retried
			if isRetryableNetworkError(err) && attempt < maxRetries-1 {
				p.logger.WithFields(logrus.Fields{
					"txHash":  txHash,
					"attempt": attempt + 1,
					"error":   err.Error(),
				}).Warn("retryable network error, will retry")
				lastErr = err
				time.Sleep(time.Duration(attempt+1) * time.Second) // Exponential backoff
				continue
			}

			// Non-retryable error or max retries reached
			return nil, err
		}

		// Success, break out of retry loop
		lastErr = nil
		break
	}

	// If we exhausted all retries
	if lastErr != nil {
		return nil, fmt.Errorf("failed after %d retries: %w", maxRetries, lastErr)
	}

	// 获取当前区块高度
	currentBlock, err := rpc.GetBlockNumber(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get current block: %w", err)
	}

	// 计算确认数
	confirmations := uint64(0)
	if tx.BlockNumber > 0 && currentBlock >= tx.BlockNumber {
		confirmations = currentBlock - tx.BlockNumber + 1
	}

	// 判断交易状态
	status := TransactionStatusPending
	if tx.Status == 1 {
		status = TransactionStatusSuccess
	} else if tx.Status == 0 && confirmations > 0 {
		status = TransactionStatusFailed
	}

	return &BlockchainTransaction{
		Hash:            txHash,
		Status:          status,
		Confirmations:   confirmations,
		BlockNumber:     tx.BlockNumber,
		FromAddress:     tx.From,
		ToAddress:       tx.To,
		Amount:          tx.Value,
		Timestamp:       time.Unix(int64(tx.Timestamp), 0),
		TokenRecipient:  tx.TokenRecipient,
		ContractAddress: tx.ContractAddress,
	}, nil
}

// isRetryableNetworkError checks if an error is retryable
func isRetryableNetworkError(err error) bool {
	if err == nil {
		return false
	}

	errStr := strings.ToLower(err.Error())
	return strings.Contains(errStr, "network") ||
		strings.Contains(errStr, "timeout") ||
		strings.Contains(errStr, "connection") ||
		strings.Contains(errStr, "dial") ||
		strings.Contains(errStr, "i/o timeout") ||
		strings.Contains(errStr, "context deadline exceeded") ||
		strings.Contains(errStr, "temporary failure") ||
		strings.Contains(errStr, "service unavailable")
}

// validateMinimumAmount 验证最小金额
func (p *Processor) validateMinimumAmount(recharge *entity.UserRecharges) error {
	// 获取代币的最小金额阈值
	symbol := recharge.Name
	thresholdStr, ok := p.config.Thresholds[symbol]
	if !ok {
		// 没有配置最小金额，认为通过
		p.logger.WithFields(logrus.Fields{
			"symbol": symbol,
		}).Debug("no minimum threshold configured")
		return nil
	}

	// 解析阈值 - 阈值配置的是代币单位（如 1 TRX），不是最小单位
	thresholdFloat, ok := new(big.Float).SetString(thresholdStr)
	if !ok {
		return fmt.Errorf("invalid threshold format for %s: %s", symbol, thresholdStr)
	}

	// 将浮点数金额转换为最小单位的整数
	// 获取代币小数位数
	decimals := 18 // 默认18位
	chainUpper := strings.ToUpper(recharge.Chan)
	if chainConfig, ok := p.chainsConfig[chainUpper]; ok {
		if strings.EqualFold(chainConfig.Native.Symbol, symbol) {
			decimals = chainConfig.Native.Decimals
		} else {
			for _, token := range chainConfig.Tokens {
				if strings.EqualFold(token.Symbol, symbol) {
					decimals = token.Decimals
					break
				}
			}
		}
	}

	// 转换金额到最小单位
	multiplier := new(big.Float).SetFloat64(1)
	for i := 0; i < decimals; i++ {
		multiplier.Mul(multiplier, big.NewFloat(10))
	}

	amountFloat := new(big.Float).SetFloat64(recharge.Amount)
	amountFloat.Mul(amountFloat, multiplier)

	amount := new(big.Int)
	amountFloat.Int(amount)

	// 将阈值也转换为最小单位
	thresholdFloat.Mul(thresholdFloat, multiplier)
	threshold := new(big.Int)
	thresholdFloat.Int(threshold)

	// 比较金额
	if amount.Cmp(threshold) < 0 {
		return fmt.Errorf("amount %.8f is below minimum threshold %s for %s",
			recharge.Amount, thresholdStr, symbol)
	}

	return nil
}

// getRequiredConfirmations 获取所需确认数
func (p *Processor) getRequiredConfirmations(chain, symbol string) uint64 {
	chainUpper := strings.ToUpper(chain)

	// 从链配置中获取
	if chainConfig, ok := p.chainsConfig[chainUpper]; ok {
		// 检查是否是原生代币
		if strings.EqualFold(chainConfig.Native.Symbol, symbol) {
			return chainConfig.Native.Confirmations
		}

		// 检查代币列表
		for _, token := range chainConfig.Tokens {
			if strings.EqualFold(token.Symbol, symbol) {
				return token.Confirmations
			}
		}
	}

	// 默认确认数
	return 12
}
