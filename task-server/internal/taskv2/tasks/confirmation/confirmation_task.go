package confirmation

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"task-api/internal/dao"
	"task-api/internal/model/entity"
	"task-api/internal/taskv2/interfaces"
	"task-api/internal/taskv2/monitoring"
	"task-api/internal/taskv2/types"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
	"github.com/yalks/wallet"
	"github.com/yalks/wallet/constants"
)

// ConfirmationTask 确认任务
type ConfirmationTask struct {
	config         *types.ConfirmationTaskConfig
	chainsConfig   map[string]types.ChainConfig
	processor      *Processor
	depositDAO     interfaces.DepositDAO
	blockchainRPCs map[string]BlockchainRPC
	metrics        *monitoring.Metrics
	logger         *logrus.Logger

	// 运行控制
	ticker *time.Ticker
	stopCh chan struct{}
	wg     sync.WaitGroup
}

// NewConfirmationTask 创建确认任务
func NewConfirmationTask(
	config *types.ConfirmationTaskConfig,
	chainsConfig map[string]types.ChainConfig,
	depositDAO interfaces.DepositDAO,
	blockchainRPCs map[string]BlockchainRPC,
	metrics *monitoring.Metrics,
	logger *logrus.Logger,
) *ConfirmationTask {
	processor := NewProcessor(config, chainsConfig, blockchainRPCs, logger)

	return &ConfirmationTask{
		config:         config,
		chainsConfig:   chainsConfig,
		processor:      processor,
		depositDAO:     depositDAO,
		blockchainRPCs: blockchainRPCs,
		metrics:        metrics,
		logger:         logger,
		stopCh:         make(chan struct{}),
	}
}

// Start 启动任务
func (t *ConfirmationTask) Start(ctx context.Context) error {
	if !t.config.Enabled {
		t.logger.Info("confirmation task is disabled")
		return nil
	}

	t.ticker = time.NewTicker(t.config.Interval)

	t.wg.Add(1)
	go func() {
		defer t.wg.Done()
		t.run(ctx)
	}()

	t.logger.Info("confirmation task started",
		"interval", t.config.Interval,
		"batchSize", t.config.BatchSize)

	return nil
}

// Stop 停止任务
func (t *ConfirmationTask) Stop(ctx context.Context) error {
	if t.ticker != nil {
		t.ticker.Stop()
	}

	close(t.stopCh)

	// 等待任务完成
	done := make(chan struct{})
	go func() {
		t.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		t.logger.Info("confirmation task stopped gracefully")
		return nil
	case <-ctx.Done():
		t.logger.Warn("confirmation task stop timeout")
		return ctx.Err()
	}
}

// run 运行任务主循环
func (t *ConfirmationTask) run(ctx context.Context) {
	// 立即执行一次
	t.process(ctx)

	for {
		select {
		case <-ctx.Done():
			t.logger.Debug("confirmation task stopped due to context cancellation")
			return
		case <-t.stopCh:
			t.logger.Debug("confirmation task stopped via stop channel")
			return
		case <-t.ticker.C:
			// 在处理前检查上下文
			select {
			case <-ctx.Done():
				t.logger.Debug("confirmation task stopped before processing")
				return
			default:
				t.process(ctx)
			}
		}
	}
}

// process 处理待确认的充值记录
func (t *ConfirmationTask) process(ctx context.Context) {
	startTime := time.Now()

	// 获取待确认的充值记录
	pendingRecharges, err := t.getPendingRecharges(ctx)
	if err != nil {
		t.logger.WithError(err).Error("failed to get pending recharges")
		// TODO: Add metrics recording when metrics methods are available
		return
	}

	if len(pendingRecharges) == 0 {
		return
	}

	t.logger.Info("processing pending recharges", "count", len(pendingRecharges))

	// 按链分组处理
	rechargesByChain := t.groupByChain(pendingRecharges)

	// 并发处理每个链
	var wg sync.WaitGroup
	processDone := make(chan struct{})

	for chain, recharges := range rechargesByChain {
		wg.Add(1)
		go func(chain string, recharges []*entity.UserRecharges) {
			defer wg.Done()
			t.processChainRecharges(ctx, chain, recharges)
		}(chain, recharges)
	}

	// 在goroutine中等待所有任务完成
	go func() {
		wg.Wait()
		close(processDone)
	}()

	// 等待任务完成或上下文取消
	select {
	case <-processDone:
		// 所有任务已完成
	case <-ctx.Done():
		// 上下文被取消，但仍等待正在处理的任务完成
		t.logger.Info("context cancelled, waiting for ongoing tasks to complete")
		<-processDone
	}

	// 记录处理时间
	duration := time.Since(startTime)
	// TODO: Add metrics recording when metrics methods are available

	t.logger.Info("confirmation task completed",
		"duration", duration,
		"processed", len(pendingRecharges))
}

// getPendingRecharges 获取待确认的充值记录
func (t *ConfirmationTask) getPendingRecharges(ctx context.Context) ([]*entity.UserRecharges, error) {
	// 使用dao获取待确认记录
	result, err := dao.UserRecharges.Ctx(ctx).
		Where("state = ?", RechargeStatePending).
		Limit(t.config.BatchSize).
		OrderAsc("created_at").
		All()

	if err != nil {
		return nil, err
	}

	var recharges []*entity.UserRecharges
	for _, r := range result {
		recharge := &entity.UserRecharges{}
		if err := r.Struct(recharge); err != nil {
			t.logger.WithError(err).Error("failed to parse recharge record")
			continue
		}
		recharges = append(recharges, recharge)
	}

	return recharges, nil
}

// groupByChain 按链分组
func (t *ConfirmationTask) groupByChain(recharges []*entity.UserRecharges) map[string][]*entity.UserRecharges {
	groups := make(map[string][]*entity.UserRecharges)

	for _, recharge := range recharges {
		chain := recharge.Chan
		groups[chain] = append(groups[chain], recharge)
	}

	return groups
}

// processChainRecharges 处理单个链的充值记录
func (t *ConfirmationTask) processChainRecharges(ctx context.Context, chain string, recharges []*entity.UserRecharges) {
	t.logger.Info("processing chain recharges", "chain", chain, "count", len(recharges))

	for _, recharge := range recharges {
		select {
		case <-ctx.Done():
			return
		default:
			t.processSingleRecharge(ctx, recharge)
		}
	}
}

// processSingleRecharge 处理单个充值记录
func (t *ConfirmationTask) processSingleRecharge(ctx context.Context, recharge *entity.UserRecharges) {
	logger := t.logger.WithFields(logrus.Fields{
		"rechargeId": recharge.UserRechargesId,
		"txHash":     recharge.TxHash,
		"chain":      recharge.Chan,
		"amount":     recharge.Amount,
		"symbol":     recharge.Name,
	})

	logger.Debug("processing recharge confirmation")

	// 使用处理器处理充值记录
	result, err := t.processor.ProcessRecharge(ctx, recharge)
	if err != nil {
		logger.WithError(err).Error("failed to process recharge")
		// TODO: Add metrics recording when metrics methods are available
		return
	}

	// 更新数据库
	if result.Success {
		// 确认成功，更新为完成状态并执行入账
		if err := t.updateRechargeCompleted(ctx, recharge, result.Confirmations); err != nil {
			logger.WithError(err).Error("failed to update recharge to completed")
			// TODO: Add metrics recording when metrics methods are available
			return
		}

		logger.Info("recharge confirmed successfully")
		// TODO: Add metrics recording when metrics methods are available

	} else if result.FailureReason == FailureReasonInsufficientConfirmations {
		// 确认数不足，只更新确认数
		if err := t.updateConfirmations(ctx, int64(recharge.UserRechargesId), result.Confirmations); err != nil {
			logger.WithError(err).Error("failed to update confirmations")
			// TODO: Add metrics recording when metrics methods are available
		}

		logger.Debug("insufficient confirmations", "confirmations", result.Confirmations)

	} else if result.FailureReason == FailureReasonChainDisabled {
		// 链被禁用，跳过处理
		logger.Info("skipping recharge for disabled chain",
			"chain", recharge.Chan)
		// 不更新数据库状态，保持pending状态

	} else {
		// 其他失败原因，标记为失败
		if err := t.updateRechargeFailed(ctx, int64(recharge.UserRechargesId), result.FailureReason, result.Message); err != nil {
			logger.WithError(err).Error("failed to update recharge to failed")
			// TODO: Add metrics recording when metrics methods are available
			return
		}

		logger.Warn("recharge validation failed",
			"reason", result.FailureReason,
			"message", result.Message)

		// TODO: Add metrics recording when metrics methods are available
	}
}

// updateRechargeCompleted 更新充值为完成状态并执行钱包入账
func (t *ConfirmationTask) updateRechargeCompleted(ctx context.Context, recharge *entity.UserRecharges, confirmations uint64) error {
	// 开启事务
	return g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 1. 更新充值记录状态
		_, err := tx.Ctx(ctx).Model("user_recharges").
			Where("user_recharges_id = ?", recharge.UserRechargesId).
			Data(gdb.Map{
				"state":         RechargeStateCompleted,
				"confirmations": confirmations,
				"completed_at":  time.Now(),
				"updated_at":    time.Now(),
			}).
			Update()
		if err != nil {
			return fmt.Errorf("update recharge status failed: %w", err)
		}

		// 2. 准备钱包入账请求
		amount, err := decimal.NewFromString(gconv.String(recharge.Amount))
		if err != nil {
			return fmt.Errorf("parse amount failed: %w", err)
		}

		fundReq := &constants.FundOperationRequest{
			UserID:      uint64(recharge.UserId),
			TokenSymbol: recharge.Name, // 代币符号，如 "USDT", "TRX", "ETH"
			Amount:      amount,
			BusinessID:  fmt.Sprintf("deposit_%s", recharge.TxHash),
			FundType:    constants.FundTypeDeposit,
			Description: fmt.Sprintf("区块链充值 %s", recharge.TxHash),
			RelatedID:   int64(recharge.UserRechargesId),
			Metadata: map[string]string{
				"type":                  "deposit",
				"chain":                 recharge.Chan,
				"tx_hash":               recharge.TxHash,
				"from_address":          recharge.FromAddress,
				"to_address":            recharge.ToAddress,
				"confirmations":         gconv.String(confirmations),
				"token_contract_address": recharge.TokenContractAddress,
				"operation":             "deposit",
			},
			RequestSource: "blockchain",
		}

		// 3. 执行钱包入账
		_, err = wallet.Manager().ProcessFundOperationInTx(ctx, tx, fundReq)
		if err != nil {
			return fmt.Errorf("wallet fund operation failed: %w", err)
		}

		t.logger.WithFields(logrus.Fields{
			"rechargeId": recharge.UserRechargesId,
			"userId":     recharge.UserId,
			"amount":     amount.String(),
			"token":      recharge.Name,
			"txHash":     recharge.TxHash,
		}).Info("deposit completed and credited to wallet")

		return nil
	})
}

// updateConfirmations 只更新确认数
func (t *ConfirmationTask) updateConfirmations(ctx context.Context, rechargeID int64, confirmations uint64) error {
	_, err := dao.UserRecharges.Ctx(ctx).
		Where("user_recharges_id = ?", rechargeID).
		Data(gdb.Map{
			"confirmations": confirmations,
			"updated_at":    time.Now(),
		}).
		Update()

	return err
}

// updateRechargeFailed 更新充值为失败状态
func (t *ConfirmationTask) updateRechargeFailed(ctx context.Context, rechargeID int64, reason FailureReason, message string) error {
	// 格式化失败原因，避免空字符串
	failureReason := formatFailureReason(reason, message)

	// 检查数据库是否有failure_reason字段
	// 如果没有，需要先添加
	_, err := dao.UserRecharges.Ctx(ctx).
		Where("user_recharges_id = ?", rechargeID).
		Data(gdb.Map{
			"state":          RechargeStateFailed,
			"failure_reason": failureReason,
			"updated_at":     time.Now(),
		}).
		Update()

	if err != nil && isMissingColumnError(err) {
		// 如果是缺少字段错误，尝试不带failure_reason更新
		t.logger.Warn("failure_reason column not found, updating without it")
		_, err = dao.UserRecharges.Ctx(ctx).
			Where("user_recharges_id = ?", rechargeID).
			Data(gdb.Map{
				"state":      RechargeStateFailed,
				"updated_at": time.Now(),
			}).
			Update()
	}

	return err
}

// isMissingColumnError 检查是否是缺少字段的错误
func isMissingColumnError(err error) bool {
	if err == nil {
		return false
	}
	errStr := err.Error()
	return contains(errStr, "Unknown column") || contains(errStr, "no such column")
}

func contains(s, substr string) bool {
	return len(s) >= len(substr) &&
		(s == substr || len(s) > 0 && containsHelper(s, substr))
}

func containsHelper(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

// formatFailureReason 格式化失败原因，避免空字符串
func formatFailureReason(reason FailureReason, message string) string {
	reasonStr := strings.TrimSpace(string(reason))
	message = strings.TrimSpace(message)

	if reasonStr == "" && message == "" {
		return "UNKNOWN: validation failed"
	}
	if reasonStr == "" {
		return message
	}
	if message == "" {
		return string(reason)
	}
	return fmt.Sprintf("%s: %s", reason, message)
}
