package confirmation

import (
	"context"
	"fmt"
	"math/big"
	"strings"

	"task-api/internal/taskv2/types"
	"task-api/internal/utility/crypto"

	"github.com/ethereum/go-ethereum/common"
	ethTypes "github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/fbsobreira/gotron-sdk/pkg/address"
	"github.com/fbsobreira/gotron-sdk/pkg/proto/core"
	"github.com/golang/protobuf/proto"
	"github.com/sirupsen/logrus"
)

// BlockchainRPC 区块链RPC接口
type BlockchainRPC interface {
	GetTransaction(ctx context.Context, txHash string) (*Transaction, error)
	GetBlockNumber(ctx context.Context) (uint64, error)
}

// Transaction 交易信息
type Transaction struct {
	Hash            string
	BlockNumber     uint64
	From            string
	To              string
	Value           string
	Status          uint64 // 1 = success, 0 = failed
	Timestamp       uint64
	TokenRecipient  string // ERC20/TRC20 token recipient (if applicable)
	ContractAddress string // Contract address (for token transfers)
}

// ETHBlockchainRPC ETH区块链RPC实现
type ETHBlockchainRPC struct {
	client *ethclient.Client
	logger *logrus.Logger
}

// NewETHBlockchainRPC 创建ETH RPC客户端
func NewETHBlockchainRPC(rpcURL string, logger *logrus.Logger) (*ETHBlockchainRPC, error) {
	client, err := ethclient.Dial(rpcURL)
	if err != nil {
		return nil, fmt.Errorf("dial eth client: %w", err)
	}

	return &ETHBlockchainRPC{
		client: client,
		logger: logger,
	}, nil
}

// GetTransaction 获取交易信息
func (e *ETHBlockchainRPC) GetTransaction(ctx context.Context, txHash string) (*Transaction, error) {
	hash := common.HexToHash(txHash)

	// 获取交易信息
	tx, isPending, err := e.client.TransactionByHash(ctx, hash)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return nil, fmt.Errorf("transaction not found")
		}
		return nil, err
	}

	if isPending {
		return &Transaction{
			Hash:   txHash,
			Status: 0,
		}, nil
	}

	// 获取交易收据
	receipt, err := e.client.TransactionReceipt(ctx, hash)
	if err != nil {
		return nil, fmt.Errorf("get receipt: %w", err)
	}

	// 获取区块信息以获取时间戳
	block, err := e.client.BlockByNumber(ctx, big.NewInt(int64(receipt.BlockNumber.Uint64())))
	if err != nil {
		return nil, fmt.Errorf("get block: %w", err)
	}

	// 获取from地址
	from, err := ethTypes.Sender(ethTypes.LatestSignerForChainID(tx.ChainId()), tx)
	if err != nil {
		return nil, fmt.Errorf("get sender: %w", err)
	}

	// 获取to地址
	to := ""
	if tx.To() != nil {
		to = tx.To().Hex()
	}

	// 检查是否为 ERC20 转账
	tokenRecipient := ""
	contractAddress := ""
	
	// ERC20 Transfer event signature: Transfer(address,address,uint256)
	transferEventSig := common.HexToHash("0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef")
	
	for _, log := range receipt.Logs {
		if len(log.Topics) >= 3 && log.Topics[0] == transferEventSig {
			// 这是一个 Transfer 事件
			// Topics[1] = from address (padded to 32 bytes)
			// Topics[2] = to address (padded to 32 bytes)
			contractAddress = log.Address.Hex()
			
			// 提取接收者地址（去除前面的零填充）
			if len(log.Topics[2]) == 32 {
				tokenRecipient = common.BytesToAddress(log.Topics[2].Bytes()).Hex()
			}
			break
		}
	}

	return &Transaction{
		Hash:            txHash,
		BlockNumber:     receipt.BlockNumber.Uint64(),
		From:            from.Hex(),
		To:              to,
		Value:           tx.Value().String(),
		Status:          receipt.Status,
		Timestamp:       block.Time(),
		TokenRecipient:  tokenRecipient,
		ContractAddress: contractAddress,
	}, nil
}

// GetBlockNumber 获取当前区块号
func (e *ETHBlockchainRPC) GetBlockNumber(ctx context.Context) (uint64, error) {
	return e.client.BlockNumber(ctx)
}

// TRONBlockchainRPC TRON区块链RPC实现
type TRONBlockchainRPC struct {
	client *crypto.TRONClient
	logger *logrus.Logger
}

// NewTRONBlockchainRPC 创建TRON RPC客户端
func NewTRONBlockchainRPC(rpcURL string, apiKey string, logger *logrus.Logger) (*TRONBlockchainRPC, error) {
	config := &crypto.ClientConfig{
		RPCUrl: rpcURL,
		APIKey: apiKey,
		UseTLS: false, // QuikNode and other mainnet endpoints require TLS
	}

	client, err := crypto.NewTRONClient(config)
	if err != nil {
		return nil, fmt.Errorf("create TRON client: %w", err)
	}

	return &TRONBlockchainRPC{
		client: client,
		logger: logger,
	}, nil
}

// GetTransaction 获取交易信息
func (t *TRONBlockchainRPC) GetTransaction(ctx context.Context, txHash string) (*Transaction, error) {
	// Remove 0x prefix if present
	txHash = strings.TrimPrefix(txHash, "0x")

	// Validate transaction hash format
	if len(txHash) != 64 {
		t.logger.WithFields(logrus.Fields{
			"txHash":      txHash,
			"expectedLen": 64,
			"actualLen":   len(txHash),
		}).Warn("invalid transaction hash format")
		return nil, fmt.Errorf("invalid transaction hash format: expected 64 characters, got %d", len(txHash))
	}

	// Get transaction info
	txInfo, err := t.client.GetTransactionInfoByID(ctx, txHash)
	if err != nil {
		// Check if this is a "transaction not found" error
		if strings.Contains(err.Error(), "transaction not found") ||
			strings.Contains(err.Error(), "TRANSACTION_FAILED") {
			t.logger.WithFields(logrus.Fields{
				"txHash": txHash,
				"error":  err.Error(),
			}).Debug("transaction not found on blockchain")
			return nil, fmt.Errorf("transaction not found")
		}

		t.logger.WithError(err).WithField("txHash", txHash).Error("failed to get transaction info")
		return nil, fmt.Errorf("get transaction info: %w", err)
	}

	// Get transaction details
	tx, err := t.client.GetTransactionByID(ctx, txHash)
	if err != nil {
		// Check if this is a "transaction not found" error
		if strings.Contains(err.Error(), "transaction not found") ||
			strings.Contains(err.Error(), "TRANSACTION_FAILED") {
			t.logger.WithFields(logrus.Fields{
				"txHash": txHash,
				"error":  err.Error(),
			}).Debug("transaction details not found on blockchain")
			return nil, fmt.Errorf("transaction not found")
		}

		t.logger.WithError(err).WithField("txHash", txHash).Error("failed to get transaction")
		return nil, fmt.Errorf("get transaction: %w", err)
	}

	// Extract from and to addresses
	from := ""
	to := ""
	value := "0"

	if tx != nil && tx.RawData != nil && len(tx.RawData.Contract) > 0 {
		contract := tx.RawData.Contract[0]
		if contract.Parameter != nil && contract.Parameter.Value != nil {
			// Handle different contract types
			switch core.Transaction_Contract_ContractType(contract.Type) {
			case core.Transaction_Contract_TransferContract:
				// Parse TRX transfer
				transferContract := &core.TransferContract{}
				if err := proto.Unmarshal(contract.Parameter.Value, transferContract); err != nil {
					t.logger.WithError(err).Warn("failed to unmarshal transfer contract")
				} else {
					// Convert addresses from hex to base58
					if ownerAddr := transferContract.GetOwnerAddress(); ownerAddr != nil {
						from = address.Address(ownerAddr).String()
					}
					if toAddr := transferContract.GetToAddress(); toAddr != nil {
						to = address.Address(toAddr).String()
					}
					value = fmt.Sprintf("%d", transferContract.GetAmount())
				}

			case core.Transaction_Contract_TriggerSmartContract:
				// Parse smart contract call (including TRC20 transfers)
				triggerContract := &core.TriggerSmartContract{}
				if err := proto.Unmarshal(contract.Parameter.Value, triggerContract); err != nil {
					t.logger.WithError(err).Warn("failed to unmarshal trigger contract")
				} else {
					// Convert addresses from hex to base58
					if ownerAddr := triggerContract.GetOwnerAddress(); ownerAddr != nil {
						from = address.Address(ownerAddr).String()
					}

					// For TRC20 transfers, parse the actual recipient from the data
					if contractAddr := triggerContract.GetContractAddress(); contractAddr != nil {
						contractAddrBase58 := address.Address(contractAddr).String()

						// Check if this is a TRC20 transfer by parsing the data
						data := triggerContract.GetData()
						if len(data) >= 68 { // 4 bytes method + 32 bytes address + 32 bytes amount
							// Check if this is a transfer method (0xa9059cbb)
							if len(data) >= 4 &&
								data[0] == 0xa9 && data[1] == 0x05 && data[2] == 0x9c && data[3] == 0xbb {
								// This is a TRC20 transfer, extract the recipient address
								if len(data) >= 36 { // 4 + 32 bytes
									// The recipient address is in bytes 4-35, but we need the last 20 bytes
									recipientHex := data[16:36] // Skip first 12 zero bytes, take last 20 bytes
									// Convert to TRON address format
									recipientAddr := make([]byte, 21)
									recipientAddr[0] = 0x41 // TRON address prefix
									copy(recipientAddr[1:], recipientHex)
									to = address.Address(recipientAddr).String()

									// Also extract the amount for TRC20 transfers
									if len(data) >= 68 {
										amountBytes := data[36:68]
										amountBigInt := new(big.Int).SetBytes(amountBytes)
										value = amountBigInt.String()
									}
								} else {
									// Fallback to contract address if parsing fails
									to = contractAddrBase58
								}
							} else {
								// Not a TRC20 transfer, use contract address
								to = contractAddrBase58
							}
						} else {
							// Data too short, use contract address
							to = contractAddrBase58
						}
					}
				}
			}
		}
	}

	// Determine transaction status (SUCCESS = 1, FAILED = 0)
	status := uint64(0)
	if txInfo != nil && txInfo.Result == 0 { // TRON uses 0 for success
		status = 1
	}

	// Get block number
	blockNumber := uint64(0)
	if txInfo != nil {
		blockNumber = uint64(txInfo.BlockNumber)
	}

	// Get timestamp
	timestamp := uint64(0)
	if txInfo != nil {
		timestamp = uint64(txInfo.BlockTimeStamp / 1000) // Convert from milliseconds to seconds
	}

	return &Transaction{
		Hash:        "0x" + txHash,
		BlockNumber: blockNumber,
		From:        from,
		To:          to,
		Value:       value,
		Status:      status,
		Timestamp:   timestamp,
	}, nil
}

// GetBlockNumber 获取当前区块号
func (t *TRONBlockchainRPC) GetBlockNumber(ctx context.Context) (uint64, error) {
	blockNumber, err := t.client.GetCurrentBlock(ctx)
	if err != nil {
		return 0, fmt.Errorf("get current block: %w", err)
	}

	return blockNumber, nil
}

// CreateBlockchainRPCs 创建区块链RPC客户端映射
func CreateBlockchainRPCs(chainsConfig map[string]types.ChainConfig, logger *logrus.Logger) (map[string]BlockchainRPC, error) {
	rpcs := make(map[string]BlockchainRPC)

	for chainName, config := range chainsConfig {
		if !config.Enabled {
			continue
		}

		switch strings.ToUpper(chainName) {
		case "ETH":
			rpc, err := NewETHBlockchainRPC(config.RpcURL, logger)
			if err != nil {
				logger.WithError(err).Warn("failed to create ETH RPC client")
				continue
			}
			rpcs["ETH"] = rpc

		case "TRON":
			rpc, err := NewTRONBlockchainRPC(config.RpcURL, config.ApiKey, logger)
			if err != nil {
				logger.WithError(err).Warn("failed to create TRON RPC client")
				continue
			}
			rpcs["TRON"] = rpc

		default:
			logger.Warn("unsupported chain", "chain", chainName)
		}
	}

	return rpcs, nil
}
