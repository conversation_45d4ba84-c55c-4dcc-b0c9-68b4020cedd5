package confirmation

import (
	"time"
)

// FailureReason 失败原因枚举
type FailureReason string

const (
	// FailureReasonTxNotFound 交易未找到
	FailureReasonTxNotFound FailureReason = "TX_NOT_FOUND"
	
	// FailureReasonTxFailed 交易失败
	FailureReasonTxFailed FailureReason = "TX_FAILED"
	
	// FailureReasonInsufficientConfirmations 确认数不足
	FailureReasonInsufficientConfirmations FailureReason = "INSUFFICIENT_CONFIRMATIONS"
	
	// FailureReasonBelowMinimum 金额低于最小值
	FailureReasonBelowMinimum FailureReason = "BELOW_MINIMUM_AMOUNT"
	
	// FailureReasonAddressMismatch 地址不匹配
	FailureReasonAddressMismatch FailureReason = "ADDRESS_MISMATCH"
	
	// FailureReasonRPCError RPC错误
	FailureReasonRPCError FailureReason = "RPC_ERROR"
	
	// FailureReasonInvalidAmount 无效金额
	FailureReasonInvalidAmount FailureReason = "INVALID_AMOUNT"
	
	// FailureReasonChainDisabled 链被禁用
	FailureReasonChainDisabled FailureReason = "CHAIN_DISABLED"
)

// TransactionStatus 交易状态
type TransactionStatus string

const (
	// TransactionStatusPending 待确认
	TransactionStatusPending TransactionStatus = "pending"
	
	// TransactionStatusSuccess 成功
	TransactionStatusSuccess TransactionStatus = "success"
	
	// TransactionStatusFailed 失败
	TransactionStatusFailed TransactionStatus = "failed"
	
	// TransactionStatusNotFound 未找到
	TransactionStatusNotFound TransactionStatus = "not_found"
)

// BlockchainTransaction 区块链交易信息
type BlockchainTransaction struct {
	Hash            string
	Status          TransactionStatus
	Confirmations   uint64
	BlockNumber     uint64
	FromAddress     string
	ToAddress       string
	Amount          string
	Timestamp       time.Time
	TokenRecipient  string // ERC20/TRC20 代币接收者地址
	ContractAddress string // 代币合约地址
}

// ProcessResult 处理结果
type ProcessResult struct {
	RechargeID    uint64
	Success       bool
	FailureReason FailureReason
	Message       string
	Confirmations uint64
}

// RechargeState 充值状态
const (
	RechargeStatePending   = 1 // 待确认
	RechargeStateCompleted = 2 // 已完成
	RechargeStateFailed    = 3 // 失败
)