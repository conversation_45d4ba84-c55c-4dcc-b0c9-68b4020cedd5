package config

import (
	"fmt"
	"os"
	"time"

	"gopkg.in/yaml.v2"
	"task-api/internal/taskv2/types"
)

// LoadConfig 加载配置文件
func LoadConfig(configPath string) (*types.Config, error) {
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("read config file: %w", err)
	}
	
	// 替换环境变量
	// configStr := os.ExpandEnv(string(data))
	
	var config types.Config
	if err := yaml.Unmarshal([]byte(data), &config); err != nil {
		return nil, fmt.Errorf("unmarshal config: %w", err)
	}
	
	// 解析时间字符串
	if err := parseTimeValues(&config); err != nil {
		return nil, fmt.Errorf("parse time values: %w", err)
	}
	
	// 验证配置
	if err := ValidateConfig(&config); err != nil {
		return nil, fmt.Errorf("validate config: %w", err)
	}
	
	return &config, nil
}

// parseTimeValues 解析配置中的时间字符串
func parseTimeValues(config *types.Config) error {
	// 全局配置已经使用了 time.Duration，yaml 会自动解析
	// 这里可以添加其他需要特殊处理的时间值
	return nil
}

// ValidateConfig 验证配置
func ValidateConfig(config *types.Config) error {
	// 验证全局配置
	if config.Global.ScanInterval <= 0 {
		return fmt.Errorf("invalid scan interval: %v", config.Global.ScanInterval)
	}
	
	if config.Global.MaxRetries < 0 {
		return fmt.Errorf("invalid max retries: %d", config.Global.MaxRetries)
	}
	
	// 验证链配置
	enabledChains := 0
	for chainName, chainConfig := range config.Chains {
		if !chainConfig.Enabled {
			continue
		}
		enabledChains++
		
		if err := validateChainConfig(chainName, &chainConfig); err != nil {
			return fmt.Errorf("chain %s: %w", chainName, err)
		}
	}
	
	if enabledChains == 0 {
		return fmt.Errorf("no chains enabled")
	}
	
	// 验证存储配置
	if config.Storage.Redis.KeyPrefix == "" {
		return fmt.Errorf("redis key prefix is required")
	}
	
	// 验证监控配置
	if config.Monitoring.MetricsPort <= 0 || config.Monitoring.MetricsPort > 65535 {
		return fmt.Errorf("invalid metrics port: %d", config.Monitoring.MetricsPort)
	}
	
	return nil
}

// validateChainConfig 验证链配置
func validateChainConfig(chainName string, config *types.ChainConfig) error {
	if config.RpcURL == "" {
		return fmt.Errorf("rpc url is required")
	}
	
	if config.ScanBatchSize <= 0 {
		return fmt.Errorf("invalid scan batch size: %d", config.ScanBatchSize)
	}
	
	// 验证原生代币配置
	if config.Native.Enabled {
		if err := validateTokenConfig(&config.Native, true); err != nil {
			return fmt.Errorf("native token: %w", err)
		}
	}
	
	// 验证代币配置
	enabledTokens := 0
	for i, token := range config.Tokens {
		if !token.Enabled {
			continue
		}
		enabledTokens++
		
		if err := validateTokenConfig(&token, false); err != nil {
			return fmt.Errorf("token %d: %w", i, err)
		}
	}
	
	// 至少要有一个代币启用
	if !config.Native.Enabled && enabledTokens == 0 {
		return fmt.Errorf("at least one token must be enabled")
	}
	
	return nil
}

// validateTokenConfig 验证代币配置
func validateTokenConfig(config *types.TokenConfig, isNative bool) error {
	if config.Symbol == "" {
		return fmt.Errorf("symbol is required")
	}
	
	if !isNative && config.ContractAddress == "" {
		return fmt.Errorf("contract address is required for non-native token")
	}
	
	if config.Decimals <= 0 || config.Decimals > 18 {
		return fmt.Errorf("invalid decimals: %d", config.Decimals)
	}
	
	if config.Confirmations == 0 {
		return fmt.Errorf("confirmations must be greater than 0")
	}
	
	return nil
}

// MergeWithDefaults 合并默认配置
func MergeWithDefaults(config *types.Config) *types.Config {
	// 设置默认值
	if config.Global.LogLevel == "" {
		config.Global.LogLevel = "info"
	}
	
	if config.Global.ScanInterval == 0 {
		config.Global.ScanInterval = 10 * time.Second
	}
	
	if config.Global.HealthCheckInterval == 0 {
		config.Global.HealthCheckInterval = 30 * time.Second
	}
	
	if config.Global.MaxRetries == 0 {
		config.Global.MaxRetries = 3
	}
	
	if config.Global.RequestTimeout == 0 {
		config.Global.RequestTimeout = 30 * time.Second
	}
	
	if config.Storage.Redis.LockTimeout == 0 {
		config.Storage.Redis.LockTimeout = 60 * time.Second
	}
	
	if config.Monitoring.Alerting.TimeWindow == 0 {
		config.Monitoring.Alerting.TimeWindow = 5 * time.Minute
	}
	
	return config
}