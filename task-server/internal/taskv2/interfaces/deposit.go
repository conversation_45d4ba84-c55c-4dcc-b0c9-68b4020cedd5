package interfaces

import (
	"context"

	"github.com/shopspring/decimal"
)

// DepositManager 存款管理接口
type DepositManager interface {
	// 存款状态管理
	CreateDeposit(ctx context.Context, deposit *Deposit) error
	BatchCreateDeposits(ctx context.Context, deposits []*Deposit) error
	UpdateDepositStatus(ctx context.Context, txHash string, status DepositStatus) error
	GetPendingDeposits(ctx context.Context, chain string) ([]*Deposit, error)

	// 确认检查
	CheckConfirmations(ctx context.Context, deposit *Deposit, currentBlock uint64) (uint64, error)
	ProcessConfirmedDeposit(ctx context.Context, deposit *Deposit) error
	BatchProcessConfirmedDeposits(ctx context.Context, deposits []*Deposit) error

	// 重复检查
	IsDepositExists(ctx context.Context, txHash string) (bool, error)
}

// DepositDAO 存款数据访问接口
type DepositDAO interface {
	CreateDeposit(deposit *Deposit) error
	BatchInsertDeposits(deposits []*Deposit) error
	GetDepositsByTxHashes(txHashes []string) ([]*Deposit, error)
	GetActiveDepositAddresses(chain string) ([]*UserAddress, error)
	UpdateDepositStatus(txHash string, status DepositStatus) error
	GetPendingDeposits(chain string) ([]*Deposit, error)
}

// Deposit 存款记录
type Deposit struct {
	ID                  uint            `json:"id"`
	TxHash              string          `json:"tx_hash"`
	Chain               string          `json:"chain"`
	Token               string          `json:"token"`
	FromAddress         string          `json:"from_address"`
	ToAddress           string          `json:"to_address"`
	Amount              decimal.Decimal `json:"amount"`
	BlockNumber         uint64          `json:"block_number"`
	Status              DepositStatus   `json:"status"`
	Confirmations       uint64          `json:"confirmations"`
	RequiredConfirms    uint64          `json:"required_confirms"`
	UserID              uint            `json:"user_id"`
	TokenID             uint            `json:"token_id"`
	TokenContractAddress string         `json:"token_contract_address"` // 代币合约地址
	CreatedAt           int64           `json:"created_at"`
	UpdatedAt           int64           `json:"updated_at"`
}

// DepositStatus 存款状态
type DepositStatus string

const (
	DepositStatusDetected  DepositStatus = "detected"  // 刚检测到
	DepositStatusPending   DepositStatus = "pending"   // 等待确认
	DepositStatusConfirmed DepositStatus = "confirmed" // 已确认
	DepositStatusProcessed DepositStatus = "processed" // 已到账
	DepositStatusFailed    DepositStatus = "failed"    // 处理失败
)

// UserAddress 用户地址信息
type UserAddress struct {
	ID        uint   `json:"id"`
	UserID    uint   `json:"user_id"`
	Chain     string `json:"chain"`
	Address   string `json:"address"`
	TokenID   uint   `json:"token_id"`
	IsActive  bool   `json:"is_active"`
	CreatedAt int64  `json:"created_at"`
}