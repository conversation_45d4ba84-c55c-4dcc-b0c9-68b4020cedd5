package interfaces

import (
	"context"
	"time"
)

// ChainScanner 区块链扫描器接口
type ChainScanner interface {
	// 基础信息
	GetChainName() string
	GetSupportedTokens() []string

	// 核心扫描方法 - 处理完整的存款生命周期
	ScanAndProcess(ctx context.Context) error

	// 区块链交互
	GetCurrentBlock(ctx context.Context) (uint64, error)

	// 状态管理
	GetLastProcessedBlock() (uint64, error)
	UpdateCheckpoint(blockNumber uint64) error
	
	// 地址管理
	RefreshMonitoredAddresses() error

	// 健康检查
	HealthCheck(ctx context.Context) error
	GetMetrics() ScannerMetrics

	// 生命周期
	Start(ctx context.Context) error
	Stop(ctx context.Context) error
}

// ScannerMetrics 扫描器指标
type ScannerMetrics struct {
	ChainName          string
	LastProcessedBlock uint64
	CurrentBlock       uint64
	PendingDeposits    int
	ProcessedDeposits  int64
	ErrorCount         int64
	LastError          error
	LastErrorTime      time.Time
	ScanLatency        time.Duration
	ProcessingRate     float64 // deposits per second
}