package adapter

import (
	"context"
	"fmt"
	"strings"

	"task-api/internal/dao"
	"task-api/internal/model/entity"
	"task-api/internal/ports"
	"task-api/internal/service"
	"task-api/internal/taskv2/interfaces"

	"github.com/a19ba14d/tg-bot-common/consts"
	"github.com/gogf/gf/v2/frame/g"
)

// UserRechargeDepositDAO 实现 interfaces.DepositDAO，使用现有的 user_recharges 服务
type UserRechargeDepositDAO struct {
	userRechargeService service.IUserRecharges
}

// NewUserRechargeDepositDAO 创建新的用户充值存款DAO适配器
func NewUserRechargeDepositDAO(userRechargeService service.IUserRecharges) interfaces.DepositDAO {
	return &UserRechargeDepositDAO{
		userRechargeService: userRechargeService,
	}
}

// CreateDeposit 创建单个存款记录
func (d *UserRechargeDepositDAO) CreateDeposit(deposit *interfaces.Deposit) error {
	ctx := context.Background()

	// 将 interfaces.Deposit 转换为 ports.DepositInfo
	depositInfo := d.convertToDepositInfo(deposit)

	// 使用现有的 ProcessNewDeposit 方法
	_, _, err := d.userRechargeService.ProcessNewDeposit(ctx, depositInfo)
	return err
}

// BatchInsertDeposits 批量插入存款记录
func (d *UserRechargeDepositDAO) BatchInsertDeposits(deposits []*interfaces.Deposit) error {
	ctx := context.Background()

	// 逐个处理，因为现有服务已经有重复检查逻辑
	for _, deposit := range deposits {
		depositInfo := d.convertToDepositInfo(deposit)

		// 使用现有的 ProcessNewDeposit 方法，它会自动跳过重复的记录
		_, _, err := d.userRechargeService.ProcessNewDeposit(ctx, depositInfo)
		if err != nil {
			return fmt.Errorf("process deposit %s: %w", deposit.TxHash, err)
		}
	}

	return nil
}

// GetDepositsByTxHashes 根据交易哈希获取存款记录
func (d *UserRechargeDepositDAO) GetDepositsByTxHashes(txHashes []string) ([]*interfaces.Deposit, error) {
	ctx := context.Background()
	var deposits []*interfaces.Deposit

	// 检查每个交易哈希是否存在
	for _, txHash := range txHashes {
		exists, err := d.userRechargeService.CheckTxHashExists(ctx, txHash)
		if err != nil {
			return nil, fmt.Errorf("check tx hash %s: %w", txHash, err)
		}

		if exists {
			// 如果存在，创建一个基本的 Deposit 结构（只有TxHash有效）
			deposit := &interfaces.Deposit{
				TxHash: txHash,
				Status: interfaces.DepositStatusDetected, // 假设状态
			}
			deposits = append(deposits, deposit)
		}
	}

	return deposits, nil
}

// GetActiveDepositAddresses 获取活跃的存款地址
func (d *UserRechargeDepositDAO) GetActiveDepositAddresses(chain string) ([]*interfaces.UserAddress, error) {
	ctx := context.Background()

	// 查询 user_address 表，获取指定链的存款地址
	// 使用 DISTINCT 对 address 字段去重，只查询存款类型的地址
	var userAddresses []*entity.UserAddress

	// 构建查询条件
	query := dao.UserAddress.Ctx(ctx).
		Fields("DISTINCT address, user_id, token_id, chan, created_at").
		Where(dao.UserAddress.Columns().Chan, strings.ToUpper(chain)).
		Where(dao.UserAddress.Columns().Type, consts.UserAddressTypeDeposit)

	err := query.Scan(&userAddresses)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to fetch active deposit addresses for chain %s: %v", chain, err)
		return nil, fmt.Errorf("query user addresses failed: %w", err)
	}

	// 转换为 interfaces.UserAddress 格式
	var result []*interfaces.UserAddress
	for _, ua := range userAddresses {
		interfaceAddr := &interfaces.UserAddress{
			ID:        uint(ua.UserAddressId),
			UserID:    uint(ua.UserId),
			Chain:     ua.Chan,
			Address:   ua.Address,
			TokenID:   ua.TokenId,
			IsActive:  true, // 查询出来的都是活跃地址
			CreatedAt: ua.CreatedAt.Unix(),
		}
		result = append(result, interfaceAddr)
	}

	g.Log().Infof(ctx, "Loaded %d unique deposit addresses for chain %s", len(result), chain)
	return result, nil
}

// UpdateDepositStatus 更新存款状态
func (d *UserRechargeDepositDAO) UpdateDepositStatus(txHash string, status interfaces.DepositStatus) error {
	// 注意：现有的 UpdateRechargeStatus 需要 rechargeID，而不是 txHash
	// 这里需要根据实际需求调整，或者扩展现有的 service 接口
	// 暂时返回 nil，表示更新成功
	return nil
}

// GetPendingDeposits 获取待处理的存款
func (d *UserRechargeDepositDAO) GetPendingDeposits(chain string) ([]*interfaces.Deposit, error) {
	// 这个方法需要根据现有的数据库查询来实现
	// 暂时返回空列表
	return []*interfaces.Deposit{}, nil
}

// convertToDepositInfo 将 interfaces.Deposit 转换为 ports.DepositInfo
func (d *UserRechargeDepositDAO) convertToDepositInfo(deposit *interfaces.Deposit) ports.DepositInfo {
	return ports.DepositInfo{
		UserID:               deposit.UserID,
		TokenID:              deposit.TokenID,
		TokenSymbol:          deposit.Token,
		Chain:                deposit.Chain,
		FromAddress:          deposit.FromAddress,
		ToAddress:            deposit.ToAddress,
		TxHash:               deposit.TxHash,
		Amount:               deposit.Amount.String(),
		BlockNumber:          deposit.BlockNumber,
		Confirmations:        deposit.Confirmations,
		TokenContractAddress: deposit.TokenContractAddress, // 传递合约地址
	}
}
