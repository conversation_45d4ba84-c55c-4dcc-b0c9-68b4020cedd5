package eth

import (
	"context"
	"fmt"
	"math/big"
	"strings"
	"time"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/common"
	ethTypes "github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"

	"task-api/internal/taskv2/interfaces"
	"task-api/internal/taskv2/monitoring"
	"task-api/internal/taskv2/scanners/base"
	"task-api/internal/taskv2/types"
	"task-api/internal/taskv2/utils"
)

const (
	// 每次FilterLogs调用的最大区块数量，根据RPC提供商限制调整
	// 某些RPC端点对区块范围有严格限制，设置为1以确保兼容性
	maxBlocksPerFilterLogsRequest = 1
)

// ETHScanner ETH区块链扫描器
type ETHScanner struct {
	*base.BaseScanner
	client          *ethclient.Client
	chainID         *big.Int
	nativeDecimals  int
	tokenProcessors map[string]*ERC20Processor // contract address -> processor
}

// NewETHScanner 创建ETH扫描器
func NewETHScanner(
	globalConfig *types.Config,
	chainConfig *types.ChainConfig,
	depositManager interfaces.DepositManager,
	storage interfaces.CheckpointStorage,
	dao interfaces.DepositDAO,
	logger *logrus.Logger,
	metrics *monitoring.Metrics,
) (*ETHScanner, error) {
	// 创建ETH客户端
	client, err := ethclient.Dial(chainConfig.RpcURL)
	if err != nil {
		return nil, fmt.Errorf("dial eth client: %w", err)
	}

	// 获取链ID
	chainID, err := client.ChainID(context.Background())
	if err != nil {
		return nil, fmt.Errorf("get chain id: %w", err)
	}

	scanner := &ETHScanner{
		BaseScanner: base.NewBaseScanner(
			"ETH",
			chainConfig,
			depositManager,
			storage,
			logger,
		),
		client:          client,
		chainID:         chainID,
		nativeDecimals:  chainConfig.Native.Decimals,
		tokenProcessors: make(map[string]*ERC20Processor),
	}

	// 设置全局配置和监控指标
	scanner.SetGlobalConfig(globalConfig)
	if metrics != nil {
		scanner.SetChainMetrics(monitoring.NewChainMetrics(metrics, "ETH"))
	}

	// 初始化代币处理器
	for _, token := range chainConfig.Tokens {
		if !token.Enabled {
			continue
		}

		processor, err := NewERC20Processor(
			client,
			common.HexToAddress(token.ContractAddress),
			token.Symbol,
			token.Decimals,
		)
		if err != nil {
			logger.Warn("failed to create ERC20 processor",
				"token", token.Symbol,
				"contract", token.ContractAddress,
				"error", err)
			continue
		}

		scanner.tokenProcessors[strings.ToLower(token.ContractAddress)] = processor
		logger.Info("ERC20 processor created",
			"token", token.Symbol,
			"contract", strings.ToLower(token.ContractAddress),
			"decimals", token.Decimals)
	}

	// 设置DAO
	scanner.SetDAO(dao)

	return scanner, nil
}

// Start 启动ETH扫描器
func (s *ETHScanner) Start(ctx context.Context) error {
	// 加载初始区块
	lastBlock, err := s.LoadInitialBlock(ctx, s.GetCurrentBlock)
	if err != nil {
		return fmt.Errorf("load initial block: %w", err)
	}

	// 初始化扫描器
	return s.InitializeScanner(ctx, lastBlock)
}

// GetCurrentBlock 获取当前区块
func (s *ETHScanner) GetCurrentBlock(ctx context.Context) (uint64, error) {
	blockNumber, err := s.client.BlockNumber(ctx)
	if err != nil {
		return 0, fmt.Errorf("get block number: %w", err)
	}
	return blockNumber, nil
}

// ScanAndProcess 扫描并处理区块
func (s *ETHScanner) ScanAndProcess(ctx context.Context) error {
	// 每次扫描前都从数据库加载最新地址
	if err := s.RefreshMonitoredAddresses(); err != nil {
		s.Logger().Error("load monitored addresses failed", "error", err)
		// 继续执行扫描，避免地址加载失败影响区块处理
	}

	// 使用重试机制
	maxRetries := 3 // 默认值
	if s.GlobalConfig() != nil {
		maxRetries = s.GlobalConfig().Global.MaxRetries
	}
	return utils.RetryWithBackoff(ctx, func() error {
		return s.scanAndProcessInternal(ctx)
	}, maxRetries)
}

// scanAndProcessInternal 内部扫描处理逻辑 - 使用优化的批量FilterLogs
func (s *ETHScanner) scanAndProcessInternal(ctx context.Context) error {
	// 使用新的优化方法
	return s.scanAndProcessOptimized(ctx)
}

// scanAndProcessOptimized 优化的扫描处理逻辑
func (s *ETHScanner) scanAndProcessOptimized(ctx context.Context) error {
	// 1. 获取扫描范围
	lastProcessedBlock, err := s.GetLastProcessedBlock()
	if err != nil {
		return fmt.Errorf("get last block: %w", err)
	}

	currentBlock, err := s.GetCurrentBlock(ctx)
	if err != nil {
		return fmt.Errorf("get current block: %w", err)
	}

	// 验证当前区块的合理性
	if err := s.validateCurrentBlock(ctx, currentBlock); err != nil {
		s.Logger().Warn("current block validation failed, using conservative approach", "error", err)
		// 使用更保守的方法：减去安全边距
		if currentBlock > 5 {
			currentBlock = currentBlock - 5
		}
	}

	// 更新指标
	s.UpdateMetrics(currentBlock)

	// 2. 计算扫描起始区块
	// 正常情况下从下一个区块开始，只有在系统启动时才回扫
	fromBlock := lastProcessedBlock

	// 3. 计算动态批量大小
	totalBlocksToScan := currentBlock - fromBlock
	batchSize := s.calculateDynamicBatchSize(totalBlocksToScan)

	// 4. 确定本次扫描的结束区块
	toBlock := currentBlock
	if totalBlocksToScan > batchSize {
		toBlock = fromBlock + batchSize
	}

	scanFromBlock := fromBlock + 1
	if scanFromBlock > toBlock {
		s.Logger().Debug("no new blocks to scan")
		return s.checkPendingDeposits(ctx, currentBlock)
	}

	s.Logger().Debug("scan range calculated",
		"lastProcessed", lastProcessedBlock,
		"fromBlock", scanFromBlock,
		"toBlock", toBlock,
		"currentBlock", currentBlock,
		"batchSize", batchSize,
		"backScan", fromBlock < lastProcessedBlock)

	startTime := time.Now()

	// 5. 获取监控地址
	addresses, err := s.GetMonitoredAddresses()
	if err != nil {
		return fmt.Errorf("get monitored addresses: %w", err)
	}

	// 6. 批量获取代币事件日志
	tokenLogsByBlock, err := s.batchGetTokenLogs(ctx, scanFromBlock, toBlock, addresses)
	if err != nil {
		return fmt.Errorf("batch get token logs: %w", err)
	}

	// 7. 按区块处理（使用优化方法）
	for blockNum := scanFromBlock; blockNum <= toBlock; blockNum++ {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			blockStartTime := time.Now()

			// 使用优化的区块处理方法
			if err := s.processBlockOptimized(ctx, blockNum, addresses, tokenLogsByBlock[blockNum]); err != nil {
				s.Logger().Error("process block failed",
					"block", blockNum,
					"error", err)
				s.IncrementErrors("block_process")

				// 检查是否是区块不存在的错误
				if strings.Contains(err.Error(), "not found") || strings.Contains(err.Error(), "does not exist") {
					s.Logger().Warn("block not found, stopping scan to avoid further errors",
						"block", blockNum,
						"lastProcessed", lastProcessedBlock)
					// 区块不存在，停止扫描并返回，避免继续尝试不存在的区块
					return nil
				}

				// 其他错误继续处理下一个区块
				continue
			}

			// 记录处理时间
			s.ObserveBlockProcessingDuration(time.Since(blockStartTime).Seconds())
		}

		// 定期检查待确认存款
		if blockNum%10 == 0 {
			if err := s.checkPendingDeposits(ctx, currentBlock); err != nil {
				s.Logger().Error("check pending deposits failed", "error", err)
			}
		}

		// 只有当处理的是新区块时才更新检查点（避免回扫时回退检查点）
		if blockNum > lastProcessedBlock {
			if err := s.UpdateCheckpoint(blockNum); err != nil {
				return fmt.Errorf("update checkpoint: %w", err)
			}
		}
	}

	duration := time.Since(startTime)
	s.Logger().Info("optimized scan completed",
		"chain", s.GetChainName(),
		"blocks", toBlock-scanFromBlock+1,
		"duration", duration.String())

	// 8. 最后再检查一次待确认存款
	return s.checkPendingDeposits(ctx, currentBlock)
}

// batchGetTokenLogs 批量获取代币事件日志
func (s *ETHScanner) batchGetTokenLogs(ctx context.Context, fromBlock, toBlock uint64, addresses map[string]*interfaces.UserAddress) (map[uint64][]ethTypes.Log, error) {
	logsByBlock := make(map[uint64][]ethTypes.Log)

	// 收集所有需要监控的代币合约地址
	contractAddresses := make(map[common.Address]bool)
	for _, processor := range s.tokenProcessors {
		contractAddresses[processor.contractAddress] = true
	}

	if len(contractAddresses) == 0 {
		s.Logger().Debug("no token contracts to monitor")
		return logsByBlock, nil
	}

	// 转换为切片
	contracts := make([]common.Address, 0, len(contractAddresses))
	for addr := range contractAddresses {
		contracts = append(contracts, addr)
	}

	// 分批获取日志，避免RPC超时
	currentFromBlock := fromBlock
	for currentFromBlock <= toBlock {
		currentToBlock := currentFromBlock + maxBlocksPerFilterLogsRequest - 1
		if currentToBlock > toBlock {
			currentToBlock = toBlock
		}

		// 构造查询
		query := ethereum.FilterQuery{
			FromBlock: big.NewInt(int64(currentFromBlock)),
			ToBlock:   big.NewInt(int64(currentToBlock)),
			Addresses: contracts,
			Topics: [][]common.Hash{
				{s.getTransferEventSigHash()}, // Transfer事件签名
			},
		}

		s.Logger().Debug("filtering token logs",
			"fromBlock", currentFromBlock,
			"toBlock", currentToBlock,
			"contracts", len(contracts))

		// 获取日志
		logs, err := s.client.FilterLogs(ctx, query)
		if err != nil {
			return nil, fmt.Errorf("filter logs (%d-%d): %w", currentFromBlock, currentToBlock, err)
		}

		// 过滤并按区块组织日志
		for _, vLog := range logs {
			if vLog.Removed {
				continue
			}

			// 检查是否是Transfer事件且to地址是监控地址
			if len(vLog.Topics) >= 3 {
				toAddress := common.BytesToAddress(vLog.Topics[2].Bytes()).Hex()
				if _, exists := addresses[strings.ToLower(toAddress)]; exists {
					if logsByBlock[vLog.BlockNumber] == nil {
						logsByBlock[vLog.BlockNumber] = make([]ethTypes.Log, 0)
					}
					logsByBlock[vLog.BlockNumber] = append(logsByBlock[vLog.BlockNumber], vLog)
				}
			}
		}

		s.Logger().Debug("batch logs fetched",
			"fromBlock", currentFromBlock,
			"toBlock", currentToBlock,
			"logs", len(logs))

		currentFromBlock = currentToBlock + 1
	}

	s.Logger().Info("token logs batch completed",
		"fromBlock", fromBlock,
		"toBlock", toBlock,
		"blocksWithLogs", len(logsByBlock))

	return logsByBlock, nil
}

// processBlockOptimized 优化的区块处理方法
func (s *ETHScanner) processBlockOptimized(ctx context.Context, blockNum uint64, addresses map[string]*interfaces.UserAddress, tokenLogs []ethTypes.Log) error {
	// 获取区块数据（只为了处理原生代币交易）
	block, err := s.client.BlockByNumber(ctx, big.NewInt(int64(blockNum)))
	if err != nil {
		return fmt.Errorf("get block %d: %w", blockNum, err)
	}

	s.Logger().Debug("processing block optimized",
		"number", blockNum,
		"hash", block.Hash().Hex(),
		"nativeTxs", len(block.Transactions()),
		"tokenLogs", len(tokenLogs))

	var deposits []*interfaces.Deposit

	// 1. 处理原生代币交易（ETH）
	if s.Config().Native.Enabled {
		for _, tx := range block.Transactions() {
			if tx.To() == nil || tx.Value().Cmp(big.NewInt(0)) == 0 {
				continue
			}

			toAddr := strings.ToLower(tx.To().Hex())
			if userAddr, exists := addresses[toAddr]; exists {
				deposit, err := s.createNativeDeposit(ctx, tx, block, userAddr)
				if err != nil {
					s.Logger().Debug("create native deposit failed", "error", err)
					continue
				}
				if deposit != nil {
					deposits = append(deposits, deposit)
				}
			}
		}
	}

	// 2. 处理代币事件日志
	for _, vLog := range tokenLogs {
		// 找到对应的处理器
		processor, exists := s.tokenProcessors[strings.ToLower(vLog.Address.Hex())]
		if !exists {
			continue
		}

		// 解析Transfer事件
		transferEvent, err := processor.ParseTransferLog(&vLog)
		if err != nil {
			s.Logger().Debug("parse transfer log failed", "error", err)
			continue
		}

		// 检查to地址是否是监控地址
		toAddr := strings.ToLower(transferEvent.To.Hex())
		userAddr, exists := addresses[toAddr]
		if !exists {
			continue
		}

		// 创建代币充值记录
		deposit, err := s.createTokenDeposit(ctx, &vLog, transferEvent, processor, userAddr)
		if err != nil {
			s.Logger().Debug("create token deposit failed", "error", err)
			continue
		}
		if deposit != nil {
			deposits = append(deposits, deposit)
		}
	}

	// 3. 批量创建存款记录
	if len(deposits) > 0 {
		if err := s.DepositManager().BatchCreateDeposits(ctx, deposits); err != nil {
			return fmt.Errorf("batch create deposits: %w", err)
		}
		s.Logger().Info("detected deposits",
			"block", blockNum,
			"count", len(deposits))

		// 更新指标
		for _, d := range deposits {
			s.IncrementDepositsDetected(d.Token)
		}
	}

	s.Logger().Debug("block processed optimized",
		"number", blockNum,
		"deposits", len(deposits),
		"monitoredAddresses", len(addresses))

	s.IncrementBlocksProcessed()
	return nil
}

// getTransferEventSigHash 获取Transfer事件签名哈希
func (s *ETHScanner) getTransferEventSigHash() common.Hash {
	// 从任意一个处理器获取Transfer事件签名
	for _, processor := range s.tokenProcessors {
		return processor.transferEventID
	}

	// 如果没有处理器，手动计算Transfer事件签名
	// Transfer(address,address,uint256)
	return common.HexToHash("0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef")
}

// calculateDynamicBatchSize 计算动态批量大小
func (s *ETHScanner) calculateDynamicBatchSize(totalBlocks uint64) uint64 {
	baseBatchSize := uint64(s.Config().ScanBatchSize)

	// 根据积压程度动态调整批量大小
	switch {
	case totalBlocks > 5000:
		// 严重积压：增大批量处理
		return baseBatchSize * 5
	case totalBlocks > 1000:
		// 中等积压：适度增大批量
		return baseBatchSize * 3
	case totalBlocks > 500:
		// 轻微积压：小幅增大批量
		return baseBatchSize * 2
	case totalBlocks < 10:
		// 接近实时：处理所有剩余区块
		return totalBlocks
	default:
		// 正常情况：使用配置的批量大小
		return baseBatchSize
	}
}

// processBlock 处理单个区块
func (s *ETHScanner) processBlock(ctx context.Context, blockNum uint64) error {
	// 获取区块数据
	block, err := s.client.BlockByNumber(ctx, big.NewInt(int64(blockNum)))
	if err != nil {
		return fmt.Errorf("get block %d: %w", blockNum, err)
	}

	s.Logger().Debug("processing block",
		"number", blockNum,
		"hash", block.Hash().Hex(),
		"txs", len(block.Transactions()))

	// 获取监控地址
	addresses, err := s.GetMonitoredAddresses()
	if err != nil {
		return fmt.Errorf("get monitored addresses: %w", err)
	}

	var deposits []*interfaces.Deposit

	// 处理区块中的交易
	for _, tx := range block.Transactions() {
		// 检查是否是监控的地址
		if tx.To() == nil {
			continue // 合约创建交易，跳过
		}

		toAddr := strings.ToLower(tx.To().Hex())

		// 检查是否是原生代币转账到监控地址
		if userAddr, exists := addresses[toAddr]; exists {
			if tx.Value().Cmp(big.NewInt(0)) > 0 && s.Config().Native.Enabled {
				deposit, err := s.createNativeDeposit(ctx, tx, block, userAddr)
				if err != nil {
					s.Logger().Debug("create native deposit failed", "error", err)
					continue
				}
				if deposit != nil {
					deposits = append(deposits, deposit)
				}
			}
		}

		// 检查是否包含代币转账事件
		receipt, err := s.client.TransactionReceipt(ctx, tx.Hash())
		if err != nil {
			s.Logger().Debug("get receipt failed", "tx", tx.Hash().Hex(), "error", err)
			continue
		}

		// 处理日志事件
		tokenDeposits := s.processTokenTransfers(ctx, receipt, addresses, blockNum)
		deposits = append(deposits, tokenDeposits...)
	}

	// 批量创建存款记录
	if len(deposits) > 0 {
		if err := s.DepositManager().BatchCreateDeposits(ctx, deposits); err != nil {
			return fmt.Errorf("batch create deposits: %w", err)
		}
		s.Logger().Info("detected deposits",
			"block", blockNum,
			"count", len(deposits))

		// 更新指标
		for _, d := range deposits {
			s.IncrementDepositsDetected(d.Token)
		}
	}

	s.IncrementBlocksProcessed()
	return nil
}

// createNativeDeposit 创建原生代币存款记录
func (s *ETHScanner) createNativeDeposit(ctx context.Context, tx *ethTypes.Transaction, block *ethTypes.Block, userAddr *interfaces.UserAddress) (*interfaces.Deposit, error) {
	// 获取发送者地址
	from, err := GetTransactionSender(s.client, tx, block)
	if err != nil {
		return nil, fmt.Errorf("get sender: %w", err)
	}

	amount := decimal.NewFromBigInt(tx.Value(), int32(-s.nativeDecimals))

	// 检查最小金额阈值
	if !s.checkMinimumAmount(s.Config().Native.Symbol, amount) {
		s.Logger().Debug("deposit below minimum threshold, skipping",
			"symbol", s.Config().Native.Symbol,
			"amount", amount,
			"txHash", tx.Hash().Hex())
		return nil, nil // 返回 nil 表示跳过这个存款
	}

	return &interfaces.Deposit{
		TxHash:               tx.Hash().Hex(),
		Chain:                s.GetChainName(),
		Token:                s.Config().Native.Symbol,
		FromAddress:          from.Hex(),
		ToAddress:            tx.To().Hex(),
		Amount:               amount,
		BlockNumber:          block.NumberU64(),
		Status:               interfaces.DepositStatusDetected,
		RequiredConfirms:     s.Config().Native.Confirmations,
		UserID:               userAddr.UserID,
		TokenID:              userAddr.TokenID,
		TokenContractAddress: "", // Empty for native ETH transfers
		CreatedAt:            time.Now().Unix(),
		UpdatedAt:            time.Now().Unix(),
	}, nil
}

// createTokenDeposit 创建代币存款记录
func (s *ETHScanner) createTokenDeposit(ctx context.Context, vLog *ethTypes.Log, transferEvent *TransferEvent, processor *ERC20Processor, userAddr *interfaces.UserAddress) (*interfaces.Deposit, error) {
	// 转换金额到正确的精度
	amount := decimal.NewFromBigInt(transferEvent.Value, int32(-processor.Decimals))

	// 检查最小金额阈值
	if !s.checkMinimumAmount(processor.Symbol, amount) {
		s.Logger().Debug("deposit below minimum threshold, skipping",
			"symbol", processor.Symbol,
			"amount", amount,
			"txHash", vLog.TxHash.Hex())
		return nil, nil // 返回 nil 表示跳过这个存款
	}

	return &interfaces.Deposit{
		TxHash:               vLog.TxHash.Hex(),
		Chain:                s.GetChainName(),
		Token:                processor.Symbol,
		FromAddress:          transferEvent.From.Hex(),
		ToAddress:            transferEvent.To.Hex(),
		Amount:               amount,
		BlockNumber:          vLog.BlockNumber,
		Status:               interfaces.DepositStatusDetected,
		RequiredConfirms:     uint64(s.getTokenConfirmations(processor.Symbol)),
		UserID:               userAddr.UserID,
		TokenID:              userAddr.TokenID,
		TokenContractAddress: vLog.Address.Hex(), // Token contract address from the log
		CreatedAt:            time.Now().Unix(),
		UpdatedAt:            time.Now().Unix(),
	}, nil
}

// getTokenConfirmations 获取代币确认数
func (s *ETHScanner) getTokenConfirmations(symbol string) int {
	for _, token := range s.Config().Tokens {
		if token.Symbol == symbol {
			return int(token.Confirmations)
		}
	}
	// 默认确认数
	return 6
}

// checkMinimumAmount 检查金额是否达到最小阈值
func (s *ETHScanner) checkMinimumAmount(symbol string, amount decimal.Decimal) bool {
	// 获取全局配置中的阈值
	if s.GlobalConfig() == nil || s.GlobalConfig().Tasks.Confirmation.Thresholds == nil {
		return true // 没有配置阈值，认为通过
	}

	thresholdStr, exists := s.GlobalConfig().Tasks.Confirmation.Thresholds[symbol]
	if !exists {
		return true // 没有配置该代币的阈值，认为通过
	}

	threshold, err := decimal.NewFromString(thresholdStr)
	if err != nil {
		s.Logger().Warn("invalid threshold format", "symbol", symbol, "threshold", thresholdStr)
		return true // 阈值格式错误，认为通过
	}

	return amount.GreaterThanOrEqual(threshold)
}

// processTokenTransfers 处理代币转账
func (s *ETHScanner) processTokenTransfers(
	ctx context.Context,
	receipt *ethTypes.Receipt,
	addresses map[string]*interfaces.UserAddress,
	blockNumber uint64,
) []*interfaces.Deposit {
	var deposits []*interfaces.Deposit

	for _, log := range receipt.Logs {
		// 检查是否是我们监控的代币合约
		contractAddr := strings.ToLower(log.Address.Hex())
		processor, exists := s.tokenProcessors[contractAddr]
		if !exists {
			continue
		}

		// 解析Transfer事件
		transfer, err := processor.ParseTransferLog(log)
		if err != nil {
			continue
		}

		// 检查接收地址是否在监控列表中
		toAddr := strings.ToLower(transfer.To.Hex())
		userAddr, exists := addresses[toAddr]
		if !exists {
			continue
		}

		// 创建存款记录（包含阈值检查）
		deposit, err := s.createTokenDeposit(ctx, log, transfer, processor, userAddr)
		if err != nil {
			s.Logger().Error("create token deposit failed", "error", err)
			continue
		}

		// 如果存款被阈值过滤掉，deposit 会是 nil
		if deposit != nil {
			deposits = append(deposits, deposit)
		}
	}

	return deposits
}

// checkPendingDeposits 检查待确认存款
func (s *ETHScanner) checkPendingDeposits(ctx context.Context, currentBlock uint64) error {
	pendingDeposits, err := s.DepositManager().GetPendingDeposits(ctx, s.GetChainName())
	if err != nil {
		return fmt.Errorf("get pending deposits: %w", err)
	}

	// 批量检查确认数
	var toConfirm []*interfaces.Deposit
	for _, deposit := range pendingDeposits {
		confirmations := currentBlock - deposit.BlockNumber + 1
		if confirmations >= deposit.RequiredConfirms {
			deposit.Confirmations = confirmations
			toConfirm = append(toConfirm, deposit)
		}
	}

	// 批量处理已确认的存款
	if len(toConfirm) > 0 {
		if err := s.DepositManager().BatchProcessConfirmedDeposits(ctx, toConfirm); err != nil {
			return fmt.Errorf("batch process confirmed deposits: %w", err)
		}
		s.Logger().Info("confirmed deposits", "count", len(toConfirm))

		// 更新指标
		for _, d := range toConfirm {
			s.IncrementDepositsConfirmed(d.Token)
		}
	}

	// 更新待处理存款数量指标
	s.SetPendingDeposits(float64(len(pendingDeposits) - len(toConfirm)))

	return nil
}

// validateCurrentBlock 验证当前区块是否可用
func (s *ETHScanner) validateCurrentBlock(ctx context.Context, blockNumber uint64) error {
	// 尝试获取区块头来验证区块是否真的存在
	_, err := s.client.HeaderByNumber(ctx, big.NewInt(int64(blockNumber)))
	if err != nil {
		return fmt.Errorf("block %d not available: %w", blockNumber, err)
	}
	return nil
}

// HealthCheck 健康检查
func (s *ETHScanner) HealthCheck(ctx context.Context) error {
	// 调用基础健康检查
	if err := s.BaseScanner.HealthCheck(ctx); err != nil {
		return err
	}

	// 检查ETH客户端连接
	if _, err := s.client.BlockNumber(ctx); err != nil {
		return fmt.Errorf("eth client health check failed: %w", err)
	}

	return nil
}
