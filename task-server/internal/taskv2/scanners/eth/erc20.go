package eth

import (
	"fmt"
	"math/big"
	"strings"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/ethclient"
)

// ERC20 ABI
const erc20ABI = `[
	{
		"anonymous": false,
		"inputs": [
			{
				"indexed": true,
				"internalType": "address",
				"name": "from",
				"type": "address"
			},
			{
				"indexed": true,
				"internalType": "address",
				"name": "to",
				"type": "address"
			},
			{
				"indexed": false,
				"internalType": "uint256",
				"name": "value",
				"type": "uint256"
			}
		],
		"name": "Transfer",
		"type": "event"
	},
	{
		"constant": true,
		"inputs": [],
		"name": "name",
		"outputs": [
			{
				"internalType": "string",
				"name": "",
				"type": "string"
			}
		],
		"payable": false,
		"stateMutability": "view",
		"type": "function"
	},
	{
		"constant": true,
		"inputs": [],
		"name": "symbol",
		"outputs": [
			{
				"internalType": "string",
				"name": "",
				"type": "string"
			}
		],
		"payable": false,
		"stateMutability": "view",
		"type": "function"
	},
	{
		"constant": true,
		"inputs": [],
		"name": "decimals",
		"outputs": [
			{
				"internalType": "uint8",
				"name": "",
				"type": "uint8"
			}
		],
		"payable": false,
		"stateMutability": "view",
		"type": "function"
	},
	{
		"constant": true,
		"inputs": [
			{
				"internalType": "address",
				"name": "account",
				"type": "address"
			}
		],
		"name": "balanceOf",
		"outputs": [
			{
				"internalType": "uint256",
				"name": "",
				"type": "uint256"
			}
		],
		"payable": false,
		"stateMutability": "view",
		"type": "function"
	}
]`

// TransferEvent Transfer事件结构
type TransferEvent struct {
	From  common.Address
	To    common.Address
	Value *big.Int
}

// ERC20Processor ERC20代币处理器
type ERC20Processor struct {
	client          *ethclient.Client
	contractAddress common.Address
	contractABI     abi.ABI
	Symbol          string
	Decimals        int
	transferEventID common.Hash
}

// NewERC20Processor 创建ERC20处理器
func NewERC20Processor(client *ethclient.Client, contractAddress common.Address, symbol string, decimals int) (*ERC20Processor, error) {
	// 解析ABI
	contractABI, err := abi.JSON(strings.NewReader(erc20ABI))
	if err != nil {
		return nil, fmt.Errorf("parse abi: %w", err)
	}

	// 获取Transfer事件的topic
	transferEvent := contractABI.Events["Transfer"]
	if transferEvent.ID == (common.Hash{}) {
		return nil, fmt.Errorf("transfer event not found in ABI")
	}

	processor := &ERC20Processor{
		client:          client,
		contractAddress: contractAddress,
		contractABI:     contractABI,
		Symbol:          symbol,
		Decimals:        decimals,
		transferEventID: transferEvent.ID,
	}

	// 如果没有提供symbol或decimals，尝试从合约读取
	if symbol == "" || decimals == 0 {
		if err := processor.loadTokenInfo(); err != nil {
			// 如果读取失败，使用默认值
			if symbol == "" {
				// processor.Symbol = "UNKNOWN"
				return nil, fmt.Errorf("failed to load token info: %w", err)
			}
			if decimals == 0 {
				// processor.Decimals = 18 // 默认18位小数
				return nil, fmt.Errorf("failed to load token info: %w", err)
			}
		}
	}

	return processor, nil
}

// loadTokenInfo 从合约加载代币信息
func (p *ERC20Processor) loadTokenInfo() error {
	// 这里可以实现调用合约的 symbol() 和 decimals() 方法
	// 为了简化，暂时跳过实现
	return nil
}

// ParseTransferLog 解析Transfer日志
func (p *ERC20Processor) ParseTransferLog(log *types.Log) (*TransferEvent, error) {
	// 检查是否是Transfer事件
	if len(log.Topics) < 3 {
		return nil, fmt.Errorf("invalid transfer log: insufficient topics")
	}

	if log.Topics[0] != p.transferEventID {
		return nil, fmt.Errorf("not a transfer event")
	}

	// 解析from和to地址（在topics中）
	from := common.BytesToAddress(log.Topics[1].Bytes())
	to := common.BytesToAddress(log.Topics[2].Bytes())

	// 解析value（在data中）
	if len(log.Data) < 32 {
		return nil, fmt.Errorf("invalid transfer log: insufficient data")
	}

	value := new(big.Int).SetBytes(log.Data)

	return &TransferEvent{
		From:  from,
		To:    to,
		Value: value,
	}, nil
}

// GetBalance 获取代币余额
func (p *ERC20Processor) GetBalance(address common.Address) (*big.Int, error) {
	// 构造balanceOf调用数据
	data, err := p.contractABI.Pack("balanceOf", address)
	if err != nil {
		return nil, fmt.Errorf("pack balanceOf: %w", err)
	}

	// 调用合约
	msg := ethereum.CallMsg{
		To:   &p.contractAddress,
		Data: data,
	}

	result, err := p.client.CallContract(nil, msg, nil)
	if err != nil {
		return nil, fmt.Errorf("call contract: %w", err)
	}

	// 解析结果
	var balance *big.Int
	err = p.contractABI.UnpackIntoInterface(&balance, "balanceOf", result)
	if err != nil {
		return nil, fmt.Errorf("unpack balance: %w", err)
	}

	return balance, nil
}
