package tron

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"math/big"
	"strings"
	"time"

	"github.com/fbsobreira/gotron-sdk/pkg/proto/api"
	"github.com/fbsobreira/gotron-sdk/pkg/proto/core"
	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
	"google.golang.org/protobuf/proto"

	"task-api/internal/taskv2/interfaces"
	"task-api/internal/taskv2/monitoring"
	"task-api/internal/taskv2/scanners/base"
	"task-api/internal/taskv2/types"
	"task-api/internal/taskv2/utils"
	"task-api/internal/utility/crypto"
)

// TRONScanner TRON区块链扫描器
type TRONScanner struct {
	*base.BaseScanner
	client          *crypto.TRONClient
	nativeDecimals  int
	tokenProcessors map[string]*TRC20Processor // contract address -> processor
	lastRPCCall     time.Time                  // 用于RPC调用频率限制
	rpcCallInterval time.Duration              // RPC调用间隔
}

// NewTRONScanner 创建TRON扫描器
func NewTRONScanner(
	globalConfig *types.Config,
	chainConfig *types.ChainConfig,
	depositManager interfaces.DepositManager,
	storage interfaces.CheckpointStorage,
	dao interfaces.DepositDAO,
	logger *logrus.Logger,
	metrics *monitoring.Metrics,
) (*TRONScanner, error) {
	// 创建TRON客户端
	tronConfig := &crypto.ClientConfig{
		RPCUrl: chainConfig.RpcURL,
		APIKey: chainConfig.ApiKey,
		UseTLS: false, // Use TLS for mainnet endpoints
	}

	client, err := crypto.NewTRONClient(tronConfig)
	if err != nil {
		return nil, fmt.Errorf("create tron client: %w", err)
	}

	scanner := &TRONScanner{
		BaseScanner: base.NewBaseScanner(
			"TRON",
			chainConfig,
			depositManager,
			storage,
			logger,
		),
		client:          client,
		nativeDecimals:  chainConfig.Native.Decimals,
		tokenProcessors: make(map[string]*TRC20Processor),
		rpcCallInterval: 200 * time.Millisecond, // 限制RPC调用频率为每200ms一次
	}

	// 设置全局配置和监控指标
	scanner.SetGlobalConfig(globalConfig)
	if metrics != nil {
		scanner.SetChainMetrics(monitoring.NewChainMetrics(metrics, "TRON"))
	}

	// 初始化代币处理器
	for _, token := range chainConfig.Tokens {
		if !token.Enabled {
			continue
		}

		processor := NewTRC20Processor(
			client,
			token.ContractAddress,
			token.Symbol,
			token.Decimals,
		)

		scanner.tokenProcessors[token.ContractAddress] = processor
	}

	// 设置DAO
	scanner.SetDAO(dao)

	return scanner, nil
}

// Start 启动TRON扫描器
func (s *TRONScanner) Start(ctx context.Context) error {
	// 加载初始区块
	lastBlock, err := s.LoadInitialBlock(ctx, s.GetCurrentBlock)
	if err != nil {
		return fmt.Errorf("load initial block: %w", err)
	}

	// 初始化扫描器
	return s.InitializeScanner(ctx, lastBlock)
}

// GetCurrentBlock 获取当前区块
func (s *TRONScanner) GetCurrentBlock(ctx context.Context) (uint64, error) {
	s.waitForRateLimit()
	return s.client.GetCurrentBlock(ctx)
}

// ScanAndProcess 扫描并处理区块
func (s *TRONScanner) ScanAndProcess(ctx context.Context) error {
	// 每次扫描前都从数据库加载最新地址
	if err := s.RefreshMonitoredAddresses(); err != nil {
		s.Logger().Error("load monitored addresses failed", "error", err)
		// 继续执行扫描，避免地址加载失败影响区块处理
	}

	// 使用重试机制
	maxRetries := 3 // 默认值
	if s.GlobalConfig() != nil {
		maxRetries = s.GlobalConfig().Global.MaxRetries
	}
	return utils.RetryWithBackoff(ctx, func() error {
		return s.scanAndProcessInternal(ctx)
	}, maxRetries)
}

// scanAndProcessInternal 内部扫描处理逻辑
func (s *TRONScanner) scanAndProcessInternal(ctx context.Context) error {
	// 1. 获取扫描范围
	lastProcessedBlock, err := s.GetLastProcessedBlock()
	if err != nil {
		return fmt.Errorf("get last block: %w", err)
	}

	s.Logger().Debug("scanner state check",
		"lastProcessedBlock", lastProcessedBlock)

	currentBlock, err := s.GetCurrentBlock(ctx)
	if err != nil {
		return fmt.Errorf("get current block: %w", err)
	}

	// 检查区块滞后情况
	if currentBlock > lastProcessedBlock {
		lag := currentBlock - lastProcessedBlock
		if lag > 100000 { // 如果滞后超过10万个区块
			s.Logger().Warn("severe block lag detected, using catch-up strategy",
				"lag", lag,
				"currentBlock", currentBlock,
				"lastProcessed", lastProcessedBlock)
			// 使用更激进的批量大小来追赶
		}
	}

	// 更新指标
	s.UpdateMetrics(currentBlock)

	// 2. 计算扫描起始区块
	// 实现回扫逻辑：只有在系统启动时（lastProcessedBlock为初始值）才回扫
	// 正常运行时从下一个区块开始扫描
	fromBlock := lastProcessedBlock

	// 检查是否是系统启动时的初始扫描
	// 如果lastProcessedBlock等于配置的startBlock或者是从当前区块-100计算出来的初始值，
	// 则认为是系统启动，需要实现回扫逻辑
	isInitialScan := s.isInitialScan(lastProcessedBlock, currentBlock)

	if isInitialScan {
		// 系统启动时：实现回扫100个区块的逻辑
		const backScanBlocks = 100
		if lastProcessedBlock >= backScanBlocks {
			fromBlock = lastProcessedBlock - backScanBlocks
		} else {
			fromBlock = 0
		}
		s.Logger().Info("initial scan with back-scanning",
			"lastProcessedBlock", lastProcessedBlock,
			"backScanFromBlock", fromBlock,
			"backScanBlocks", backScanBlocks)
	}

	// 3. 计算动态批量大小
	totalBlocksToScan := currentBlock - fromBlock
	batchSize := s.calculateDynamicBatchSize(totalBlocksToScan)

	// 4. 确定本次扫描的结束区块
	toBlock := currentBlock
	if totalBlocksToScan > batchSize {
		toBlock = fromBlock + batchSize
	}

	scanFromBlock := fromBlock + 1
	if scanFromBlock > toBlock {
		s.Logger().Debug("no new blocks to scan")
		return s.checkPendingDeposits(ctx, currentBlock)
	}

	s.Logger().Debug("scan range calculated",
		"lastProcessed", lastProcessedBlock,
		"fromBlock", scanFromBlock,
		"toBlock", toBlock,
		"currentBlock", currentBlock,
		"batchSize", batchSize)

	// 5. 扫描区块范围
	s.Logger().Info("starting block scan",
		"fromBlock", scanFromBlock,
		"toBlock", toBlock,
		"totalBlocks", toBlock-scanFromBlock+1,
		"batchSize", batchSize)

	for blockNum := scanFromBlock; blockNum <= toBlock; blockNum++ {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			startTime := time.Now()

			// 处理单个区块
			if err := s.processBlock(ctx, blockNum); err != nil {
				s.Logger().Error("process block failed",
					"block", blockNum,
					"error", err)
				s.IncrementErrors("block_process")
				// 记录错误但继续处理下一个区块
				continue
			}

			// 记录处理时间
			s.ObserveBlockProcessingDuration(time.Since(startTime).Seconds())

			// 定期报告进度
			if blockNum%100 == 0 {
				progress := float64(blockNum-scanFromBlock+1) / float64(toBlock-scanFromBlock+1) * 100
				s.Logger().Info("scan progress",
					"block", blockNum,
					"progress", fmt.Sprintf("%.1f%%", progress),
					"remaining", toBlock-blockNum)
			}
		}

		// 定期检查待确认存款
		if blockNum%10 == 0 {
			if err := s.checkPendingDeposits(ctx, currentBlock); err != nil {
				s.Logger().Error("check pending deposits failed", "error", err)
			}
		}

		// 只有当处理的是新区块时才更新检查点（避免回扫时回退检查点）
		if blockNum > lastProcessedBlock {
			if err := s.UpdateCheckpoint(blockNum); err != nil {
				return fmt.Errorf("update checkpoint: %w", err)
			}
		}
	}

	// 6. 最后再检查一次待确认存款
	return s.checkPendingDeposits(ctx, currentBlock)
}

// isInitialScan 判断是否是系统启动时的初始扫描
func (s *TRONScanner) isInitialScan(lastProcessedBlock, currentBlock uint64) bool {
	// 如果配置了startBlock，检查lastProcessedBlock是否等于startBlock
	if s.Config().StartBlock > 0 {
		return lastProcessedBlock == s.Config().StartBlock
	}

	// 如果没有配置startBlock，检查lastProcessedBlock是否是从当前区块-100计算出来的初始值
	// 这种情况下，lastProcessedBlock应该接近currentBlock-100
	const defaultLookback = 100
	expectedInitialBlock := uint64(0)
	if currentBlock > defaultLookback {
		expectedInitialBlock = currentBlock - defaultLookback
	}

	// 允许一定的误差范围（比如50个区块），因为在扫描过程中currentBlock可能会变化
	const tolerance = 50
	return lastProcessedBlock <= expectedInitialBlock+tolerance &&
		lastProcessedBlock >= expectedInitialBlock-tolerance
}

// calculateDynamicBatchSize 计算动态批量大小
func (s *TRONScanner) calculateDynamicBatchSize(totalBlocks uint64) uint64 {
	baseBatchSize := uint64(s.Config().ScanBatchSize)

	// 根据积压程度动态调整批量大小
	switch {
	case totalBlocks > 50000:
		// 极严重积压：大幅增大批量处理，但考虑RPC限制
		return baseBatchSize * 20
	case totalBlocks > 10000:
		// 严重积压：大幅增大批量处理
		return baseBatchSize * 10
	case totalBlocks > 5000:
		// 中等积压：增大批量处理
		return baseBatchSize * 5
	case totalBlocks > 1000:
		// 轻微积压：适度增大批量
		return baseBatchSize * 3
	case totalBlocks > 500:
		// 小幅积压：小幅增大批量
		return baseBatchSize * 2
	case totalBlocks < 10:
		// 接近实时：处理所有剩余区块
		return totalBlocks
	default:
		// 正常情况：使用配置的批量大小
		return baseBatchSize
	}
}

// waitForRateLimit 等待RPC调用频率限制
func (s *TRONScanner) waitForRateLimit() {
	now := time.Now()
	elapsed := now.Sub(s.lastRPCCall)
	if elapsed < s.rpcCallInterval {
		time.Sleep(s.rpcCallInterval - elapsed)
	}
	s.lastRPCCall = time.Now()
}

// checkMinimumAmount 检查金额是否达到最小阈值
func (s *TRONScanner) checkMinimumAmount(symbol string, amount decimal.Decimal) bool {
	// 获取全局配置中的阈值
	if s.GlobalConfig() == nil || s.GlobalConfig().Tasks.Confirmation.Thresholds == nil {
		return true // 没有配置阈值，认为通过
	}

	thresholdStr, exists := s.GlobalConfig().Tasks.Confirmation.Thresholds[symbol]
	if !exists {
		return true // 没有配置该代币的阈值，认为通过
	}

	threshold, err := decimal.NewFromString(thresholdStr)
	if err != nil {
		s.Logger().Warn("invalid threshold format", "symbol", symbol, "threshold", thresholdStr)
		return true // 阈值格式错误，认为通过
	}

	return amount.GreaterThanOrEqual(threshold)
}

// processBlock 处理单个区块
func (s *TRONScanner) processBlock(ctx context.Context, blockNum uint64) error {
	s.Logger().Debug("processing block",
		"number", blockNum)

	// 获取监控地址
	addresses, err := s.GetMonitoredAddresses()
	if err != nil {
		return fmt.Errorf("get monitored addresses: %w", err)
	}

	// 应用RPC调用频率限制
	s.waitForRateLimit()

	// 获取区块数据
	block, err := s.client.GetBlockByNum(ctx, int64(blockNum))
	if err != nil {
		return fmt.Errorf("get block by num: %w", err)
	}

	// BlockExtention 包含两种格式的交易：transactions 和 transactionInfo
	if block == nil || (block.Transactions == nil && block.GetTransactions() == nil) {
		s.Logger().Debug("empty block", "number", blockNum)
		s.IncrementBlocksProcessed()
		return nil
	}

	// 获取交易列表
	var transactions []*api.TransactionExtention
	if block.GetTransactions() != nil {
		transactions = block.GetTransactions()
	}

	s.Logger().Debug("processing block",
		"number", blockNum,
		"txs", len(transactions))

	var deposits []*interfaces.Deposit

	// 处理区块中的交易
	for i, txExt := range transactions {
		// 获取交易数据
		tx := txExt.GetTransaction()
		if tx == nil {
			continue
		}

		// 获取交易 ID
		txID := hex.EncodeToString(txExt.GetTxid())

		// 检查交易是否成功（从 TransactionExtention 的 Result 字段）
		if txExt.GetResult() != nil && txExt.GetResult().GetCode() != 0 {
			// code != 0 表示交易失败
			continue
		}

		// 另外检查交易内部的返回状态
		if tx.GetRet() != nil && len(tx.GetRet()) > 0 {
			if tx.GetRet()[0].GetContractRet() != core.Transaction_Result_SUCCESS {
				continue
			}
		}

		// 处理不同类型的交易
		if tx.GetRawData() != nil && tx.GetRawData().GetContract() != nil {
			for _, contract := range tx.GetRawData().GetContract() {
				switch contract.GetType() {
				case core.Transaction_Contract_TransferContract:
					// TRX转账
					if deposit := s.processTransferContractV2(contract, txID, blockNum, addresses); deposit != nil {
						deposits = append(deposits, deposit)
					}

				case core.Transaction_Contract_TriggerSmartContract:
					// 智能合约调用（可能是TRC20转账）
					if tokenDeposits := s.processTriggerSmartContractV2(contract, txID, blockNum, addresses); len(tokenDeposits) > 0 {
						deposits = append(deposits, tokenDeposits...)
					}
				}
			}
		}

		_ = i // 避免未使用变量警告
	}

	// 批量创建存款记录
	if len(deposits) > 0 {
		if err := s.DepositManager().BatchCreateDeposits(ctx, deposits); err != nil {
			return fmt.Errorf("batch create deposits: %w", err)
		}
		s.Logger().Info("detected deposits",
			"block", blockNum,
			"count", len(deposits))

		// 更新指标
		for _, d := range deposits {
			s.IncrementDepositsDetected(d.Token)
		}
	}

	s.IncrementBlocksProcessed()
	return nil
}

// processTransferContractV2 处理TRX转账（使用protobuf格式）
func (s *TRONScanner) processTransferContractV2(
	contract *core.Transaction_Contract,
	txID string,
	blockNumber uint64,
	addresses map[string]*interfaces.UserAddress,
) *interfaces.Deposit {
	// 解析合约参数
	transferContract := &core.TransferContract{}
	if err := proto.Unmarshal(contract.GetParameter().GetValue(), transferContract); err != nil {
		s.Logger().Warn("unmarshal transfer contract failed", "error", err)
		return nil
	}

	// 获取转账信息
	ownerAddress := hex.EncodeToString(transferContract.GetOwnerAddress())
	toAddress := hex.EncodeToString(transferContract.GetToAddress())
	amount := transferContract.GetAmount()

	// 转换地址格式（从hex到base58）
	toAddrBase58 := s.hexToBase58Address(toAddress)
	fromAddrBase58 := s.hexToBase58Address(ownerAddress)

	// 检查是否是监控地址
	userAddr, exists := addresses[toAddrBase58]
	if !exists {
		return nil
	}

	// 检查是否启用了原生代币
	if !s.Config().Native.Enabled {
		return nil
	}

	// 计算实际金额（转换为代币单位）
	actualAmount := decimal.NewFromInt(amount).Div(decimal.New(1, int32(s.nativeDecimals)))

	// 检查最小金额阈值
	if !s.checkMinimumAmount(s.Config().Native.Symbol, actualAmount) {
		s.Logger().Debug("deposit below minimum threshold, skipping",
			"symbol", s.Config().Native.Symbol,
			"amount", actualAmount,
			"txHash", txID)
		return nil
	}

	// 创建存款记录
	return &interfaces.Deposit{
		TxHash:               txID,
		Chain:                s.GetChainName(),
		Token:                s.Config().Native.Symbol,
		FromAddress:          fromAddrBase58,
		ToAddress:            toAddrBase58,
		Amount:               actualAmount,
		BlockNumber:          blockNumber,
		Status:               interfaces.DepositStatusDetected,
		RequiredConfirms:     s.Config().Native.Confirmations,
		UserID:               userAddr.UserID,
		TokenID:              userAddr.TokenID,
		TokenContractAddress: "", // 原生代币没有合约地址
		CreatedAt:            time.Now().Unix(),
		UpdatedAt:            time.Now().Unix(),
	}
}

// processTriggerSmartContractV2 处理智能合约调用（使用protobuf格式）
func (s *TRONScanner) processTriggerSmartContractV2(
	contract *core.Transaction_Contract,
	txID string,
	blockNumber uint64,
	addresses map[string]*interfaces.UserAddress,
) []*interfaces.Deposit {
	var deposits []*interfaces.Deposit

	// 解析合约参数
	triggerContract := &core.TriggerSmartContract{}
	if err := proto.Unmarshal(contract.GetParameter().GetValue(), triggerContract); err != nil {
		s.Logger().Warn("unmarshal trigger contract failed", "error", err)
		return deposits
	}

	// 获取合约地址
	contractAddress := hex.EncodeToString(triggerContract.GetContractAddress())
	contractAddrBase58 := s.hexToBase58Address(contractAddress)

	// 检查是否是我们监控的代币合约
	processor, exists := s.tokenProcessors[contractAddrBase58]
	if !exists {
		return deposits
	}

	// 获取调用数据
	data := hex.EncodeToString(triggerContract.GetData())
	if data == "" {
		return deposits
	}

	// 解析TRC20转账
	transfer, err := processor.ParseTransferData(data)
	if err != nil {
		return deposits
	}

	// 检查接收地址是否在监控列表中
	toAddrBase58 := hexToBase58Address(transfer.To)
	userAddr, exists := addresses[toAddrBase58]
	if !exists {
		return deposits
	}

	// 获取发送者地址
	ownerAddress := hex.EncodeToString(triggerContract.GetOwnerAddress())
	fromAddrBase58 := s.hexToBase58Address(ownerAddress)

	// 计算实际金额（转换为代币单位）
	actualAmount := decimal.NewFromBigInt(transfer.Value, int32(-processor.Decimals))

	// 检查最小金额阈值
	if !s.checkMinimumAmount(processor.Symbol, actualAmount) {
		s.Logger().Debug("deposit below minimum threshold, skipping",
			"symbol", processor.Symbol,
			"amount", actualAmount,
			"txHash", txID)
		return deposits
	}

	// 创建存款记录
	deposit := &interfaces.Deposit{
		TxHash:               txID,
		Chain:                s.GetChainName(),
		Token:                processor.Symbol,
		FromAddress:          fromAddrBase58,
		ToAddress:            toAddrBase58,
		Amount:               actualAmount,
		BlockNumber:          blockNumber,
		Status:               interfaces.DepositStatusDetected,
		RequiredConfirms:     s.getTokenConfirmations(processor.Symbol),
		UserID:               userAddr.UserID,
		TokenID:              userAddr.TokenID,
		TokenContractAddress: contractAddrBase58, // 添加合约地址
		CreatedAt:            time.Now().Unix(),
		UpdatedAt:            time.Now().Unix(),
	}

	deposits = append(deposits, deposit)
	return deposits
}

/* Commented out until client supports block fetching
func (s *TRONScanner) processBlockOriginal(ctx context.Context, blockNum uint64) error {
	s.Logger().Debug("processing block",
		"number", blockNum,
		"txs", len(block.Transactions))

	// 获取监控地址
	addresses, err := s.GetMonitoredAddresses()
	if err != nil {
		return fmt.Errorf("get monitored addresses: %w", err)
	}

	var deposits []*interfaces.Deposit

	// 处理区块中的交易
	for _, tx := range block.Transactions {
		if tx.Ret == nil || len(tx.Ret) == 0 {
			continue
		}

		// 检查交易是否成功
		if tx.Ret[0].ContractRet != "SUCCESS" {
			continue
		}

		// 处理不同类型的交易
		if tx.RawData != nil && len(tx.RawData.Contract) > 0 {
			for _, contract := range tx.RawData.Contract {
				switch contract.Type {
				case "TransferContract":
					// TRX转账
					if deposit := s.processTransferContract(contract, tx.TxID, blockNum, addresses); deposit != nil {
						deposits = append(deposits, deposit)
					}

				case "TriggerSmartContract":
					// 智能合约调用（可能是TRC20转账）
					if tokenDeposits := s.processTriggerSmartContract(contract, tx.TxID, blockNum, addresses); len(tokenDeposits) > 0 {
						deposits = append(deposits, tokenDeposits...)
					}
				}
			}
		}
	}

	// 批量创建存款记录
	if len(deposits) > 0 {
		if err := s.DepositManager().BatchCreateDeposits(ctx, deposits); err != nil {
			return fmt.Errorf("batch create deposits: %w", err)
		}
		s.Logger().Info("detected deposits",
			"block", blockNum,
			"count", len(deposits))

		// 更新指标
		for _, d := range deposits {
			s.IncrementDepositsDetected(d.Token)
		}
	}

	s.IncrementBlocksProcessed()
	return nil
}
*/

// processTransferContract 处理TRX转账
func (s *TRONScanner) processTransferContract(
	contract interface{},
	txID string,
	blockNumber uint64,
	addresses map[string]*interfaces.UserAddress,
) *interfaces.Deposit {
	// 解析合约参数
	params, ok := contract.(map[string]interface{})["parameter"].(map[string]interface{})
	if !ok {
		return nil
	}

	value, ok := params["value"].(map[string]interface{})
	if !ok {
		return nil
	}

	// 获取转账信息
	ownerAddress, _ := value["owner_address"].(string)
	toAddress, _ := value["to_address"].(string)
	amount, _ := value["amount"].(float64)

	// 转换地址格式（从hex到base58）
	toAddrBase58 := s.hexToBase58Address(toAddress)
	fromAddrBase58 := s.hexToBase58Address(ownerAddress)

	// 检查是否是监控地址
	userAddr, exists := addresses[toAddrBase58]
	if !exists {
		return nil
	}

	// 检查是否启用了原生代币
	if !s.Config().Native.Enabled {
		return nil
	}

	// 创建存款记录
	return &interfaces.Deposit{
		TxHash:           txID,
		Chain:            s.GetChainName(),
		Token:            s.Config().Native.Symbol,
		FromAddress:      fromAddrBase58,
		ToAddress:        toAddrBase58,
		Amount:           decimal.NewFromFloat(amount).Div(decimal.New(1, int32(s.nativeDecimals))),
		BlockNumber:      blockNumber,
		Status:           interfaces.DepositStatusDetected,
		RequiredConfirms: s.Config().Native.Confirmations,
		UserID:           userAddr.UserID,
		TokenID:          userAddr.TokenID,
		CreatedAt:        time.Now().Unix(),
		UpdatedAt:        time.Now().Unix(),
	}
}

// processTriggerSmartContract 处理智能合约调用
func (s *TRONScanner) processTriggerSmartContract(
	contract interface{},
	txID string,
	blockNumber uint64,
	addresses map[string]*interfaces.UserAddress,
) []*interfaces.Deposit {
	var deposits []*interfaces.Deposit

	// 解析合约参数
	params, ok := contract.(map[string]interface{})["parameter"].(map[string]interface{})
	if !ok {
		return deposits
	}

	value, ok := params["value"].(map[string]interface{})
	if !ok {
		return deposits
	}

	// 获取合约地址
	contractAddress, _ := value["contract_address"].(string)
	contractAddrBase58 := s.hexToBase58Address(contractAddress)

	// 检查是否是我们监控的代币合约
	processor, exists := s.tokenProcessors[contractAddrBase58]
	if !exists {
		return deposits
	}

	// 获取调用数据
	data, _ := value["data"].(string)
	if data == "" {
		return deposits
	}

	// 解析TRC20转账
	transfer, err := processor.ParseTransferData(data)
	if err != nil {
		return deposits
	}

	// 检查接收地址是否在监控列表中
	toAddrBase58 := hexToBase58Address(transfer.To)
	userAddr, exists := addresses[toAddrBase58]
	if !exists {
		return deposits
	}

	// 获取发送者地址
	ownerAddress, _ := value["owner_address"].(string)
	fromAddrBase58 := s.hexToBase58Address(ownerAddress)

	// 创建存款记录
	deposit := &interfaces.Deposit{
		TxHash:               txID,
		Chain:                s.GetChainName(),
		Token:                processor.Symbol,
		FromAddress:          fromAddrBase58,
		ToAddress:            toAddrBase58,
		Amount:               decimal.NewFromBigInt(transfer.Value, int32(-processor.Decimals)),
		BlockNumber:          blockNumber,
		Status:               interfaces.DepositStatusDetected,
		RequiredConfirms:     s.getTokenConfirmations(processor.Symbol),
		UserID:               userAddr.UserID,
		TokenID:              userAddr.TokenID,
		TokenContractAddress: contractAddrBase58, // 添加合约地址
		CreatedAt:            time.Now().Unix(),
		UpdatedAt:            time.Now().Unix(),
	}

	deposits = append(deposits, deposit)
	return deposits
}

// checkPendingDeposits 检查待确认存款
func (s *TRONScanner) checkPendingDeposits(ctx context.Context, currentBlock uint64) error {
	pendingDeposits, err := s.DepositManager().GetPendingDeposits(ctx, s.GetChainName())
	if err != nil {
		return fmt.Errorf("get pending deposits: %w", err)
	}

	// 批量检查确认数
	var toConfirm []*interfaces.Deposit
	for _, deposit := range pendingDeposits {
		confirmations := currentBlock - deposit.BlockNumber + 1
		if confirmations >= deposit.RequiredConfirms {
			deposit.Confirmations = confirmations
			toConfirm = append(toConfirm, deposit)
		}
	}

	// 批量处理已确认的存款
	if len(toConfirm) > 0 {
		if err := s.DepositManager().BatchProcessConfirmedDeposits(ctx, toConfirm); err != nil {
			return fmt.Errorf("batch process confirmed deposits: %w", err)
		}
		s.Logger().Info("confirmed deposits", "count", len(toConfirm))

		// 更新指标
		for _, d := range toConfirm {
			s.IncrementDepositsConfirmed(d.Token)
		}
	}

	// 更新待处理存款数量指标
	s.SetPendingDeposits(float64(len(pendingDeposits) - len(toConfirm)))

	return nil
}

// hexToBase58Address 将hex地址转换为base58地址
func (s *TRONScanner) hexToBase58Address(hexAddr string) string {
	if hexAddr == "" {
		return ""
	}

	// 移除0x前缀
	hexAddr = strings.TrimPrefix(hexAddr, "0x")

	// 解码hex
	bytes, err := hex.DecodeString(hexAddr)
	if err != nil {
		s.Logger().Warn("decode hex address failed", "hex", hexAddr, "error", err)
		return hexAddr
	}

	// 转换为base58
	return bytesToBase58Address(bytes)
}

// getTokenConfirmations 获取代币确认数
func (s *TRONScanner) getTokenConfirmations(symbol string) uint64 {
	for _, token := range s.Config().Tokens {
		if token.Symbol == symbol {
			return token.Confirmations
		}
	}
	return s.Config().Native.Confirmations // 默认使用原生代币的确认数
}

// HealthCheck 健康检查
func (s *TRONScanner) HealthCheck(ctx context.Context) error {
	// 调用基础健康检查
	if err := s.BaseScanner.HealthCheck(ctx); err != nil {
		return err
	}

	// TODO: Implement proper health check when client supports it
	// For now, assume healthy
	_ = s.client

	return nil
}

// hexToBase58Address 将十六进制地址转换为Base58格式
func hexToBase58Address(hexAddr string) string {
	// 移除0x前缀
	hexAddr = strings.TrimPrefix(hexAddr, "0x")

	// 解码十六进制
	bytes, err := hex.DecodeString(hexAddr)
	if err != nil {
		return hexAddr
	}

	return bytesToBase58Address(bytes)
}

// bytesToBase58Address 将字节地址转换为Base58格式
func bytesToBase58Address(addressBytes []byte) string {
	// TRON地址前缀为0x41
	if len(addressBytes) == 20 {
		addressBytes = append([]byte{0x41}, addressBytes...)
	}

	// 计算校验和
	h := sha256.Sum256(addressBytes)
	h2 := sha256.Sum256(h[:])
	checksum := h2[:4]

	// 合并地址和校验和
	fullAddressBytes := append(addressBytes, checksum...)

	// Base58编码
	return base58Encode(fullAddressBytes)
}

// base58Encode 简单的Base58编码实现
func base58Encode(input []byte) string {
	alphabet := "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz"

	// 计算前导零的数量
	zeroCount := 0
	for _, b := range input {
		if b == 0 {
			zeroCount++
		} else {
			break
		}
	}

	// 转换为大整数
	bigInt := new(big.Int).SetBytes(input)
	base := big.NewInt(58)
	zero := big.NewInt(0)
	mod := &big.Int{}

	var result []byte
	for bigInt.Cmp(zero) > 0 {
		bigInt.DivMod(bigInt, base, mod)
		result = append([]byte{alphabet[mod.Int64()]}, result...)
	}

	// 添加前导1
	for i := 0; i < zeroCount; i++ {
		result = append([]byte{'1'}, result...)
	}

	return string(result)
}
