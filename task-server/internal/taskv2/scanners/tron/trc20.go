package tron

import (
	"fmt"
	"math/big"
	"strings"

	"task-api/internal/utility/crypto"
)

// TRC20转账方法ID (transfer(address,uint256))
const transferMethodID = "a9059cbb"

// TransferData TRC20转账数据
type TransferData struct {
	To    string
	Value *big.Int
}

// TRC20Processor TRC20代币处理器
type TRC20Processor struct {
	client          *crypto.TRONClient
	contractAddress string
	Symbol          string
	Decimals        int
}

// NewTRC20Processor 创建TRC20处理器
func NewTRC20Processor(client *crypto.TRONClient, contractAddress string, symbol string, decimals int) *TRC20Processor {
	return &TRC20Processor{
		client:          client,
		contractAddress: contractAddress,
		Symbol:          symbol,
		Decimals:        decimals,
	}
}

// ParseTransferData 解析transfer调用数据
func (p *TRC20Processor) ParseTransferData(data string) (*TransferData, error) {
	// 移除0x前缀
	data = strings.TrimPrefix(data, "0x")
	
	// 检查数据长度 (4字节方法ID + 32字节地址 + 32字节金额 = 68字节 = 136个字符)
	if len(data) < 136 {
		return nil, fmt.Errorf("invalid transfer data length: %d", len(data))
	}
	
	// 检查方法ID
	methodID := data[:8]
	if methodID != transferMethodID {
		return nil, fmt.Errorf("not a transfer method: %s", methodID)
	}
	
	// 解析接收地址 (跳过前12个0，取20字节地址)
	toAddressHex := data[8+24:8+64] // 32字节中的后20字节
	
	// 解析金额
	amountHex := data[8+64:8+128]
	amount := new(big.Int)
	amount.SetString(amountHex, 16)
	
	return &TransferData{
		To:    toAddressHex,
		Value: amount,
	}, nil
}

// GetBalance 获取代币余额
func (p *TRC20Processor) GetBalance(address string) (*big.Int, error) {
	// 构造调用参数
	parameter := p.encodeBalanceOfCall(address)
	
	// TODO: Implement proper contract call when client supports it
	// For now, return zero balance
	_ = parameter
	return big.NewInt(0), nil
}

// encodeBalanceOfCall 编码balanceOf调用
func (p *TRC20Processor) encodeBalanceOfCall(address string) string {
	// balanceOf(address) 方法ID
	methodID := "70a08231"
	
	// 转换地址 - 简单实现，假设输入已经是hex格式
	addressHex := strings.TrimPrefix(address, "0x")
	
	// 补齐到32字节
	addressParam := strings.Repeat("0", 24) + addressHex
	
	return methodID + addressParam
}

// DecodeTransferLog 解码Transfer事件日志
func (p *TRC20Processor) DecodeTransferLog(topics []string, data string) (*TransferData, error) {
	// Transfer事件签名: Transfer(address,address,uint256)
	// topics[0]: 事件签名哈希
	// topics[1]: from地址
	// topics[2]: to地址
	// data: amount
	
	if len(topics) < 3 {
		return nil, fmt.Errorf("invalid transfer log topics")
	}
	
	// 解析to地址 (topics[2]的后20字节)
	toAddressHex := topics[2][len(topics[2])-40:]
	
	// 解析金额
	amount := new(big.Int)
	amount.SetString(strings.TrimPrefix(data, "0x"), 16)
	
	return &TransferData{
		To:    toAddressHex,
		Value: amount,
	}, nil
}