package scanners

import (
	"fmt"

	"github.com/sirupsen/logrus"
	
	"task-api/internal/taskv2/interfaces"
	"task-api/internal/taskv2/monitoring"
	"task-api/internal/taskv2/scanners/eth"
	"task-api/internal/taskv2/scanners/tron"
	"task-api/internal/taskv2/types"
)

// ScannerFactory 扫描器工厂
type ScannerFactory struct {
	globalConfig   *types.Config
	depositManager interfaces.DepositManager
	storage        interfaces.CheckpointStorage
	dao            interfaces.DepositDAO
	logger         *logrus.Logger
	metrics        *monitoring.Metrics
}

// NewScannerFactory 创建扫描器工厂
func NewScannerFactory(
	globalConfig *types.Config,
	depositManager interfaces.DepositManager,
	storage interfaces.CheckpointStorage,
	dao interfaces.DepositDAO,
	logger *logrus.Logger,
	metrics *monitoring.Metrics,
) *ScannerFactory {
	return &ScannerFactory{
		globalConfig:   globalConfig,
		depositManager: depositManager,
		storage:        storage,
		dao:            dao,
		logger:         logger,
		metrics:        metrics,
	}
}

// CreateScanner 创建扫描器
func (f *ScannerFactory) CreateScanner(chainName string) (interfaces.ChainScanner, error) {
	chainConfig, exists := f.globalConfig.Chains[chainName]
	if !exists {
		return nil, fmt.Errorf("chain config not found: %s", chainName)
	}
	
	if !chainConfig.Enabled {
		return nil, fmt.Errorf("chain not enabled: %s", chainName)
	}
	
	switch chainName {
	case "ETH":
		return eth.NewETHScanner(
			f.globalConfig,
			&chainConfig,
			f.depositManager,
			f.storage,
			f.dao,
			f.logger,
			f.metrics,
		)
		
	case "TRON":
		return tron.NewTRONScanner(
			f.globalConfig,
			&chainConfig,
			f.depositManager,
			f.storage,
			f.dao,
			f.logger,
			f.metrics,
		)
		
	default:
		return nil, fmt.Errorf("unsupported chain: %s", chainName)
	}
}

// CreateAllScanners 创建所有启用的扫描器
func (f *ScannerFactory) CreateAllScanners() (map[string]interfaces.ChainScanner, error) {
	scanners := make(map[string]interfaces.ChainScanner)
	
	for chainName, chainConfig := range f.globalConfig.Chains {
		if !chainConfig.Enabled {
			f.logger.Info("chain disabled, skipping", "chain", chainName)
			continue
		}
		
		scanner, err := f.CreateScanner(chainName)
		if err != nil {
			f.logger.Error("failed to create scanner", 
				"chain", chainName, 
				"error", err)
			continue
		}
		
		scanners[chainName] = scanner
		f.logger.Info("scanner created", "chain", chainName)
	}
	
	if len(scanners) == 0 {
		return nil, fmt.Errorf("no scanners created")
	}
	
	return scanners, nil
}