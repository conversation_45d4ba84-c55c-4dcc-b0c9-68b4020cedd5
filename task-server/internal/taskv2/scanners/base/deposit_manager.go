package base

import (
	"context"
	"fmt"
	"strings"

	"task-api/internal/taskv2/interfaces"

	"github.com/sirupsen/logrus"
)

// DepositManager 存款管理器实现
type DepositManager struct {
	dao    interfaces.DepositDAO
	logger *logrus.Logger
}

// NewDepositManager 创建存款管理器
func NewDepositManager(dao interfaces.DepositDAO, logger *logrus.Logger) *DepositManager {
	return &DepositManager{
		dao:    dao,
		logger: logger,
	}
}

// CreateDeposit 创建单个存款记录
func (dm *DepositManager) CreateDeposit(ctx context.Context, deposit *interfaces.Deposit) error {
	// 检查是否已存在
	exists, err := dm.IsDepositExists(ctx, deposit.TxHash)
	if err != nil {
		return fmt.Errorf("check deposit exists: %w", err)
	}

	if exists {
		dm.logger.Debug("deposit already exists", "txHash", deposit.TxHash)
		return nil
	}

	// 创建存款记录
	return dm.dao.CreateDeposit(deposit)
}

// BatchCreateDeposits 批量创建存款记录，自动处理重复
func (dm *DepositManager) BatchCreateDeposits(ctx context.Context, deposits []*interfaces.Deposit) error {
	if len(deposits) == 0 {
		return nil
	}

	// 1. 批量检查交易是否已存在
	txHashes := make([]string, 0, len(deposits))
	for _, d := range deposits {
		txHashes = append(txHashes, d.TxHash)
	}

	existingTxs, err := dm.dao.GetDepositsByTxHashes(txHashes)
	if err != nil {
		return fmt.Errorf("check existing deposits: %w", err)
	}

	// 2. 构建已存在交易的map
	existingMap := make(map[string]bool)
	for _, tx := range existingTxs {
		existingMap[tx.TxHash] = true
	}

	// 3. 过滤出新的存款记录
	var newDeposits []*interfaces.Deposit
	var duplicates []string

	for _, deposit := range deposits {
		if existingMap[deposit.TxHash] {
			duplicates = append(duplicates, deposit.TxHash)
			continue
		}
		newDeposits = append(newDeposits, deposit)
	}

	// 4. 记录重复的交易
	if len(duplicates) > 0 {
		dm.logger.WithFields(logrus.Fields{
			"count":    len(duplicates),
			"txHashes": duplicates,
		}).Warn("duplicate deposits detected")
	}

	// 5. 批量插入新记录
	if len(newDeposits) > 0 {
		if err := dm.dao.BatchInsertDeposits(newDeposits); err != nil {
			// 处理并发插入导致的重复
			if strings.Contains(err.Error(), "duplicate key") {
				dm.logger.Warn("concurrent deposit insert detected", "error", err)
				// 逐个尝试插入
				for _, deposit := range newDeposits {
					if err := dm.createSingleDeposit(ctx, deposit); err != nil {
						dm.logger.Error("create single deposit failed",
							"txHash", deposit.TxHash,
							"error", err)
					}
				}
				return nil
			}
			return fmt.Errorf("batch insert deposits: %w", err)
		}

		dm.logger.Info("new deposits created", "count", len(newDeposits))
	}

	return nil
}

// UpdateDepositStatus 更新存款状态
func (dm *DepositManager) UpdateDepositStatus(ctx context.Context, txHash string, status interfaces.DepositStatus) error {
	return dm.dao.UpdateDepositStatus(txHash, status)
}

// GetPendingDeposits 获取待确认的存款
func (dm *DepositManager) GetPendingDeposits(ctx context.Context, chain string) ([]*interfaces.Deposit, error) {
	deposits, err := dm.dao.GetPendingDeposits(chain)
	if err != nil {
		return nil, err
	}

	// 返回结果
	return deposits, nil
}

// CheckConfirmations 检查确认数
func (dm *DepositManager) CheckConfirmations(ctx context.Context, deposit *interfaces.Deposit, currentBlock uint64) (uint64, error) {
	if currentBlock < deposit.BlockNumber {
		return 0, nil
	}
	return currentBlock - deposit.BlockNumber + 1, nil
}

// ProcessConfirmedDeposit 处理已确认的存款
func (dm *DepositManager) ProcessConfirmedDeposit(ctx context.Context, deposit *interfaces.Deposit) error {
	// 更新状态为已确认
	if err := dm.UpdateDepositStatus(ctx, deposit.TxHash, interfaces.DepositStatusConfirmed); err != nil {
		return fmt.Errorf("update deposit status: %w", err)
	}

	// TODO: 调用钱包服务进行入账
	// 这里应该调用现有的钱包服务接口

	// 更新状态为已处理
	if err := dm.UpdateDepositStatus(ctx, deposit.TxHash, interfaces.DepositStatusProcessed); err != nil {
		return fmt.Errorf("update deposit status to processed: %w", err)
	}

	dm.logger.Info("deposit processed", "txHash", deposit.TxHash, "amount", deposit.Amount)
	return nil
}

// BatchProcessConfirmedDeposits 批量处理已确认的存款
func (dm *DepositManager) BatchProcessConfirmedDeposits(ctx context.Context, deposits []*interfaces.Deposit) error {
	for _, deposit := range deposits {
		if err := dm.ProcessConfirmedDeposit(ctx, deposit); err != nil {
			dm.logger.Error("process confirmed deposit failed",
				"txHash", deposit.TxHash,
				"error", err)
			// 继续处理其他存款
		}
	}
	return nil
}

// IsDepositExists 检查存款是否已存在
func (dm *DepositManager) IsDepositExists(ctx context.Context, txHash string) (bool, error) {
	deposits, err := dm.dao.GetDepositsByTxHashes([]string{txHash})
	if err != nil {
		return false, err
	}
	return len(deposits) > 0, nil
}

// 单个存款创建（处理并发）
func (dm *DepositManager) createSingleDeposit(ctx context.Context, deposit *interfaces.Deposit) error {
	exists, err := dm.IsDepositExists(ctx, deposit.TxHash)
	if err != nil {
		return fmt.Errorf("check deposit exists: %w", err)
	}

	if exists {
		dm.logger.Debug("deposit already exists", "txHash", deposit.TxHash)
		return nil
	}

	return dm.dao.CreateDeposit(deposit)
}

