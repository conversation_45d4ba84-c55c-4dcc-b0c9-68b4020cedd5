package base

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"task-api/internal/taskv2/interfaces"
	"task-api/internal/taskv2/monitoring"
	"task-api/internal/taskv2/types"

	"github.com/sirupsen/logrus"
)

// BaseScanner 基础扫描器实现
type BaseScanner struct {
	chainName          string
	config             *types.ChainConfig
	globalConfig       *types.Config
	depositManager     interfaces.DepositManager
	storage            interfaces.CheckpointStorage
	dao                interfaces.DepositDAO
	logger             *logrus.Entry
	metrics            interfaces.ScannerMetrics
	chainMetrics       *monitoring.ChainMetrics
	monitoredAddresses map[string]*interfaces.UserAddress // 移除缓存，改为直接存储
	lastCheckpoint     uint64
	mu                 sync.RWMutex
	ctx                context.Context
	cancel             context.CancelFunc
}

// NewBaseScanner 创建基础扫描器
func NewBaseScanner(
	chainName string,
	config *types.ChainConfig,
	depositManager interfaces.DepositManager,
	storage interfaces.CheckpointStorage,
	logger *logrus.Logger,
) *BaseScanner {
	return &BaseScanner{
		chainName:          chainName,
		config:             config,
		depositManager:     depositManager,
		storage:            storage,
		logger:             logger.WithField("chain", chainName),
		monitoredAddresses: make(map[string]*interfaces.UserAddress),
		metrics: interfaces.ScannerMetrics{
			ChainName: chainName,
		},
	}
}

// SetGlobalConfig 设置全局配置
func (s *BaseScanner) SetGlobalConfig(config *types.Config) {
	s.globalConfig = config
}

// SetChainMetrics 设置链监控指标
func (s *BaseScanner) SetChainMetrics(metrics *monitoring.ChainMetrics) {
	s.chainMetrics = metrics
}

// GetChainName 获取链名称
func (s *BaseScanner) GetChainName() string {
	return s.chainName
}

// GetSupportedTokens 获取支持的代币列表
func (s *BaseScanner) GetSupportedTokens() []string {
	tokens := []string{}
	if s.config.Native.Enabled {
		tokens = append(tokens, s.config.Native.Symbol)
	}
	for _, token := range s.config.Tokens {
		if token.Enabled {
			tokens = append(tokens, token.Symbol)
		}
	}
	return tokens
}

// LoadInitialBlock 加载初始区块（供子类调用）
func (s *BaseScanner) LoadInitialBlock(ctx context.Context, getCurrentBlock func(context.Context) (uint64, error)) (uint64, error) {
	// 首先尝试从存储中获取上次处理的区块
	if checkpoint, err := s.getStoredCheckpoint(); err == nil && checkpoint > 0 {
		s.logger.Info("loaded checkpoint from storage", "block", checkpoint)
		return checkpoint, nil
	}

	// 如果配置中指定了起始区块，使用配置的值
	if s.config.StartBlock > 0 {
		s.logger.Info("using configured start block", "block", s.config.StartBlock)
		return s.config.StartBlock, nil
	}

	// 否则获取当前最新区块并减去100
	currentBlock, err := getCurrentBlock(ctx)
	if err != nil {
		return 0, fmt.Errorf("get current block: %w", err)
	}

	// 确保不会产生负数
	var startBlock uint64
	if currentBlock > types.DefaultBlockLookback {
		startBlock = currentBlock - types.DefaultBlockLookback
	} else {
		startBlock = 0
	}

	s.logger.Info("calculated start block from current block",
		"currentBlock", currentBlock,
		"startBlock", startBlock)

	return startBlock, nil
}

// InitializeScanner 初始化扫描器状态（供子类调用）
func (s *BaseScanner) InitializeScanner(ctx context.Context, lastBlock uint64) error {
	s.ctx, s.cancel = context.WithCancel(ctx)
	s.lastCheckpoint = lastBlock

	// 启动时加载所有监控地址到内存
	if err := s.loadMonitoredAddresses(); err != nil {
		return fmt.Errorf("load monitored addresses: %w", err)
	}

	s.logger.Info("scanner initialized",
		"lastBlock", lastBlock,
		"monitoredAddresses", len(s.monitoredAddresses))
	return nil
}

// getStoredCheckpoint 从存储中获取检查点
func (s *BaseScanner) getStoredCheckpoint() (uint64, error) {
	if s.storage == nil {
		return 0, fmt.Errorf("storage not configured")
	}

	checkpoint, err := s.storage.GetCheckpoint(s.ctx, s.chainName)
	if err != nil {
		return 0, err
	}

	return checkpoint, nil
}

// Stop 停止扫描器
func (s *BaseScanner) Stop(ctx context.Context) error {
	s.logger.Info("stopping scanner")

	if s.cancel != nil {
		s.cancel()
	}

	// 保存检查点
	if err := s.saveCheckpoint(); err != nil {
		return fmt.Errorf("save checkpoint: %w", err)
	}

	s.logger.Info("scanner stopped")
	return nil
}

// GetLastProcessedBlock 获取最后处理的区块
func (s *BaseScanner) GetLastProcessedBlock() (uint64, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.lastCheckpoint, nil
}

// UpdateCheckpoint 更新检查点
func (s *BaseScanner) UpdateCheckpoint(blockNumber uint64) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	// 更新内存中的检查点
	s.lastCheckpoint = blockNumber
	s.metrics.LastProcessedBlock = blockNumber

	// 保存到持久化存储
	if s.storage != nil {
		if err := s.storage.SetCheckpoint(context.Background(), s.chainName, blockNumber); err != nil {
			return fmt.Errorf("save checkpoint to storage: %w", err)
		}
	}

	return nil
}

// GetMetrics 获取指标
func (s *BaseScanner) GetMetrics() interfaces.ScannerMetrics {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.metrics
}

// HealthCheck 健康检查
func (s *BaseScanner) HealthCheck(ctx context.Context) error {
	// 检查区块延迟
	if s.metrics.CurrentBlock > 0 && s.metrics.LastProcessedBlock > 0 {
		lag := s.metrics.CurrentBlock - s.metrics.LastProcessedBlock

		// 根据链类型设置不同的阈值
		var lagThreshold uint64 = 100 // 默认阈值
		if s.chainName == "TRON" {
			lagThreshold = 1000 // TRON 允许更大的滞后，因为区块生成更快
		}

		if lag > lagThreshold {
			return fmt.Errorf("block lag too high: %d", lag)
		}
	}

	// 检查错误率
	if s.metrics.LastError != nil && time.Since(s.metrics.LastErrorTime) < 5*time.Minute {
		return fmt.Errorf("recent error: %v", s.metrics.LastError)
	}

	return nil
}

// saveCheckpoint 保存检查点
func (s *BaseScanner) saveCheckpoint() error {
	if s.storage != nil {
		if err := s.storage.SetCheckpoint(context.Background(), s.chainName, s.lastCheckpoint); err != nil {
			return fmt.Errorf("save checkpoint to storage: %w", err)
		}
	}
	s.logger.Info("checkpoint saved", "block", s.lastCheckpoint)
	return nil
}

// SetDAO 设置DAO
func (s *BaseScanner) SetDAO(dao interfaces.DepositDAO) {
	s.dao = dao
}

// Config 获取链配置
func (s *BaseScanner) Config() *types.ChainConfig {
	return s.config
}

// GlobalConfig 获取全局配置
func (s *BaseScanner) GlobalConfig() *types.Config {
	return s.globalConfig
}

// DepositManager 获取存款管理器
func (s *BaseScanner) DepositManager() interfaces.DepositManager {
	return s.depositManager
}

// Logger 获取日志器
func (s *BaseScanner) Logger() *logrus.Entry {
	return s.logger
}

// loadMonitoredAddresses 加载监控地址到内存
func (s *BaseScanner) loadMonitoredAddresses() error {
	if s.dao == nil {
		return fmt.Errorf("dao not configured")
	}

	// 从数据库加载
	addresses, err := s.dao.GetActiveDepositAddresses(s.chainName)
	if err != nil {
		return fmt.Errorf("get active deposit addresses: %w", err)
	}

	s.mu.Lock()
	defer s.mu.Unlock()

	// 转换为map便于快速查找
	s.monitoredAddresses = make(map[string]*interfaces.UserAddress)
	for _, addr := range addresses {
		// 根据链类型决定是否转换为小写
		// TRON 地址是 Base58 编码，大小写敏感，不应该转换
		// ETH 地址是 hex 编码，不区分大小写，可以转换
		addressKey := addr.Address
		if s.chainName != "TRON" {
			addressKey = strings.ToLower(addr.Address)
		}
		s.monitoredAddresses[addressKey] = addr
	}

	s.logger.Info("loaded monitored addresses", "count", len(s.monitoredAddresses))
	return nil
}

// GetMonitoredAddresses 获取监控地址（直接返回内存中的地址）
func (s *BaseScanner) GetMonitoredAddresses() (map[string]*interfaces.UserAddress, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// 返回副本避免并发修改
	result := make(map[string]*interfaces.UserAddress)
	for k, v := range s.monitoredAddresses {
		result[k] = v
	}

	return result, nil
}

// RefreshMonitoredAddresses 刷新监控地址（供外部调用）
func (s *BaseScanner) RefreshMonitoredAddresses() error {
	return s.loadMonitoredAddresses()
}

// 监控指标辅助方法
func (s *BaseScanner) UpdateMetrics(currentBlock uint64) {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.metrics.CurrentBlock = currentBlock

	if s.chainMetrics != nil {
		blockLag := currentBlock - s.metrics.LastProcessedBlock
		s.chainMetrics.SetBlockLag(float64(blockLag))
	}
}

func (s *BaseScanner) IncrementBlocksProcessed() {
	if s.chainMetrics != nil {
		s.chainMetrics.IncrementBlocksProcessed()
	}
}

func (s *BaseScanner) IncrementDepositsDetected(token string) {
	if s.chainMetrics != nil {
		s.chainMetrics.IncrementDepositsDetected(token)
	}
}

func (s *BaseScanner) IncrementDepositsConfirmed(token string) {
	if s.chainMetrics != nil {
		s.chainMetrics.IncrementDepositsConfirmed(token)
	}
}

func (s *BaseScanner) IncrementErrors(errorType string) {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.metrics.ErrorCount++
	s.metrics.LastErrorTime = time.Now()

	if s.chainMetrics != nil {
		s.chainMetrics.IncrementErrors(errorType)
	}
}

func (s *BaseScanner) ObserveBlockProcessingDuration(seconds float64) {
	if s.chainMetrics != nil {
		s.chainMetrics.ObserveBlockProcessingDuration(seconds)
	}
}

func (s *BaseScanner) SetPendingDeposits(count float64) {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.metrics.PendingDeposits = int(count)

	if s.chainMetrics != nil {
		s.chainMetrics.SetPendingDeposits(count)
	}
}
