package utils

import (
	"context"
	"errors"
	"fmt"
	"time"
)

// RetryableError 可重试错误
type RetryableError struct {
	Err        error
	Retryable  bool
	RetryAfter time.Duration
}

// Error 实现error接口
func (e *RetryableError) Error() string {
	return e.Err.Error()
}

// Unwrap 实现错误解包
func (e *RetryableError) Unwrap() error {
	return e.Err
}

// NewRetryableError 创建可重试错误
func NewRetryableError(err error, retryable bool) *RetryableError {
	return &RetryableError{
		Err:       err,
		Retryable: retryable,
	}
}

// RetryOptions 重试选项
type RetryOptions struct {
	MaxRetries   int
	InitialDelay time.Duration
	MaxDelay     time.Duration
	Multiplier   float64
}

// DefaultRetryOptions 默认重试选项
var DefaultRetryOptions = RetryOptions{
	MaxRetries:   3,
	InitialDelay: time.Second,
	MaxDelay:     30 * time.Second,
	Multiplier:   2.0,
}

// RetryWithBackoff 简单的指数退避重试
func RetryWithBackoff(ctx context.Context, fn func() error, maxRetries int) error {
	return RetryWithOptions(ctx, fn, RetryOptions{
		MaxRetries:   maxRetries,
		InitialDelay: time.Second,
		MaxDelay:     30 * time.Second,
		Multiplier:   2.0,
	})
}

// RetryWithOptions 使用自定义选项的重试
func RetryWithOptions(ctx context.Context, fn func() error, opts RetryOptions) error {
	var lastErr error
	delay := opts.InitialDelay
	
	for i := 0; i <= opts.MaxRetries; i++ {
		// 执行函数
		if err := fn(); err == nil {
			return nil
		} else {
			lastErr = err
			
			// 检查是否为可重试错误
			var retryErr *RetryableError
			if errors.As(err, &retryErr) {
				if !retryErr.Retryable {
					return err
				}
				// 如果指定了重试延迟，使用指定的延迟
				if retryErr.RetryAfter > 0 {
					delay = retryErr.RetryAfter
				}
			}
			
			// 如果是最后一次尝试，直接返回错误
			if i == opts.MaxRetries {
				break
			}
			
			// 等待重试
			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-time.After(delay):
				// 计算下次延迟
				delay = time.Duration(float64(delay) * opts.Multiplier)
				if delay > opts.MaxDelay {
					delay = opts.MaxDelay
				}
			}
		}
	}
	
	return fmt.Errorf("max retries exceeded: %w", lastErr)
}

// RetryableFunc 包装函数，添加重试能力
type RetryableFunc func(ctx context.Context) error

// WithRetry 为函数添加重试能力
func WithRetry(fn RetryableFunc, opts RetryOptions) RetryableFunc {
	return func(ctx context.Context) error {
		return RetryWithOptions(ctx, func() error {
			return fn(ctx)
		}, opts)
	}
}

// IsRetryable 检查错误是否可重试
func IsRetryable(err error) bool {
	if err == nil {
		return false
	}
	
	// 检查是否为RetryableError
	var retryErr *RetryableError
	if errors.As(err, &retryErr) {
		return retryErr.Retryable
	}
	
	// 默认某些错误类型可重试
	// 例如：超时、网络错误等
	if errors.Is(err, context.DeadlineExceeded) {
		return true
	}
	
	// 检查错误消息中的关键词
	errMsg := err.Error()
	retryableKeywords := []string{
		"timeout",
		"connection refused",
		"connection reset",
		"temporary failure",
		"rate limit",
	}
	
	for _, keyword := range retryableKeywords {
		if contains(errMsg, keyword) {
			return true
		}
	}
	
	return false
}

// contains 检查字符串是否包含子串（不区分大小写）
func contains(s, substr string) bool {
	return len(s) >= len(substr) && 
		(s == substr || 
		 len(s) > len(substr) && 
		 (contains(s[:len(s)-1], substr) || contains(s[1:], substr)))
}