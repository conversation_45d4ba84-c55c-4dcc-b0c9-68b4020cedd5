package monitoring

import (
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

// Metrics 监控指标
type Metrics struct {
	// 计数器
	BlocksProcessed   *prometheus.CounterVec
	DepositsDetected  *prometheus.CounterVec
	DepositsConfirmed *prometheus.CounterVec
	ErrorsTotal       *prometheus.CounterVec

	// 直方图
	BlockProcessingDuration *prometheus.HistogramVec
	DepositConfirmationTime *prometheus.HistogramVec

	// 仪表盘
	LastProcessedBlock *prometheus.GaugeVec
	BlockLag           *prometheus.GaugeVec
	PendingDeposits    *prometheus.GaugeVec
}

// NewMetrics 创建监控指标
func NewMetrics() *Metrics {
	return &Metrics{
		// 计数器
		BlocksProcessed: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "taskv2_blocks_processed_total",
				Help: "Total number of blocks processed",
			},
			[]string{"chain"},
		),
		DepositsDetected: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "taskv2_deposits_detected_total",
				Help: "Total number of deposits detected",
			},
			[]string{"chain", "token"},
		),
		DepositsConfirmed: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "taskv2_deposits_confirmed_total",
				Help: "Total number of deposits confirmed",
			},
			[]string{"chain", "token"},
		),
		ErrorsTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "taskv2_errors_total",
				Help: "Total number of errors",
			},
			[]string{"chain", "error_type"},
		),

		// 直方图
		BlockProcessingDuration: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "taskv2_block_processing_duration_seconds",
				Help:    "Time spent processing a block",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"chain"},
		),
		DepositConfirmationTime: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "taskv2_deposit_confirmation_time_seconds",
				Help:    "Time from deposit detection to confirmation",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"chain", "token"},
		),

		// 仪表盘
		LastProcessedBlock: promauto.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "taskv2_last_processed_block",
				Help: "Last processed block number",
			},
			[]string{"chain"},
		),
		BlockLag: promauto.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "taskv2_block_lag",
				Help: "Number of blocks behind the latest",
			},
			[]string{"chain"},
		),
		PendingDeposits: promauto.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "taskv2_pending_deposits",
				Help: "Number of pending deposits",
			},
			[]string{"chain"},
		),
	}
}

// ChainMetrics 链特定的指标助手
type ChainMetrics struct {
	metrics *Metrics
	chain   string
}

// NewChainMetrics 创建链特定的指标助手
func NewChainMetrics(metrics *Metrics, chain string) *ChainMetrics {
	return &ChainMetrics{
		metrics: metrics,
		chain:   chain,
	}
}

// IncrementBlocksProcessed 增加处理的区块数
func (c *ChainMetrics) IncrementBlocksProcessed() {
	c.metrics.BlocksProcessed.WithLabelValues(c.chain).Inc()
}

// IncrementDepositsDetected 增加检测到的存款数
func (c *ChainMetrics) IncrementDepositsDetected(token string) {
	c.metrics.DepositsDetected.WithLabelValues(c.chain, token).Inc()
}

// IncrementDepositsConfirmed 增加确认的存款数
func (c *ChainMetrics) IncrementDepositsConfirmed(token string) {
	c.metrics.DepositsConfirmed.WithLabelValues(c.chain, token).Inc()
}

// IncrementErrors 增加错误数
func (c *ChainMetrics) IncrementErrors(errorType string) {
	c.metrics.ErrorsTotal.WithLabelValues(c.chain, errorType).Inc()
}

// ObserveBlockProcessingDuration 记录区块处理时间
func (c *ChainMetrics) ObserveBlockProcessingDuration(seconds float64) {
	c.metrics.BlockProcessingDuration.WithLabelValues(c.chain).Observe(seconds)
}

// ObserveDepositConfirmationTime 记录存款确认时间
func (c *ChainMetrics) ObserveDepositConfirmationTime(token string, seconds float64) {
	c.metrics.DepositConfirmationTime.WithLabelValues(c.chain, token).Observe(seconds)
}

// SetLastProcessedBlock 设置最后处理的区块
func (c *ChainMetrics) SetLastProcessedBlock(blockNumber float64) {
	c.metrics.LastProcessedBlock.WithLabelValues(c.chain).Set(blockNumber)
}

// SetBlockLag 设置区块延迟
func (c *ChainMetrics) SetBlockLag(lag float64) {
	c.metrics.BlockLag.WithLabelValues(c.chain).Set(lag)
}

// SetPendingDeposits 设置待处理存款数
func (c *ChainMetrics) SetPendingDeposits(count float64) {
	c.metrics.PendingDeposits.WithLabelValues(c.chain).Set(count)
}