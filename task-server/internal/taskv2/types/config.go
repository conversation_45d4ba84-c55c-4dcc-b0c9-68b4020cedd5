package types

import "time"

const (
	// DefaultBlockLookback 默认回退的区块数量
	DefaultBlockLookback = 100
)

// Config Task v2 配置
type Config struct {
	Global     GlobalConfig            `yaml:"global"`
	Chains     map[string]ChainConfig  `yaml:"chains"`
	Storage    StorageConfig           `yaml:"storage"`
	Monitoring MonitoringConfig        `yaml:"monitoring"`
	Tasks      TasksConfig             `yaml:"tasks"`
}

// GlobalConfig 全局配置
type GlobalConfig struct {
	LogLevel            string        `yaml:"logLevel"`
	ScanInterval        time.Duration `yaml:"scanInterval"`
	HealthCheckInterval time.Duration `yaml:"healthCheckInterval"`
	MaxRetries          int           `yaml:"maxRetries"`
	RequestTimeout      time.Duration `yaml:"requestTimeout"`
}

// ChainConfig 区块链配置
type ChainConfig struct {
	Enabled       bool         `yaml:"enabled"`
	RpcURL        string       `yaml:"rpcUrl"`
	ApiKey        string       `yaml:"apiKey,omitempty"`
	StartBlock    uint64       `yaml:"startBlock,omitempty"` // 可选，如果不设置则自动计算
	ScanBatchSize int          `yaml:"scanBatchSize"`
	Native        TokenConfig  `yaml:"native"`
	Tokens        []TokenConfig `yaml:"tokens"`
}

// TokenConfig 代币配置
type TokenConfig struct {
	Symbol          string `yaml:"symbol"`
	ContractAddress string `yaml:"contractAddress,omitempty"`
	Decimals        int    `yaml:"decimals"`
	Confirmations   uint64 `yaml:"confirmations"`
	Enabled         bool   `yaml:"enabled"`
}

// StorageConfig 存储配置
type StorageConfig struct {
	Redis RedisConfig `yaml:"redis"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	KeyPrefix   string        `yaml:"keyPrefix"`
	LockTimeout time.Duration `yaml:"lockTimeout"`
}

// MonitoringConfig 监控配置
type MonitoringConfig struct {
	MetricsPort int             `yaml:"metricsPort"`
	Alerting    AlertingConfig  `yaml:"alerting"`
}

// AlertingConfig 告警配置
type AlertingConfig struct {
	ErrorThreshold int           `yaml:"errorThreshold"`
	TimeWindow     time.Duration `yaml:"timeWindow"`
}

// TasksConfig 任务配置
type TasksConfig struct {
	Confirmation ConfirmationTaskConfig `yaml:"confirmation"`
}

// ConfirmationTaskConfig 确认任务配置
type ConfirmationTaskConfig struct {
	Enabled     bool                       `yaml:"enabled"`
	Interval    time.Duration              `yaml:"interval"`
	BatchSize   int                        `yaml:"batchSize"`
	MaxRetries  int                        `yaml:"maxRetries"`
	Timeout     time.Duration              `yaml:"timeout"`
	Thresholds  map[string]string          `yaml:"thresholds"`
	Chains      map[string]ChainTaskConfig `yaml:"chains"`
}

// ChainTaskConfig 链任务配置
type ChainTaskConfig struct {
	Enabled bool `yaml:"enabled"`
}