package types

import (
	"time"

	"github.com/shopspring/decimal"
)

// DepositStatus 存款状态
type DepositStatus string

const (
	DepositStatusDetected  DepositStatus = "detected"  // 刚检测到
	DepositStatusPending   DepositStatus = "pending"   // 等待确认
	DepositStatusConfirmed DepositStatus = "confirmed" // 已确认
	DepositStatusProcessed DepositStatus = "processed" // 已到账
	DepositStatusFailed    DepositStatus = "failed"    // 处理失败
)

// Deposit 存款记录
type Deposit struct {
	ID               uint            `json:"id" db:"id"`
	TxHash           string          `json:"tx_hash" db:"tx_hash"`
	Chain            string          `json:"chain" db:"chain"`
	Token            string          `json:"token" db:"token"`
	FromAddress      string          `json:"from_address" db:"from_address"`
	ToAddress        string          `json:"to_address" db:"to_address"`
	Amount           decimal.Decimal `json:"amount" db:"amount"`
	BlockNumber      uint64          `json:"block_number" db:"block_number"`
	Status           DepositStatus   `json:"status" db:"status"`
	Confirmations    uint64          `json:"confirmations" db:"confirmations"`
	RequiredConfirms uint64          `json:"required_confirms" db:"required_confirms"`
	UserID           uint            `json:"user_id" db:"user_id"`
	TokenID          uint            `json:"token_id" db:"token_id"`
	CreatedAt        time.Time       `json:"created_at" db:"created_at"`
	UpdatedAt        time.Time       `json:"updated_at" db:"updated_at"`
}

// CanTransitionTo 检查状态是否可以转换
func (d *Deposit) CanTransitionTo(newStatus DepositStatus) bool {
	validTransitions := map[DepositStatus][]DepositStatus{
		DepositStatusDetected:  {DepositStatusPending, DepositStatusFailed},
		DepositStatusPending:   {DepositStatusConfirmed, DepositStatusFailed},
		DepositStatusConfirmed: {DepositStatusProcessed, DepositStatusFailed},
		DepositStatusProcessed: {}, // 终态
		DepositStatusFailed:    {DepositStatusPending}, // 可重试
	}

	allowed := validTransitions[d.Status]
	for _, status := range allowed {
		if status == newStatus {
			return true
		}
	}
	return false
}

// UserAddress 用户地址信息
type UserAddress struct {
	ID        uint      `json:"id" db:"id"`
	UserID    uint      `json:"user_id" db:"user_id"`
	Chain     string    `json:"chain" db:"chain"`
	Address   string    `json:"address" db:"address"`
	TokenID   uint      `json:"token_id" db:"token_id"`
	IsActive  bool      `json:"is_active" db:"is_active"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
}