package types

import "time"

// HealthStatus 健康状态
type HealthStatus struct {
	Chain              string    `json:"chain"`
	Status             string    `json:"status"` // healthy, degraded, unhealthy
	LastProcessedBlock uint64    `json:"last_processed_block"`
	CurrentBlock       uint64    `json:"current_block"`
	BlockLag           uint64    `json:"block_lag"`
	LastError          string    `json:"last_error,omitempty"`
	LastErrorTime      time.Time `json:"last_error_time,omitempty"`
}

// HealthStatusCode 健康状态码
type HealthStatusCode string

const (
	HealthStatusHealthy   HealthStatusCode = "healthy"
	HealthStatusDegraded  HealthStatusCode = "degraded"
	HealthStatusUnhealthy HealthStatusCode = "unhealthy"
)

// Block 区块信息
type Block struct {
	Number       uint64         `json:"number"`
	Hash         string         `json:"hash"`
	ParentHash   string         `json:"parent_hash"`
	Timestamp    int64          `json:"timestamp"`
	Transactions []*Transaction `json:"transactions"`
}

// Transaction 交易信息
type Transaction struct {
	Hash     string          `json:"hash"`
	From     string          `json:"from"`
	To       string          `json:"to"`
	Value    string          `json:"value"`
	Data     string          `json:"data"`
	GasUsed  uint64          `json:"gas_used"`
	Status   bool            `json:"status"`
	TokenTransfers []TokenTransfer `json:"token_transfers,omitempty"`
}

// TokenTransfer 代币转账信息
type TokenTransfer struct {
	Contract string `json:"contract"`
	From     string `json:"from"`
	To       string `json:"to"`
	Value    string `json:"value"`
	Symbol   string `json:"symbol"`
	Decimals int    `json:"decimals"`
}