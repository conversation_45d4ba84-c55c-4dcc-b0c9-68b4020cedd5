package cmd

import (
	"context"
	"fmt"
	"net"
	"os"
	"os/signal"
	"syscall"
	taskv1 "task-api/api" // 导入生成的 pb 文件 (修正路径)

	// "task-api/internal/service" // 不再直接使用 service 包的构造函数
	grpcadapter "task-api/internal/adapter/grpc" // 导入 gRPC 适配器
	// _ "task-api/internal/logic/task_impl"      // 导入 logic 实现包以执行其 init 函数

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcmd"

	// "github.com/gogf/gf/v2/os/glog" // Removed unused import

	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection" // 导入反射服务

	// 恢复导入 cmd 包以访问 Main
	_ "task-api/internal/logic/redis"
	_ "task-api/internal/logic/task"  // Import task logic to ensure registration
	_ "task-api/internal/logic/token" // Explicitly import token logic to ensure registration
	_ "task-api/internal/logic/user_recharges"

	_ "task-api/internal/packed"

	_ "github.com/gogf/gf/contrib/drivers/mysql/v2"
	_ "github.com/gogf/gf/contrib/nosql/redis/v2" // Add Redis adapter import
)

var (
	ServeGrpc = &gcmd.Command{
		Name:  "serve-grpc",
		Usage: "serve-grpc [--port <port>]",
		Brief: "start the gRPC server",
		Func:  serveGrpcFunc,
	}
)

func serveGrpcFunc(ctx context.Context, parser *gcmd.Parser) (err error) {
	logger := g.Log("cmd.serve-grpc")

	// --- 1. 获取配置 ---
	// 从配置文件读取端口，提供默认值
	grpcPort := g.Cfg().MustGet(ctx, "grpcServer.port", 50051).Int() // 默认 50051
	// 允许命令行覆盖配置
	if portOpt := parser.GetOpt("port"); portOpt != nil {
		grpcPort = portOpt.Int()
	}
	listenAddr := fmt.Sprintf(":%d", grpcPort)
	logger.Infof(ctx, "gRPC server attempting to listen on %s", listenAddr)

	// --- 2. 创建 TCP 监听 ---
	lis, err := net.Listen("tcp", listenAddr)
	if err != nil {
		logger.Fatalf(ctx, "Failed to listen: %v", err)
		return err // 虽然 Fatalf 会退出，但还是返回 err
	}
	logger.Infof(ctx, "gRPC server listening on %s", lis.Addr().String())

	// --- 3. 创建 gRPC 服务器实例 ---
	// 可以添加拦截器等选项
	grpcServer := grpc.NewServer(
		grpc.UnaryInterceptor(grpcadapter.ApiKeyAuthInterceptor), // 应用 API Key 认证拦截器
	)

	// --- 4. 注册服务实现 ---
	// 使用适配器层提供的构造函数
	taskServiceImpl := grpcadapter.NewTaskGrpcService() // 创建服务实例
	taskv1.RegisterTaskServiceServer(grpcServer, taskServiceImpl)
	logger.Info(ctx, "Registered TaskServiceServer implementation from adapter.")

	// --- 5. 注册反射服务 (可选，方便调试) ---
	reflection.Register(grpcServer)
	logger.Info(ctx, "Registered gRPC reflection service.")

	// --- 6. 启动服务器 (异步) ---
	go func() {
		logger.Info(ctx, "Starting gRPC server...")
		if err := grpcServer.Serve(lis); err != nil && err != grpc.ErrServerStopped {
			// 通常 Serve 会阻塞，除非出错或被 Stop/GracefulStop
			// 如果不是因为正常关闭而出错，则记录 Fatal
			logger.Fatalf(ctx, "gRPC server failed to serve: %v", err)
		} else if err == grpc.ErrServerStopped {
			logger.Info(ctx, "gRPC server stopped gracefully.")
		}
	}()

	// --- 7. 处理优雅关闭 ---
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	sig := <-quit
	logger.Infof(ctx, "Received signal %s. Shutting down gRPC server...", sig)

	// 调用 GracefulStop 或 Stop
	// grpcServer.GracefulStop() // 等待现有连接完成
	grpcServer.Stop() // 立即停止

	logger.Info(ctx, "gRPC server shut down.")
	return nil
}
