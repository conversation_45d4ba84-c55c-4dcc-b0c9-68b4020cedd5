package service

import (
	"context"
	// "fmt" // Removed unused import
	// "strings" // Removed unused import

	// "github.com/gogf/gf/v2/errors/gcode" // Removed unused import
	// "github.com/gogf/gf/v2/errors/gerror" // Removed unused import
	// "github.com/gogf/gf/v2/frame/g" // Removed unused import
	"github.com/shopspring/decimal"

	// "task-api/internal/dao" // Removed unused import
	"task-api/internal/model/entity"
)

// IToken defines the interface for token service operations.
type IToken interface {
	// IsDepositAllowed checks if depositing a specific token on a given network is allowed.
	// It considers the token's allow_deposit, is_active, and status fields.
	IsDepositAllowed(ctx context.Context, symbol string, network string) (bool, error)
	// GetActiveWithdrawalSymbols returns a list of symbols that are active for withdrawal.
	GetActiveWithdrawalSymbols(ctx context.Context) ([]string, error)
	// GetTokenDetailsForWithdrawal returns token details for a specific symbol and chain.
	GetTokenDetailsForWithdrawal(ctx context.Context, symbol string, chain string) (*entity.Tokens, error)
	// GetTokensBySymbol returns all tokens with the given symbol that are active.
	GetTokensBySymbol(ctx context.Context, symbol string) ([]*entity.Tokens, error)
	// GetTokenInfo returns token information with withdrawal fee calculations.
	// It calculates the fee based on the token's fee type (fixed or percent).
	// Returns token info, withdrawal fee, min/max withdrawal amount.
	GetTokenInfo(ctx context.Context, symbol string, chain string) (*entity.Tokens, string, error)
	// GetActiveTransferSymbols returns a list of unique symbols that are active and allowed for transfer.
	GetActiveTransferSymbols(ctx context.Context) ([]string, error)
	// GetTokenBySymbol returns the first active token matching the given symbol.
	GetTokenBySymbol(ctx context.Context, symbol string) (*entity.Tokens, error)
	GetTokenByID(ctx context.Context, id uint) (*entity.Tokens, error)
	// GetActiveTransferTokens returns a list of tokens that are active and allowed for transfer.
	GetActiveTransferTokens(ctx context.Context) ([]*entity.Tokens, error)
	// GetActiveRedPacketTokens returns a list of tokens that are active and allowed for red packet.
	GetActiveRedPacketTokens(ctx context.Context) ([]*entity.Tokens, error)
	// GetActiveReceiveTokens returns a list of tokens that are active and allowed for receiving payments.
	GetActiveReceiveTokens(ctx context.Context) ([]*entity.Tokens, error)
	// GetToken retrieves a specific token by symbol and network.
	GetToken(ctx context.Context, symbol, network string) (*entity.Tokens, error)
	// ConvertBalanceToRaw 将用户余额转换为原始值（乘以10^decimals）
	ConvertBalanceToRaw(ctx context.Context, balance decimal.Decimal, symbol string) (decimal.Decimal, error)
	// ConvertRawToBalance 将原始值转换回用户余额（除以10^decimals）
	ConvertRawToBalance(ctx context.Context, rawBalance decimal.Decimal, symbol string) (decimal.Decimal, error)
	// FormatUserBalance 格式化用户余额并移除多余小数点和零
	FormatUserBalance(ctx context.Context, balance decimal.Decimal, symbol string) decimal.Decimal
	// FormatUserBalanceWithSymbol 格式化用户余额并添加代币符号
	FormatUserBalanceWithSymbol(ctx context.Context, balance decimal.Decimal, symbol string) string
}

// No implementation details here.

var localToken IToken

// RegisterToken 注册 Token 服务实现
func RegisterToken(i IToken) {
	localToken = i
}

// Token 获取 Token 服务实例
func Token() IToken {
	if localToken == nil {
		panic("implement not found for interface IToken, forgot register?")
	}
	return localToken
}
