package service

import (
	"context"
	// "task-api/internal/task/checker" // No longer needed here
	// "task-api/internal/ports" // Import ports for DepositInfo

	"github.com/a19ba14d/tg-bot-common/consts"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/shopspring/decimal"
)

// ITransaction 定义交易流水服务接口
type ITransaction interface {
	// Record 记录一笔交易流水
	// tx: 数据库事务对象，如果需要在事务中执行，则传入；否则传入 nil
	// userID: 用户 ID (数据库 users.id)
	// username: 用户 ID (数据库 users.id, 对应 transaction.username)
	// amount: 交易金额 (绝对值)
	// symbol: 代币符号
	// transactionType: 交易类型
	// balanceBefore: 交易前余额
	// referenceID: 关联实体 ID (字符串形式)
	// description: 交易描述
	// 返回: 交易流水 ID 和错误
	Record(ctx context.Context,
		tx gdb.TX,
		userID uint,
		amount decimal.Decimal,
		symbol string,
		transactionType consts.TransactionType,
		referenceID string,
		description string) (transactionID int64, err error)

	// RecordDepositTransaction 记录一笔检测到的充值交易
	// deposit: 包含充值信息的结构体 (from ports)
	// 返回: 错误
	// RecordDepositTransaction(ctx context.Context, deposit ports.DepositInfo) error // Use ports.DepositInfo

	// RecordConfirmedDepositTransaction 记录一笔已确认并入账的充值交易流水
	// balanceBefore: 调用此函数前查询到的钱包余额 (float64, 与 transactions 表字段一致)
	// deposit: 包含充值信息的结构体，Amount 应为 decimal 格式的字符串
	// RecordConfirmedDepositTransaction(ctx context.Context, deposit ports.DepositInfo, balanceBefore float64) error
}

// 文件末尾，无内容替换

// No implementation details here.

var localTransaction ITransaction

// RegisterTransaction 注册 Transaction 服务实现
func RegisterTransaction(i ITransaction) {
	localTransaction = i
}

// Transaction 获取 Transaction 服务实例
func Transaction() ITransaction {
	if localTransaction == nil {
		panic("implement not found for interface ITransaction, forgot register?")
	}
	return localTransaction
}
