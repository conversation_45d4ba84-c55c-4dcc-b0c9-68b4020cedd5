package service

import (
	"context"
	"task-api/internal/model/entity"
)

// 引入 protobuf 定义

// ITask 定义了任务服务的接口
type ITask interface {
	// ListUserWithdraws 获取用户提现记录列表（带分页和筛选）
	ListUserWithdraws(ctx context.Context, page, pageSize int, filter map[string]interface{}) (list []*entity.UserWithdraws, total int, err error)

	// UpdateUserWithdrawStatus 更新用户提现记录状态和相关字段
	UpdateUserWithdrawStatus(ctx context.Context, id uint, status uint, data map[string]interface{}) error
}

// No implementation details here.

var localTask ITask

// RegisterTask 注册 Task 服务实现
func RegisterTask(i ITask) {
	localTask = i
}

// Task 获取 Task 服务实例
func Task() ITask {
	if localTask == nil {
		panic("implement not found for interface ITask, forgot register?")
	}
	return localTask
}
