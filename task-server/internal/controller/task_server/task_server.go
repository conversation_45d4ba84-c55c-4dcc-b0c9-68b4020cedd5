package task_server

import (
	"context"

	api "task-api/api"

	"github.com/gogf/gf/contrib/rpc/grpcx/v2"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
)

type Controller struct {
	api.UnimplementedTaskServiceServer
}

func Register(s *grpcx.GrpcServer) {
	api.RegisterTaskServiceServer(s.Server, &Controller{})
}

func (*Controller) ListWithdrawals(ctx context.Context, req *api.ListWithdrawalsRequest) (res *api.ApiResponse, err error) {
	return nil, gerror.NewCode(gcode.CodeNotImplemented)
}

func (*Controller) UpdateWithdrawalStatus(ctx context.Context, req *api.UpdateWithdrawalStatusRequest) (res *api.ApiResponse, err error) {
	return nil, gerror.NewCode(gcode.CodeNotImplemented)
}
