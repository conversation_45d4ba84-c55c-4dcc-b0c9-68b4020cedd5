// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"task-api/internal/dao/internal"
)

// merchantSettlementsDao is the data access object for the table merchant_settlements.
// You can define custom methods on it to extend its functionality as needed.
type merchantSettlementsDao struct {
	*internal.MerchantSettlementsDao
}

var (
	// MerchantSettlements is a globally accessible object for table merchant_settlements operations.
	MerchantSettlements = merchantSettlementsDao{internal.NewMerchantSettlementsDao()}
)

// Add your custom methods and functionality below.
