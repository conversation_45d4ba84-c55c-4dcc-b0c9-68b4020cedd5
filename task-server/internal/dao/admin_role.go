// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"task-api/internal/dao/internal"
)

// adminRoleDao is the data access object for the table admin_role.
// You can define custom methods on it to extend its functionality as needed.
type adminRoleDao struct {
	*internal.AdminRoleDao
}

var (
	// AdminRole is a globally accessible object for table admin_role operations.
	AdminRole = adminRoleDao{internal.NewAdminRoleDao()}
)

// Add your custom methods and functionality below.
