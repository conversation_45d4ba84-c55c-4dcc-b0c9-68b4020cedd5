// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"task-api/internal/dao/internal"
)

// adminNoticeDao is the data access object for the table admin_notice.
// You can define custom methods on it to extend its functionality as needed.
type adminNoticeDao struct {
	*internal.AdminNoticeDao
}

var (
	// AdminNotice is a globally accessible object for table admin_notice operations.
	AdminNotice = adminNoticeDao{internal.NewAdminNoticeDao()}
)

// Add your custom methods and functionality below.
