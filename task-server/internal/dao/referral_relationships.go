// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"task-api/internal/dao/internal"
)

// referralRelationshipsDao is the data access object for the table referral_relationships.
// You can define custom methods on it to extend its functionality as needed.
type referralRelationshipsDao struct {
	*internal.ReferralRelationshipsDao
}

var (
	// ReferralRelationships is a globally accessible object for table referral_relationships operations.
	ReferralRelationships = referralRelationshipsDao{internal.NewReferralRelationshipsDao()}
)

// Add your custom methods and functionality below.
