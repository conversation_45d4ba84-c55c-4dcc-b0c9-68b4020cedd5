// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"task-api/internal/dao/internal"
)

// redPacketClaimsDao is the data access object for the table red_packet_claims.
// You can define custom methods on it to extend its functionality as needed.
type redPacketClaimsDao struct {
	*internal.RedPacketClaimsDao
}

var (
	// RedPacketClaims is a globally accessible object for table red_packet_claims operations.
	RedPacketClaims = redPacketClaimsDao{internal.NewRedPacketClaimsDao()}
)

// Add your custom methods and functionality below.
