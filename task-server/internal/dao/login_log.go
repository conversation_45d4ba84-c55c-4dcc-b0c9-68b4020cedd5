// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"task-api/internal/dao/internal"
)

// loginLogDao is the data access object for the table login_log.
// You can define custom methods on it to extend its functionality as needed.
type loginLogDao struct {
	*internal.LoginLogDao
}

var (
	// LoginLog is a globally accessible object for table login_log operations.
	LoginLog = loginLogDao{internal.NewLoginLogDao()}
)

// Add your custom methods and functionality below.
