// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"task-api/internal/dao/internal"
)

// tokensDao is the data access object for the table tokens.
// You can define custom methods on it to extend its functionality as needed.
type tokensDao struct {
	*internal.TokensDao
}

var (
	// Tokens is a globally accessible object for table tokens operations.
	Tokens = tokensDao{internal.NewTokensDao()}
)

// Add your custom methods and functionality below.
