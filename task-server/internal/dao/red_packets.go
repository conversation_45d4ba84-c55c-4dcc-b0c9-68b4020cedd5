// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"task-api/internal/dao/internal"
)

// redPacketsDao is the data access object for the table red_packets.
// You can define custom methods on it to extend its functionality as needed.
type redPacketsDao struct {
	*internal.RedPacketsDao
}

var (
	// RedPackets is a globally accessible object for table red_packets operations.
	RedPackets = redPacketsDao{internal.NewRedPacketsDao()}
)

// Add your custom methods and functionality below.
