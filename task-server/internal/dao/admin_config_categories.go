// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"task-api/internal/dao/internal"
)

// adminConfigCategoriesDao is the data access object for the table admin_config_categories.
// You can define custom methods on it to extend its functionality as needed.
type adminConfigCategoriesDao struct {
	*internal.AdminConfigCategoriesDao
}

var (
	// AdminConfigCategories is a globally accessible object for table admin_config_categories operations.
	AdminConfigCategories = adminConfigCategoriesDao{internal.NewAdminConfigCategoriesDao()}
)

// Add your custom methods and functionality below.
