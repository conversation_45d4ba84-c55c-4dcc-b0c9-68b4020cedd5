// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"task-api/internal/dao/internal"
)

// transfersDao is the data access object for the table transfers.
// You can define custom methods on it to extend its functionality as needed.
type transfersDao struct {
	*internal.TransfersDao
}

var (
	// Transfers is a globally accessible object for table transfers operations.
	Transfers = transfersDao{internal.NewTransfersDao()}
)

// Add your custom methods and functionality below.
