// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"task-api/internal/dao/internal"
)

// withdrawalRiskControlSettingsDao is the data access object for the table withdrawal_risk_control_settings.
// You can define custom methods on it to extend its functionality as needed.
type withdrawalRiskControlSettingsDao struct {
	*internal.WithdrawalRiskControlSettingsDao
}

var (
	// WithdrawalRiskControlSettings is a globally accessible object for table withdrawal_risk_control_settings operations.
	WithdrawalRiskControlSettings = withdrawalRiskControlSettingsDao{internal.NewWithdrawalRiskControlSettingsDao()}
)

// Add your custom methods and functionality below.
