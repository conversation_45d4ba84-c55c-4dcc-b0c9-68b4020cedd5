// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"task-api/internal/dao/internal"
)

// merchantMenuDao is the data access object for the table merchant_menu.
// You can define custom methods on it to extend its functionality as needed.
type merchantMenuDao struct {
	*internal.MerchantMenuDao
}

var (
	// MerchantMenu is a globally accessible object for table merchant_menu operations.
	MerchantMenu = merchantMenuDao{internal.NewMerchantMenuDao()}
)

// Add your custom methods and functionality below.
