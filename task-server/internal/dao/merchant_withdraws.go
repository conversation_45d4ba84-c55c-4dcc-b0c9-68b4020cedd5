// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"task-api/internal/dao/internal"
)

// merchantWithdrawsDao is the data access object for the table merchant_withdraws.
// You can define custom methods on it to extend its functionality as needed.
type merchantWithdrawsDao struct {
	*internal.MerchantWithdrawsDao
}

var (
	// MerchantWithdraws is a globally accessible object for table merchant_withdraws operations.
	MerchantWithdraws = merchantWithdrawsDao{internal.NewMerchantWithdrawsDao()}
)

// Add your custom methods and functionality below.
