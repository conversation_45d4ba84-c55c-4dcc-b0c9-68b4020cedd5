// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"task-api/internal/dao/internal"
)

// operationLogDao is the data access object for the table operation_log.
// You can define custom methods on it to extend its functionality as needed.
type operationLogDao struct {
	*internal.OperationLogDao
}

var (
	// OperationLog is a globally accessible object for table operation_log operations.
	OperationLog = operationLogDao{internal.NewOperationLogDao()}
)

// Add your custom methods and functionality below.
