// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"task-api/internal/dao/internal"
)

// merchantCallbackNotificationsDao is the data access object for the table merchant_callback_notifications.
// You can define custom methods on it to extend its functionality as needed.
type merchantCallbackNotificationsDao struct {
	*internal.MerchantCallbackNotificationsDao
}

var (
	// MerchantCallbackNotifications is a globally accessible object for table merchant_callback_notifications operations.
	MerchantCallbackNotifications = merchantCallbackNotificationsDao{internal.NewMerchantCallbackNotificationsDao()}
)

// Add your custom methods and functionality below.
