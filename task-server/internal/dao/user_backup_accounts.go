// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"task-api/internal/dao/internal"
)

// userBackupAccountsDao is the data access object for the table user_backup_accounts.
// You can define custom methods on it to extend its functionality as needed.
type userBackupAccountsDao struct {
	*internal.UserBackupAccountsDao
}

var (
	// UserBackupAccounts is a globally accessible object for table user_backup_accounts operations.
	UserBackupAccounts = userBackupAccountsDao{internal.NewUserBackupAccountsDao()}
)

// Add your custom methods and functionality below.
