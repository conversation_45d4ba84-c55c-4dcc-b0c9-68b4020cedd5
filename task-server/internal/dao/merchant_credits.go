// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"task-api/internal/dao/internal"
)

// merchantCreditsDao is the data access object for the table merchant_credits.
// You can define custom methods on it to extend its functionality as needed.
type merchantCreditsDao struct {
	*internal.MerchantCreditsDao
}

var (
	// MerchantCredits is a globally accessible object for table merchant_credits operations.
	MerchantCredits = merchantCreditsDao{internal.NewMerchantCreditsDao()}
)

// Add your custom methods and functionality below.
