// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"task-api/internal/dao/internal"
)

// transactionsDao is the data access object for the table transactions.
// You can define custom methods on it to extend its functionality as needed.
type transactionsDao struct {
	*internal.TransactionsDao
}

var (
	// Transactions is a globally accessible object for table transactions operations.
	Transactions = transactionsDao{internal.NewTransactionsDao()}
)

// Add your custom methods and functionality below.
