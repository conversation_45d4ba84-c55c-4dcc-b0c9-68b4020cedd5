// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"task-api/internal/dao/internal"
)

// ipAccessListDao is the data access object for the table ip_access_list.
// You can define custom methods on it to extend its functionality as needed.
type ipAccessListDao struct {
	*internal.IpAccessListDao
}

var (
	// IpAccessList is a globally accessible object for table ip_access_list operations.
	IpAccessList = ipAccessListDao{internal.NewIpAccessListDao()}
)

// Add your custom methods and functionality below.
