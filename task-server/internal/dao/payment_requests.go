// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"task-api/internal/dao/internal"
)

// paymentRequestsDao is the data access object for the table payment_requests.
// You can define custom methods on it to extend its functionality as needed.
type paymentRequestsDao struct {
	*internal.PaymentRequestsDao
}

var (
	// PaymentRequests is a globally accessible object for table payment_requests operations.
	PaymentRequests = paymentRequestsDao{internal.NewPaymentRequestsDao()}
)

// Add your custom methods and functionality below.
