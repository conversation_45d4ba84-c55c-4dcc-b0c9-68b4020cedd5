// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"task-api/internal/dao/internal"
)

// adminPermissionsDao is the data access object for the table admin_permissions.
// You can define custom methods on it to extend its functionality as needed.
type adminPermissionsDao struct {
	*internal.AdminPermissionsDao
}

var (
	// AdminPermissions is a globally accessible object for table admin_permissions operations.
	AdminPermissions = adminPermissionsDao{internal.NewAdminPermissionsDao()}
)

// Add your custom methods and functionality below.
