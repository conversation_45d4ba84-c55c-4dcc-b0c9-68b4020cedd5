// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"task-api/internal/dao/internal"
)

// imagesDao is the data access object for the table images.
// You can define custom methods on it to extend its functionality as needed.
type imagesDao struct {
	*internal.ImagesDao
}

var (
	// Images is a globally accessible object for table images operations.
	Images = imagesDao{internal.NewImagesDao()}
)

// Add your custom methods and functionality below.
