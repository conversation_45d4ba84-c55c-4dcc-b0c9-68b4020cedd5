// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"context"
	"task-api/internal/dao/internal"
	"task-api/internal/model/entity"
)

// addressDao is the data access object for the table address.
// You can define custom methods on it to extend its functionality as needed.
type addressDao struct {
	*internal.AddressDao
}

var (
	// Address is a globally accessible object for table address operations.
	Address = addressDao{internal.NewAddressDao()}
)

// Add your custom methods and functionality below.

// Database Index Recommendations:
// For optimal performance of address queries, the following indexes are recommended:
//
// 1. Index on 'address' field for unique address lookups:
//    CREATE INDEX idx_address_address ON address(address);
//
// 2. Composite index on 'chan' and 'address' for chain-specific address lookups:
//    CREATE INDEX idx_address_chan_address ON address(chan, address);
//
// 3. Index on 'chan' field for chain-specific address fetching:
//    CREATE INDEX idx_address_chan ON address(chan);
//
// 4. Index on 'bind_status' for filtering bound/unbound addresses:
//    CREATE INDEX idx_address_bind_status ON address(bind_status);

// GetActiveDepositAddresses 获取指定链的活跃充值地址 (包括绑定状态过滤)
func (dao *addressDao) GetActiveDepositAddresses(ctx context.Context, chainName string) ([]*entity.Address, error) {
	var addresses []*entity.Address
	err := dao.Ctx(ctx).
		Where(dao.Columns().Chan, chainName).
		Where(dao.Columns().BindStatus, 1). // 只获取已绑定的地址
		Scan(&addresses)
	return addresses, err
}

// GetUniqueDepositAddress 根据地址字段唯一查询地址
func (dao *addressDao) GetUniqueDepositAddress(ctx context.Context, address string) (*entity.Address, error) {
	var addressEntity *entity.Address
	err := dao.Ctx(ctx).
		Where(dao.Columns().Address, address).
		Scan(&addressEntity)
	return addressEntity, err
}

// GetUniqueDepositAddressByChainAndAddress 根据链和地址唯一查询地址
func (dao *addressDao) GetUniqueDepositAddressByChainAndAddress(ctx context.Context, chainName, address string) (*entity.Address, error) {
	var addressEntity *entity.Address
	err := dao.Ctx(ctx).
		Where(dao.Columns().Chan, chainName).
		Where(dao.Columns().Address, address).
		Scan(&addressEntity)
	return addressEntity, err
}
