// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"task-api/internal/dao/internal"
)

// fiatCurrenciesDao is the data access object for the table fiat_currencies.
// You can define custom methods on it to extend its functionality as needed.
type fiatCurrenciesDao struct {
	*internal.FiatCurrenciesDao
}

var (
	// FiatCurrencies is a globally accessible object for table fiat_currencies operations.
	FiatCurrencies = fiatCurrenciesDao{internal.NewFiatCurrenciesDao()}
)

// Add your custom methods and functionality below.
