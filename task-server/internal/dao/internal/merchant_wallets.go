// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// MerchantWalletsDao is the data access object for the table merchant_wallets.
type MerchantWalletsDao struct {
	table    string                 // table is the underlying table name of the DAO.
	group    string                 // group is the database configuration group name of the current DAO.
	columns  MerchantWalletsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler     // handlers for customized model modification.
}

// MerchantWalletsColumns defines and stores column names for the table merchant_wallets.
type MerchantWalletsColumns struct {
	WalletId         string // 钱包记录唯一 ID
	LedgerId         string // ledger 钱包id
	MerchantId       string // 用户 ID (外键, 关联 users.user_id)
	AvailableBalance string // 可用余额
	FrozenBalance    string // 冻结余额 (例如: 挂单中, 提现处理中)
	DecimalPlaces    string // 精度
	CreatedAt        string // 钱包记录创建时间
	UpdatedAt        string // 余额最后更新时间
	DeletedAt        string // 软删除的时间戳
	TelegramId       string //
	Type             string // 类型
	Symbol           string // 代币符号 (例如: USDT, BTC, ETH)
}

// merchantWalletsColumns holds the columns for the table merchant_wallets.
var merchantWalletsColumns = MerchantWalletsColumns{
	WalletId:         "wallet_id",
	LedgerId:         "ledger_id",
	MerchantId:       "merchant_id",
	AvailableBalance: "available_balance",
	FrozenBalance:    "frozen_balance",
	DecimalPlaces:    "decimal_places",
	CreatedAt:        "created_at",
	UpdatedAt:        "updated_at",
	DeletedAt:        "deleted_at",
	TelegramId:       "telegram_id",
	Type:             "type",
	Symbol:           "symbol",
}

// NewMerchantWalletsDao creates and returns a new DAO object for table data access.
func NewMerchantWalletsDao(handlers ...gdb.ModelHandler) *MerchantWalletsDao {
	return &MerchantWalletsDao{
		group:    "default",
		table:    "merchant_wallets",
		columns:  merchantWalletsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *MerchantWalletsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *MerchantWalletsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *MerchantWalletsDao) Columns() MerchantWalletsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *MerchantWalletsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *MerchantWalletsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *MerchantWalletsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
