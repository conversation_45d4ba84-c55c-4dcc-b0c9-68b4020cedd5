// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AuthPaymentOrdersDao is the data access object for the table auth_payment_orders.
type AuthPaymentOrdersDao struct {
	table    string                   // table is the underlying table name of the DAO.
	group    string                   // group is the database configuration group name of the current DAO.
	columns  AuthPaymentOrdersColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler       // handlers for customized model modification.
}

// AuthPaymentOrdersColumns defines and stores column names for the table auth_payment_orders.
type AuthPaymentOrdersColumns struct {
	Id                    string // 主键ID
	OrderNo               string // 系统订单号
	MerchantId            string // 商户ID
	UserAccount           string // 用户账户标识(users.account)
	UserId                string // 用户ID(查询到后填充)
	OrderType             string // 订单类型: deduct-扣款, add-加款
	TokenSymbol           string // 代币符号
	Amount                string // 订单金额
	AuthReason            string // 授权原因/说明
	MerchantOrderNo       string // 商户订单号
	MerchantTransactionId string // 商户交易记录ID
	UserTransactionId     string // 用户交易记录ID
	Status                string // 订单状态
	CallbackUrl           string // 回调URL(使用商户默认或指定)
	CallbackStatus        string // 回调状态
	CallbackAttempts      string // 回调尝试次数
	LastCallbackAt        string // 最后回调时间
	NextCallbackAt        string // 下次回调时间
	CallbackResponse      string // 最后回调响应
	ExpireAt              string // 订单过期时间(仅扣款订单)
	CompletedAt           string // 完成时间
	ErrorMessage          string // 错误信息
	RequestIp             string // 请求IP
	CreatedAt             string // 创建时间
	UpdatedAt             string // 更新时间
	CallbackBot           string // 回调telegram 机器人
	NotificationSent      string // 是否已发送通知: 0-未发送, 1-已发送
	NotificationSentAt    string // 通知发送时间
}

// authPaymentOrdersColumns holds the columns for the table auth_payment_orders.
var authPaymentOrdersColumns = AuthPaymentOrdersColumns{
	Id:                    "id",
	OrderNo:               "order_no",
	MerchantId:            "merchant_id",
	UserAccount:           "user_account",
	UserId:                "user_id",
	OrderType:             "order_type",
	TokenSymbol:           "token_symbol",
	Amount:                "amount",
	AuthReason:            "auth_reason",
	MerchantOrderNo:       "merchant_order_no",
	MerchantTransactionId: "merchant_transaction_id",
	UserTransactionId:     "user_transaction_id",
	Status:                "status",
	CallbackUrl:           "callback_url",
	CallbackStatus:        "callback_status",
	CallbackAttempts:      "callback_attempts",
	LastCallbackAt:        "last_callback_at",
	NextCallbackAt:        "next_callback_at",
	CallbackResponse:      "callback_response",
	ExpireAt:              "expire_at",
	CompletedAt:           "completed_at",
	ErrorMessage:          "error_message",
	RequestIp:             "request_ip",
	CreatedAt:             "created_at",
	UpdatedAt:             "updated_at",
	CallbackBot:           "callback_bot",
	NotificationSent:      "notification_sent",
	NotificationSentAt:    "notification_sent_at",
}

// NewAuthPaymentOrdersDao creates and returns a new DAO object for table data access.
func NewAuthPaymentOrdersDao(handlers ...gdb.ModelHandler) *AuthPaymentOrdersDao {
	return &AuthPaymentOrdersDao{
		group:    "default",
		table:    "auth_payment_orders",
		columns:  authPaymentOrdersColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AuthPaymentOrdersDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AuthPaymentOrdersDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AuthPaymentOrdersDao) Columns() AuthPaymentOrdersColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AuthPaymentOrdersDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AuthPaymentOrdersDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AuthPaymentOrdersDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
