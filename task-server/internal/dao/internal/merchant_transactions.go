// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// MerchantTransactionsDao is the data access object for the table merchant_transactions.
type MerchantTransactionsDao struct {
	table    string                      // table is the underlying table name of the DAO.
	group    string                      // group is the database configuration group name of the current DAO.
	columns  MerchantTransactionsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler          // handlers for customized model modification.
}

// MerchantTransactionsColumns defines and stores column names for the table merchant_transactions.
type MerchantTransactionsColumns struct {
	TransactionId        string // 交易记录 ID (主键)
	TransactionChannel   string // 交易方式 auth 授权支付，okpay, okpay 渠道  , addres 地址渠道
	MerchantId           string // 关联用户 ID
	Username             string // telegra username
	TokenId              string // 关联代币 ID
	Type                 string // 交易类型: deposit, withdrawal, transfer, red_packet, payment, commission, system_adjust, etc.
	WalletType           string // 钱包类型:  冻结frozen ，余额 available
	Direction            string // 资金方向: in-增加, out-减少
	Amount               string // 交易金额 (绝对值)
	BalanceBefore        string // 交易前余额快照 (对应钱包)
	BalanceAfter         string // 交易后余额快照 (对应钱包)
	RelatedTransactionId string // 关联交易 ID (例如: 转账的对方记录)
	RelatedEntityId      string // 关联实体 ID (例如: 红包 ID, 提现订单 ID)
	RelatedEntityType    string // 关联实体类型 (例如: red_packet, withdrawal_order)
	Status               string // 交易状态: 1-成功, 0-失败
	Memo                 string // 交易备注/消息 (例如: 管理员调账原因)
	CreatedAt            string // 创建时间
	UpdatedAt            string // 最后更新时间
	DeletedAt            string // 软删除时间
	Symbol               string // 代币符号 (例如: USDT, BTC, ETH)
	BusinessId           string // 业务唯一标识符，用于幂等性检查
	RequestAmount        string // 用户请求的原始金额 (用户输入的金额)
	RequestReference     string // 用户请求的参考信息 (如转账备注、提现地址等)
	RequestMetadata      string // 用户请求的元数据 (JSON格式存储扩展信息)
	RequestSource        string // 请求来源 (telegram, web, api, admin等)
	RequestIp            string // 用户请求的IP地址
	RequestUserAgent     string // 用户请求的User-Agent
	RequestTimestamp     string // 用户发起请求的时间戳
	ProcessedAt          string // 交易处理完成时间
	FeeAmount            string // 手续费金额
	FeeType              string // 手续费类型 (fixed, percentage)
	ExchangeRate         string // 汇率 (如果涉及币种转换)
	TargetUserId         string // 目标用户ID (转账、红包等操作的接收方)
	TargetUsername       string // 目标用户名 (转账、红包等操作的接收方用户名)
	DtmGid               string // DTM全局事务ID
	DtmBranchId          string // DTM分支ID
}

// merchantTransactionsColumns holds the columns for the table merchant_transactions.
var merchantTransactionsColumns = MerchantTransactionsColumns{
	TransactionId:        "transaction_id",
	TransactionChannel:   "transaction_channel",
	MerchantId:           "merchant_id",
	Username:             "username",
	TokenId:              "token_id",
	Type:                 "type",
	WalletType:           "wallet_type",
	Direction:            "direction",
	Amount:               "amount",
	BalanceBefore:        "balance_before",
	BalanceAfter:         "balance_after",
	RelatedTransactionId: "related_transaction_id",
	RelatedEntityId:      "related_entity_id",
	RelatedEntityType:    "related_entity_type",
	Status:               "status",
	Memo:                 "memo",
	CreatedAt:            "created_at",
	UpdatedAt:            "updated_at",
	DeletedAt:            "deleted_at",
	Symbol:               "symbol",
	BusinessId:           "business_id",
	RequestAmount:        "request_amount",
	RequestReference:     "request_reference",
	RequestMetadata:      "request_metadata",
	RequestSource:        "request_source",
	RequestIp:            "request_ip",
	RequestUserAgent:     "request_user_agent",
	RequestTimestamp:     "request_timestamp",
	ProcessedAt:          "processed_at",
	FeeAmount:            "fee_amount",
	FeeType:              "fee_type",
	ExchangeRate:         "exchange_rate",
	TargetUserId:         "target_user_id",
	TargetUsername:       "target_username",
	DtmGid:               "dtm_gid",
	DtmBranchId:          "dtm_branch_id",
}

// NewMerchantTransactionsDao creates and returns a new DAO object for table data access.
func NewMerchantTransactionsDao(handlers ...gdb.ModelHandler) *MerchantTransactionsDao {
	return &MerchantTransactionsDao{
		group:    "default",
		table:    "merchant_transactions",
		columns:  merchantTransactionsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *MerchantTransactionsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *MerchantTransactionsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *MerchantTransactionsDao) Columns() MerchantTransactionsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *MerchantTransactionsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *MerchantTransactionsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *MerchantTransactionsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
