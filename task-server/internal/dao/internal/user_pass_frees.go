// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// UserPassFreesDao is the data access object for the table user_pass_frees.
type UserPassFreesDao struct {
	table    string               // table is the underlying table name of the DAO.
	group    string               // group is the database configuration group name of the current DAO.
	columns  UserPassFreesColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler   // handlers for customized model modification.
}

// UserPassFreesColumns defines and stores column names for the table user_pass_frees.
type UserPassFreesColumns struct {
	UserPassFreesId string // 主键ID
	UserId          string // 用户ID (Foreign key to users table recommended)
	Symbol          string // 币种名称
	Amount          string // 免密金额
	CreatedAt       string // 创建时间
	UpdatedAt       string // 最后更新时间
}

// userPassFreesColumns holds the columns for the table user_pass_frees.
var userPassFreesColumns = UserPassFreesColumns{
	UserPassFreesId: "user_pass_frees_id",
	UserId:          "user_id",
	Symbol:          "symbol",
	Amount:          "amount",
	CreatedAt:       "created_at",
	UpdatedAt:       "updated_at",
}

// NewUserPassFreesDao creates and returns a new DAO object for table data access.
func NewUserPassFreesDao(handlers ...gdb.ModelHandler) *UserPassFreesDao {
	return &UserPassFreesDao{
		group:    "default",
		table:    "user_pass_frees",
		columns:  userPassFreesColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *UserPassFreesDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *UserPassFreesDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *UserPassFreesDao) Columns() UserPassFreesColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *UserPassFreesDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *UserPassFreesDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *UserPassFreesDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
