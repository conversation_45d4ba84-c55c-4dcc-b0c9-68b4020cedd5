// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdminPermissionsDao is the data access object for the table admin_permissions.
type AdminPermissionsDao struct {
	table    string                  // table is the underlying table name of the DAO.
	group    string                  // group is the database configuration group name of the current DAO.
	columns  AdminPermissionsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler      // handlers for customized model modification.
}

// AdminPermissionsColumns defines and stores column names for the table admin_permissions.
type AdminPermissionsColumns struct {
	Id        string //
	Name      string // 权限名称 (给人看，例如：保存订单按钮)
	Pid       string // 父权限ID
	Key       string // 权限标识符 (给 Casbin 用，例如：page:order:btn_save)
	Type      string // 权限类型 (例如: menu, api, button)
	ParentKey string // 父权限标识符 (用于分组展示)
	Remark    string // 备注
	Status    string // 角色状态
	CreatedAt string // 创建时间
	UpdatedAt string // 更新时间
	DeletedAt string // 软删除的时间戳
	Level     string // 关系树等级
	Tree      string // 关系树
	Sort      string // 排序
}

// adminPermissionsColumns holds the columns for the table admin_permissions.
var adminPermissionsColumns = AdminPermissionsColumns{
	Id:        "id",
	Name:      "name",
	Pid:       "pid",
	Key:       "key",
	Type:      "type",
	ParentKey: "parent_key",
	Remark:    "remark",
	Status:    "status",
	CreatedAt: "created_at",
	UpdatedAt: "updated_at",
	DeletedAt: "deleted_at",
	Level:     "level",
	Tree:      "tree",
	Sort:      "sort",
}

// NewAdminPermissionsDao creates and returns a new DAO object for table data access.
func NewAdminPermissionsDao(handlers ...gdb.ModelHandler) *AdminPermissionsDao {
	return &AdminPermissionsDao{
		group:    "default",
		table:    "admin_permissions",
		columns:  adminPermissionsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AdminPermissionsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AdminPermissionsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AdminPermissionsDao) Columns() AdminPermissionsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AdminPermissionsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AdminPermissionsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AdminPermissionsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
