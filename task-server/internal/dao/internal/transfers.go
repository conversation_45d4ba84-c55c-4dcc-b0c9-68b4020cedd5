// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// TransfersDao is the data access object for the table transfers.
type TransfersDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  TransfersColumns   // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// TransfersColumns defines and stores column names for the table transfers.
type TransfersColumns struct {
	TransferId            string // 转账记录 ID (主键)
	MessageId             string // 转账记录 ID (主键)
	ChatId                string // 转账记录 ID (主键)
	SenderUserId          string // 发送方用户 ID (外键, 指向 users.user_id)
	ReceiverUserId        string // 接收方用户 ID (外键, 指向 users.user_id)
	TokenId               string // 代币 ID (外键, 指向 tokens.token_id)
	Amount                string // 转账金额 (最小单位)
	SenderTransactionId   string // 关联的发送方资金扣除交易 ID (外键, 指向 transactions.transaction_id)
	ReceiverTransactionId string // 关联的接收方资金增加交易 ID (外键, 指向 transactions.transaction_id)
	Memo                  string // 转账备注
	Status                string // 状态 (pending_pass, pending_collection, completed, expired)
	HoldId                string // 钱包服务返回的冻结 ID
	CreatedAt             string // 转账发起时间 (记录创建时间)
	ExpiresAt             string // 过期时间 (created_at + 24 小时)
	UpdatedAt             string // 最后更新时间
	DeletedAt             string // 软删除时间
	NeedPass              string // 是否需要支付密码
	Key                   string //
	Symbol                string //
	Message               string //
	InlineMessageId       string // 内联消息 ID，用于后续编辑
	SenderUsername        string // 发送方用户名
	ReceiverUsername      string // 接收方用户名
	NotificationSent      string // 是否已发送通知: 0-未发送, 1-已发送
	NotificationSentAt    string // 通知发送时间
}

// transfersColumns holds the columns for the table transfers.
var transfersColumns = TransfersColumns{
	TransferId:            "transfer_id",
	MessageId:             "message_id",
	ChatId:                "chat_id",
	SenderUserId:          "sender_user_id",
	ReceiverUserId:        "receiver_user_id",
	TokenId:               "token_id",
	Amount:                "amount",
	SenderTransactionId:   "sender_transaction_id",
	ReceiverTransactionId: "receiver_transaction_id",
	Memo:                  "memo",
	Status:                "status",
	HoldId:                "hold_id",
	CreatedAt:             "created_at",
	ExpiresAt:             "expires_at",
	UpdatedAt:             "updated_at",
	DeletedAt:             "deleted_at",
	NeedPass:              "need_pass",
	Key:                   "key",
	Symbol:                "symbol",
	Message:               "message",
	InlineMessageId:       "inline_message_id",
	SenderUsername:        "sender_username",
	ReceiverUsername:      "receiver_username",
	NotificationSent:      "notification_sent",
	NotificationSentAt:    "notification_sent_at",
}

// NewTransfersDao creates and returns a new DAO object for table data access.
func NewTransfersDao(handlers ...gdb.ModelHandler) *TransfersDao {
	return &TransfersDao{
		group:    "default",
		table:    "transfers",
		columns:  transfersColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *TransfersDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *TransfersDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *TransfersDao) Columns() TransfersColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *TransfersDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *TransfersDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *TransfersDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
