// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// FiatCurrenciesDao is the data access object for the table fiat_currencies.
type FiatCurrenciesDao struct {
	table    string                // table is the underlying table name of the DAO.
	group    string                // group is the database configuration group name of the current DAO.
	columns  FiatCurrenciesColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler    // handlers for customized model modification.
}

// FiatCurrenciesColumns defines and stores column names for the table fiat_currencies.
type FiatCurrenciesColumns struct {
	FiatId        string // 法币内部 ID (主键)
	Code          string // 法币代码 (ISO 4217 标准, 例如: USD, CNY, EUR)
	Name          string // 法币名称 (例如: US Dollar, Chinese Yuan, Euro)
	Symbol        string // 法币符号 (例如: $, ¥, €)
	Decimals      string // 法币精度 (小数位数, 大多数为 2)
	CountryCode   string // 主要关联国家/地区代码 (ISO 3166-1 alpha-2, 例如: US, CN, EU - EU 非标准但常用)
	Status        string // 法币状态: active-可用, inactive-禁用
	AllowDeposit  string // 是否允许通过此法币充值 (例如银行转账)
	AllowWithdraw string // 是否允许提现到此法币 (例如银行转账)
	DisplayOrder  string // 显示排序 (数字越小越靠前)
	CreatedAt     string // 创建时间
	UpdatedAt     string // 最后更新时间
	DeletedAt     string // 软删除时间
}

// fiatCurrenciesColumns holds the columns for the table fiat_currencies.
var fiatCurrenciesColumns = FiatCurrenciesColumns{
	FiatId:        "fiat_id",
	Code:          "code",
	Name:          "name",
	Symbol:        "symbol",
	Decimals:      "decimals",
	CountryCode:   "country_code",
	Status:        "status",
	AllowDeposit:  "allow_deposit",
	AllowWithdraw: "allow_withdraw",
	DisplayOrder:  "display_order",
	CreatedAt:     "created_at",
	UpdatedAt:     "updated_at",
	DeletedAt:     "deleted_at",
}

// NewFiatCurrenciesDao creates and returns a new DAO object for table data access.
func NewFiatCurrenciesDao(handlers ...gdb.ModelHandler) *FiatCurrenciesDao {
	return &FiatCurrenciesDao{
		group:    "default",
		table:    "fiat_currencies",
		columns:  fiatCurrenciesColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *FiatCurrenciesDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *FiatCurrenciesDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *FiatCurrenciesDao) Columns() FiatCurrenciesColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *FiatCurrenciesDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *FiatCurrenciesDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *FiatCurrenciesDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
