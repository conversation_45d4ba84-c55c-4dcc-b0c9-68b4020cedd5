// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// UserWithdrawsDao is the data access object for the table user_withdraws.
type UserWithdrawsDao struct {
	table    string               // table is the underlying table name of the DAO.
	group    string               // group is the database configuration group name of the current DAO.
	columns  UserWithdrawsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler   // handlers for customized model modification.
}

// UserWithdrawsColumns defines and stores column names for the table user_withdraws.
type UserWithdrawsColumns struct {
	UserWithdrawsId        string // 主键ID
	UserId                 string // 用户ID (Foreign key to users table recommended)
	TokenId                string // 币种ID
	WalletId               string // 用户钱包ID
	Name                   string // 币种ID
	Chan                   string //
	OrderNo                string // 提现订单号 (Should be unique)
	Address                string // 提币目标地址
	RecipientName          string // 法币收款人姓名
	RecipientAccount       string // 法币收款账户
	RecipientQrcode        string // 法币收款账户
	Amount                 string // 申请提现金额
	HandlingFee            string // 提现手续费
	ActualAmount           string // 实际到账金额
	AuditStatus            string // 审核状态: 1-免审, 2-待审核, 3-审核通过, 4-审核拒绝
	AutoWithdrawalProgress string // 自动提现状态 0 未开始 1 进行中 2 成功 3 结束
	ProcessingStatus       string // 提现处理状态: 1-自动放币处理中, 2-处理中(待冷钱包转入)，3.待人工转账.，4-成功, 5-失败
	RefuseReasonZh         string // 拒绝原因 (中文)
	RefuseReasonEn         string // 拒绝原因 (英文)
	TxHash                 string // 链上交易哈希/ID
	ErrorMessage           string // 失败或错误信息
	UserRemark             string // 用户提现备注
	AdminRemark            string // 管理员审核备注
	CreatedAt              string // 创建时间
	CheckedAt              string // 审核时间 (审核通过或拒绝的时间)
	ProcessingAt           string // 开始处理时间 (进入“处理中”状态的时间)
	CompletedAt            string // 完成时间 (变为“已完成”或“失败”状态的时间)
	UpdatedAt              string // 最后更新时间
	Retries                string //
	NergyState             string // 0 表示未发送 1 发送未确认 2 已经确认有能量可以使用了
	NotificationSent       string // 是否已发送通知: 0-未发送, 1-已发送
	NotificationSentAt     string // 通知发送时间
	FiatType               string // 法币提现类型 alipay_account 支付宝账号  alipay_qr 支付宝二维码  wechat_qr 微信二维码
}

// userWithdrawsColumns holds the columns for the table user_withdraws.
var userWithdrawsColumns = UserWithdrawsColumns{
	UserWithdrawsId:        "user_withdraws_id",
	UserId:                 "user_id",
	TokenId:                "token_id",
	WalletId:               "wallet_id",
	Name:                   "name",
	Chan:                   "chan",
	OrderNo:                "order_no",
	Address:                "address",
	RecipientName:          "recipient_name",
	RecipientAccount:       "recipient_account",
	RecipientQrcode:        "recipient_qrcode",
	Amount:                 "amount",
	HandlingFee:            "handling_fee",
	ActualAmount:           "actual_amount",
	AuditStatus:            "audit_status",
	AutoWithdrawalProgress: "auto_withdrawal_progress",
	ProcessingStatus:       "processing_status",
	RefuseReasonZh:         "refuse_reason_zh",
	RefuseReasonEn:         "refuse_reason_en",
	TxHash:                 "tx_hash",
	ErrorMessage:           "error_message",
	UserRemark:             "user_remark",
	AdminRemark:            "admin_remark",
	CreatedAt:              "created_at",
	CheckedAt:              "checked_at",
	ProcessingAt:           "processing_at",
	CompletedAt:            "completed_at",
	UpdatedAt:              "updated_at",
	Retries:                "retries",
	NergyState:             "nergy_state",
	NotificationSent:       "notification_sent",
	NotificationSentAt:     "notification_sent_at",
	FiatType:               "fiat_type",
}

// NewUserWithdrawsDao creates and returns a new DAO object for table data access.
func NewUserWithdrawsDao(handlers ...gdb.ModelHandler) *UserWithdrawsDao {
	return &UserWithdrawsDao{
		group:    "default",
		table:    "user_withdraws",
		columns:  userWithdrawsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *UserWithdrawsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *UserWithdrawsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *UserWithdrawsDao) Columns() UserWithdrawsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *UserWithdrawsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *UserWithdrawsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *UserWithdrawsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
