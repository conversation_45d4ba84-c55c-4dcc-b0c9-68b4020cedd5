// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// RemoteOperationIntentsDao is the data access object for the table remote_operation_intents.
type RemoteOperationIntentsDao struct {
	table    string                        // table is the underlying table name of the DAO.
	group    string                        // group is the database configuration group name of the current DAO.
	columns  RemoteOperationIntentsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler            // handlers for customized model modification.
}

// RemoteOperationIntentsColumns defines and stores column names for the table remote_operation_intents.
type RemoteOperationIntentsColumns struct {
	Id            string // 主键ID
	TransactionId string // 关联的交易记录ID
	UserId        string // 用户ID
	TokenSymbol   string // 代币符号
	OperationType string // 操作类型: credit, debit
	Amount        string // 操作金额
	NewBalance    string // 操作后的新余额
	BusinessId    string // 业务ID
	Metadata      string // 元数据JSON
	Status        string // 状态: pending, processing, completed, failed
	RetryCount    string // 重试次数
	LastError     string // 最后一次错误信息
	CreatedAt     string // 创建时间
	UpdatedAt     string // 更新时间
	ProcessedAt   string // 处理完成时间
}

// remoteOperationIntentsColumns holds the columns for the table remote_operation_intents.
var remoteOperationIntentsColumns = RemoteOperationIntentsColumns{
	Id:            "id",
	TransactionId: "transaction_id",
	UserId:        "user_id",
	TokenSymbol:   "token_symbol",
	OperationType: "operation_type",
	Amount:        "amount",
	NewBalance:    "new_balance",
	BusinessId:    "business_id",
	Metadata:      "metadata",
	Status:        "status",
	RetryCount:    "retry_count",
	LastError:     "last_error",
	CreatedAt:     "created_at",
	UpdatedAt:     "updated_at",
	ProcessedAt:   "processed_at",
}

// NewRemoteOperationIntentsDao creates and returns a new DAO object for table data access.
func NewRemoteOperationIntentsDao(handlers ...gdb.ModelHandler) *RemoteOperationIntentsDao {
	return &RemoteOperationIntentsDao{
		group:    "default",
		table:    "remote_operation_intents",
		columns:  remoteOperationIntentsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *RemoteOperationIntentsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *RemoteOperationIntentsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *RemoteOperationIntentsDao) Columns() RemoteOperationIntentsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *RemoteOperationIntentsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *RemoteOperationIntentsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *RemoteOperationIntentsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
