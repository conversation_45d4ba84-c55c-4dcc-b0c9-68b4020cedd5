// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// WithdrawalFeeSettingsDao is the data access object for the table withdrawal_fee_settings.
type WithdrawalFeeSettingsDao struct {
	table    string                       // table is the underlying table name of the DAO.
	group    string                       // group is the database configuration group name of the current DAO.
	columns  WithdrawalFeeSettingsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler           // handlers for customized model modification.
}

// WithdrawalFeeSettingsColumns defines and stores column names for the table withdrawal_fee_settings.
type WithdrawalFeeSettingsColumns struct {
	Id        string //
	Currency  string // 币种符号 (如 USDT, BTC)
	Network   string // 网络类型 (如 TRC20, ERC20)
	AmountMin string // 单笔提现金额范围最小值
	AmountMax string // 单笔提现金额范围最大值
	FeeType   string // 手续费类型: fixed-固定金额, percent-百分比
	FeeValue  string // 手续费值 (固定金额或百分比)
	Status    string // 状态: 1-启用, 0-禁用
	CreatedAt string // 创建时间
	UpdatedAt string // 更新时间
	DeletedAt string // 软删除时间
}

// withdrawalFeeSettingsColumns holds the columns for the table withdrawal_fee_settings.
var withdrawalFeeSettingsColumns = WithdrawalFeeSettingsColumns{
	Id:        "id",
	Currency:  "currency",
	Network:   "network",
	AmountMin: "amount_min",
	AmountMax: "amount_max",
	FeeType:   "fee_type",
	FeeValue:  "fee_value",
	Status:    "status",
	CreatedAt: "created_at",
	UpdatedAt: "updated_at",
	DeletedAt: "deleted_at",
}

// NewWithdrawalFeeSettingsDao creates and returns a new DAO object for table data access.
func NewWithdrawalFeeSettingsDao(handlers ...gdb.ModelHandler) *WithdrawalFeeSettingsDao {
	return &WithdrawalFeeSettingsDao{
		group:    "default",
		table:    "withdrawal_fee_settings",
		columns:  withdrawalFeeSettingsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *WithdrawalFeeSettingsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *WithdrawalFeeSettingsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *WithdrawalFeeSettingsDao) Columns() WithdrawalFeeSettingsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *WithdrawalFeeSettingsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *WithdrawalFeeSettingsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *WithdrawalFeeSettingsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
