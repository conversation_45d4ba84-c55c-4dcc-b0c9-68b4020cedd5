// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// MerchantCallbackNotificationsDao is the data access object for the table merchant_callback_notifications.
type MerchantCallbackNotificationsDao struct {
	table    string                               // table is the underlying table name of the DAO.
	group    string                               // group is the database configuration group name of the current DAO.
	columns  MerchantCallbackNotificationsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler                   // handlers for customized model modification.
}

// MerchantCallbackNotificationsColumns defines and stores column names for the table merchant_callback_notifications.
type MerchantCallbackNotificationsColumns struct {
	Id             string // 主键ID
	MerchantId     string // 商户ID
	EventType      string // 事件类型
	EventId        string // 事件ID（deposit_id 或 withdraw_id）
	OrderNo        string // 订单号
	CallbackUrl    string // 回调URL
	RequestBody    string // 请求体JSON
	RequestHeaders string // 请求头信息
	ResponseStatus string // HTTP响应状态码
	ResponseBody   string // 响应体内容
	ResponseTimeMs string // 响应时间（毫秒）
	RetryCount     string // 重试次数
	MaxRetries     string // 最大重试次数
	NextRetryAt    string // 下次重试时间
	Status         string // 回调状态
	ErrorMessage   string // 错误信息
	CreatedAt      string // 创建时间
	UpdatedAt      string // 更新时间
}

// merchantCallbackNotificationsColumns holds the columns for the table merchant_callback_notifications.
var merchantCallbackNotificationsColumns = MerchantCallbackNotificationsColumns{
	Id:             "id",
	MerchantId:     "merchant_id",
	EventType:      "event_type",
	EventId:        "event_id",
	OrderNo:        "order_no",
	CallbackUrl:    "callback_url",
	RequestBody:    "request_body",
	RequestHeaders: "request_headers",
	ResponseStatus: "response_status",
	ResponseBody:   "response_body",
	ResponseTimeMs: "response_time_ms",
	RetryCount:     "retry_count",
	MaxRetries:     "max_retries",
	NextRetryAt:    "next_retry_at",
	Status:         "status",
	ErrorMessage:   "error_message",
	CreatedAt:      "created_at",
	UpdatedAt:      "updated_at",
}

// NewMerchantCallbackNotificationsDao creates and returns a new DAO object for table data access.
func NewMerchantCallbackNotificationsDao(handlers ...gdb.ModelHandler) *MerchantCallbackNotificationsDao {
	return &MerchantCallbackNotificationsDao{
		group:    "default",
		table:    "merchant_callback_notifications",
		columns:  merchantCallbackNotificationsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *MerchantCallbackNotificationsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *MerchantCallbackNotificationsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *MerchantCallbackNotificationsDao) Columns() MerchantCallbackNotificationsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *MerchantCallbackNotificationsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *MerchantCallbackNotificationsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *MerchantCallbackNotificationsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
