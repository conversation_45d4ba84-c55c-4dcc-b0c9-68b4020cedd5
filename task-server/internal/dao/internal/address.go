// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AddressDao is the data access object for the table address.
type AddressDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  AddressColumns     // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// AddressColumns defines and stores column names for the table address.
type AddressColumns struct {
	Id         string //
	Chan       string // 链
	Address    string // 地址
	Image      string // 二维码
	CreatedAt  string //
	UpdatedAt  string //
	BindStatus string // 绑定状态 0: 未绑定, 1: 已绑定
}

// addressColumns holds the columns for the table address.
var addressColumns = AddressColumns{
	Id:         "id",
	Chan:       "chan",
	Address:    "address",
	Image:      "image",
	CreatedAt:  "created_at",
	UpdatedAt:  "updated_at",
	BindStatus: "bind_status",
}

// NewAddressDao creates and returns a new DAO object for table data access.
func NewAddressDao(handlers ...gdb.ModelHandler) *AddressDao {
	return &AddressDao{
		group:    "default",
		table:    "address",
		columns:  addressColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AddressDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AddressDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AddressDao) Columns() AddressColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AddressDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AddressDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AddressDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
