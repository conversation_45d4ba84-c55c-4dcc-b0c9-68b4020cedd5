// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// WithdrawalAmountSettingsDao is the data access object for the table withdrawal_amount_settings.
type WithdrawalAmountSettingsDao struct {
	table    string                          // table is the underlying table name of the DAO.
	group    string                          // group is the database configuration group name of the current DAO.
	columns  WithdrawalAmountSettingsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler              // handlers for customized model modification.
}

// WithdrawalAmountSettingsColumns defines and stores column names for the table withdrawal_amount_settings.
type WithdrawalAmountSettingsColumns struct {
	Id        string //
	Currency  string // 币种符号 (如 USDT, BTC)
	Network   string // 网络类型 (如 TRC20, ERC20)
	MinAmount string // 单笔最小提现金额
	MaxAmount string // 单笔最大提现金额
	Status    string // 状态: 1-启用, 0-禁用
	CreatedAt string // 创建时间
	UpdatedAt string // 更新时间
	DeletedAt string // 软删除时间
}

// withdrawalAmountSettingsColumns holds the columns for the table withdrawal_amount_settings.
var withdrawalAmountSettingsColumns = WithdrawalAmountSettingsColumns{
	Id:        "id",
	Currency:  "currency",
	Network:   "network",
	MinAmount: "min_amount",
	MaxAmount: "max_amount",
	Status:    "status",
	CreatedAt: "created_at",
	UpdatedAt: "updated_at",
	DeletedAt: "deleted_at",
}

// NewWithdrawalAmountSettingsDao creates and returns a new DAO object for table data access.
func NewWithdrawalAmountSettingsDao(handlers ...gdb.ModelHandler) *WithdrawalAmountSettingsDao {
	return &WithdrawalAmountSettingsDao{
		group:    "default",
		table:    "withdrawal_amount_settings",
		columns:  withdrawalAmountSettingsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *WithdrawalAmountSettingsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *WithdrawalAmountSettingsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *WithdrawalAmountSettingsDao) Columns() WithdrawalAmountSettingsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *WithdrawalAmountSettingsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *WithdrawalAmountSettingsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *WithdrawalAmountSettingsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
