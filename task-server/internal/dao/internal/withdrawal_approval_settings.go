// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// WithdrawalApprovalSettingsDao is the data access object for the table withdrawal_approval_settings.
type WithdrawalApprovalSettingsDao struct {
	table    string                            // table is the underlying table name of the DAO.
	group    string                            // group is the database configuration group name of the current DAO.
	columns  WithdrawalApprovalSettingsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler                // handlers for customized model modification.
}

// WithdrawalApprovalSettingsColumns defines and stores column names for the table withdrawal_approval_settings.
type WithdrawalApprovalSettingsColumns struct {
	Id                string //
	Currency          string // 币种符号 (如 USDT, BTC)
	Network           string // 网络类型 (如 TRC20, ERC20)
	AutoReleaseMin    string // 无需审核自动放币最小金额
	AutoReleaseMax    string // 无需审核自动放币最大金额
	ApprovalAutoMin   string // 审核确定后自动放币最小金额
	ApprovalAutoMax   string // 审核确定后自动放币最大金额
	ApprovalManualMin string // 审核确定后手动放币最小金额
	ApprovalManualMax string // 审核确定后手动放币最大金额
	Status            string // 状态: 1-启用, 0-禁用
	CreatedAt         string // 创建时间
	UpdatedAt         string // 更新时间
	DeletedAt         string // 软删除时间
}

// withdrawalApprovalSettingsColumns holds the columns for the table withdrawal_approval_settings.
var withdrawalApprovalSettingsColumns = WithdrawalApprovalSettingsColumns{
	Id:                "id",
	Currency:          "currency",
	Network:           "network",
	AutoReleaseMin:    "auto_release_min",
	AutoReleaseMax:    "auto_release_max",
	ApprovalAutoMin:   "approval_auto_min",
	ApprovalAutoMax:   "approval_auto_max",
	ApprovalManualMin: "approval_manual_min",
	ApprovalManualMax: "approval_manual_max",
	Status:            "status",
	CreatedAt:         "created_at",
	UpdatedAt:         "updated_at",
	DeletedAt:         "deleted_at",
}

// NewWithdrawalApprovalSettingsDao creates and returns a new DAO object for table data access.
func NewWithdrawalApprovalSettingsDao(handlers ...gdb.ModelHandler) *WithdrawalApprovalSettingsDao {
	return &WithdrawalApprovalSettingsDao{
		group:    "default",
		table:    "withdrawal_approval_settings",
		columns:  withdrawalApprovalSettingsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *WithdrawalApprovalSettingsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *WithdrawalApprovalSettingsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *WithdrawalApprovalSettingsDao) Columns() WithdrawalApprovalSettingsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *WithdrawalApprovalSettingsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *WithdrawalApprovalSettingsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *WithdrawalApprovalSettingsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
