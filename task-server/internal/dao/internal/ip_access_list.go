// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// IpAccessListDao is the data access object for the table ip_access_list.
type IpAccessListDao struct {
	table    string              // table is the underlying table name of the DAO.
	group    string              // group is the database configuration group name of the current DAO.
	columns  IpAccessListColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler  // handlers for customized model modification.
}

// IpAccessListColumns defines and stores column names for the table ip_access_list.
type IpAccessListColumns struct {
	Id          string // 记录ID
	UserId      string // 用户ID (关联用户表)
	AgentId     string // 代理ID (关联代理表)
	AdminId     string // 管理员ID (关联管理员表)
	MerchantId  string // 管理员ID (关联管理员表)
	ListType    string // 列表类型 (blacklist-黑名单, whitelist-白名单)
	UseType     string // 适用类型
	Description string // 规则描述 (可选, 如添加原因或来源)
	IpAddress   string // IP地址 (支持IPv4和IPv6)
	Reason      string // 原因 (拉黑或加白)
	AddedBy     string // 操作者 (用户名或系统标识)
	ExpiresAt   string // 过期时间 (NULL表示永久)
	IsEnabled   string // 是否启用 (1-启用, 0-禁用)
	CreatedAt   string // 创建时间
	UpdatedAt   string // 修改时间
	DeletedAt   string // 软删除的时间戳
}

// ipAccessListColumns holds the columns for the table ip_access_list.
var ipAccessListColumns = IpAccessListColumns{
	Id:          "id",
	UserId:      "user_id",
	AgentId:     "agent_id",
	AdminId:     "admin_id",
	MerchantId:  "merchant_id",
	ListType:    "list_type",
	UseType:     "use_type",
	Description: "description",
	IpAddress:   "ip_address",
	Reason:      "reason",
	AddedBy:     "added_by",
	ExpiresAt:   "expires_at",
	IsEnabled:   "is_enabled",
	CreatedAt:   "created_at",
	UpdatedAt:   "updated_at",
	DeletedAt:   "deleted_at",
}

// NewIpAccessListDao creates and returns a new DAO object for table data access.
func NewIpAccessListDao(handlers ...gdb.ModelHandler) *IpAccessListDao {
	return &IpAccessListDao{
		group:    "default",
		table:    "ip_access_list",
		columns:  ipAccessListColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *IpAccessListDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *IpAccessListDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *IpAccessListDao) Columns() IpAccessListColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *IpAccessListDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *IpAccessListDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *IpAccessListDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
