// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// TccTransactionsDao is the data access object for the table tcc_transactions.
type TccTransactionsDao struct {
	table    string                 // table is the underlying table name of the DAO.
	group    string                 // group is the database configuration group name of the current DAO.
	columns  TccTransactionsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler     // handlers for customized model modification.
}

// TccTransactionsColumns defines and stores column names for the table tcc_transactions.
type TccTransactionsColumns struct {
	Id         string //
	Gid        string // 全局事务ID
	BranchId   string // 分支ID
	BusinessId string // 业务ID(幂等性)
	Operation  string // 操作类型: credit/debit/transfer
	Status     string // 0:待处理, 1:尝试中, 2:已确认, 3:已取消
	TryData    string // Try阶段序列化数据
	CreatedAt  string //
	UpdatedAt  string //
}

// tccTransactionsColumns holds the columns for the table tcc_transactions.
var tccTransactionsColumns = TccTransactionsColumns{
	Id:         "id",
	Gid:        "gid",
	BranchId:   "branch_id",
	BusinessId: "business_id",
	Operation:  "operation",
	Status:     "status",
	TryData:    "try_data",
	CreatedAt:  "created_at",
	UpdatedAt:  "updated_at",
}

// NewTccTransactionsDao creates and returns a new DAO object for table data access.
func NewTccTransactionsDao(handlers ...gdb.ModelHandler) *TccTransactionsDao {
	return &TccTransactionsDao{
		group:    "default",
		table:    "tcc_transactions",
		columns:  tccTransactionsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *TccTransactionsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *TccTransactionsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *TccTransactionsDao) Columns() TccTransactionsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *TccTransactionsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *TccTransactionsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *TccTransactionsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
