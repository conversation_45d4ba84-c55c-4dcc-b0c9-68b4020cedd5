// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"task-api/internal/dao/internal"
)

// redPacketsInforsDao is the data access object for the table red_packets_infors.
// You can define custom methods on it to extend its functionality as needed.
type redPacketsInforsDao struct {
	*internal.RedPacketsInforsDao
}

var (
	// RedPacketsInfors is a globally accessible object for table red_packets_infors operations.
	RedPacketsInfors = redPacketsInforsDao{internal.NewRedPacketsInforsDao()}
)

// Add your custom methods and functionality below.
