// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"task-api/internal/dao/internal"
)

// agentMenuDao is the data access object for the table agent_menu.
// You can define custom methods on it to extend its functionality as needed.
type agentMenuDao struct {
	*internal.AgentMenuDao
}

var (
	// AgentMenu is a globally accessible object for table agent_menu operations.
	AgentMenu = agentMenuDao{internal.NewAgentMenuDao()}
)

// Add your custom methods and functionality below.
