// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"task-api/internal/dao/internal"
)

// withdrawalFeeSettingsDao is the data access object for the table withdrawal_fee_settings.
// You can define custom methods on it to extend its functionality as needed.
type withdrawalFeeSettingsDao struct {
	*internal.WithdrawalFeeSettingsDao
}

var (
	// WithdrawalFeeSettings is a globally accessible object for table withdrawal_fee_settings operations.
	WithdrawalFeeSettings = withdrawalFeeSettingsDao{internal.NewWithdrawalFeeSettingsDao()}
)

// Add your custom methods and functionality below.
