// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"task-api/internal/dao/internal"
)

// userRechargesDao is the data access object for the table user_recharges.
// You can define custom methods on it to extend its functionality as needed.
type userRechargesDao struct {
	*internal.UserRechargesDao
}

var (
	// UserRecharges is a globally accessible object for table user_recharges operations.
	UserRecharges = userRechargesDao{internal.NewUserRechargesDao()}
)

// Add your custom methods and functionality below.
