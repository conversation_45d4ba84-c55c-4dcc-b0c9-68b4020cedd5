// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"context"
	"task-api/internal/dao/internal"
	"task-api/internal/model/entity"
)

// userAddressDao is the data access object for the table user_address.
// You can define custom methods on it to extend its functionality as needed.
type userAddressDao struct {
	*internal.UserAddressDao
}

var (
	// UserAddress is a globally accessible object for table user_address operations.
	UserAddress = userAddressDao{internal.NewUserAddressDao()}
)

// Add your custom methods and functionality below.

// Database Index Recommendations:
// For optimal performance of address queries, the following indexes are recommended:
//
// 1. Index on 'address' field for unique address lookups:
//    CREATE INDEX idx_user_address_address ON user_address(address);
//
// 2. Composite index on 'chan' and 'address' for chain-specific address lookups:
//    CREATE INDEX idx_user_address_chan_address ON user_address(chan, address);
//
// 3. Index on 'chan' field for chain-specific address fetching:
//    CREATE INDEX idx_user_address_chan ON user_address(chan);
//
// 4. Index on 'type' field for filtering deposit/withdrawal addresses:
//    CREATE INDEX idx_user_address_type ON user_address(type);
//
// 5. Composite index for deposit address queries:
//    CREATE INDEX idx_user_address_chan_type ON user_address(chan, type);
//
// These indexes will significantly improve the performance of GetActiveDepositAddresses
// and address-based queries, especially when dealing with large datasets.

// GetActiveDepositAddresses 获取指定链的活跃充值地址
func (dao *userAddressDao) GetActiveDepositAddresses(ctx context.Context, chainName string) ([]*entity.UserAddress, error) {
	var addresses []*entity.UserAddress
	err := dao.Ctx(ctx).
		Where(dao.Columns().Chan, chainName).
		Where(dao.Columns().Type, "deposit"). // 只获取充值类型的地址
		Scan(&addresses)
	return addresses, err
}

// GetActiveDepositAddressesWithDistinct 获取指定链的活跃充值地址（去重）
func (dao *userAddressDao) GetActiveDepositAddressesWithDistinct(ctx context.Context, chainName string) ([]*entity.UserAddress, error) {
	var addresses []*entity.UserAddress
	err := dao.Ctx(ctx).
		Fields("DISTINCT address, user_id, token_id, chan, created_at").
		Where(dao.Columns().Chan, chainName).
		Where(dao.Columns().Type, "deposit").
		Scan(&addresses)
	return addresses, err
}

// GetUniqueDepositAddress 根据地址字段唯一查询用户地址
func (dao *userAddressDao) GetUniqueDepositAddress(ctx context.Context, address string) (*entity.UserAddress, error) {
	var userAddress *entity.UserAddress
	err := dao.Ctx(ctx).
		Where(dao.Columns().Address, address).
		Scan(&userAddress)
	return userAddress, err
}

// GetUniqueDepositAddressByChainAndAddress 根据链和地址唯一查询用户地址
func (dao *userAddressDao) GetUniqueDepositAddressByChainAndAddress(ctx context.Context, chainName, address string) (*entity.UserAddress, error) {
	var userAddress *entity.UserAddress
	err := dao.Ctx(ctx).
		Where(dao.Columns().Chan, chainName).
		Where(dao.Columns().Address, address).
		Scan(&userAddress)
	return userAddress, err
}

// BatchGetDepositAddressesByAddresses 批量根据地址列表查询用户地址
func (dao *userAddressDao) BatchGetDepositAddressesByAddresses(ctx context.Context, addresses []string) ([]*entity.UserAddress, error) {
	if len(addresses) == 0 {
		return []*entity.UserAddress{}, nil
	}
	
	var userAddresses []*entity.UserAddress
	err := dao.Ctx(ctx).
		WhereIn(dao.Columns().Address, addresses).
		Scan(&userAddresses)
	return userAddresses, err
}
