package task

import (
	"context"
	"encoding/json"
	"task-api/internal/dao"
	"task-api/internal/model/entity"
	"task-api/internal/service"

	"github.com/gogf/gf/v2/frame/g"
)

// sTask 实现了 ITask 接口
// 注意：这个结构体现在是 task 包内部的实现细节。
// 如果其他包需要引用 Task Service 的实例，应该通过 service.ITask 接口。
type sTask struct{}

func init() {
	service.RegisterTask(NewTask())
}

// NewTask 创建并返回 Task 服务的新实例
func NewTask() service.ITask {
	return &sTask{}
}

// ListUserWithdraws 获取用户提现记录列表（带分页和筛选）
func (s *sTask) ListUserWithdraws(ctx context.Context, page, pageSize int, filter map[string]interface{}) (list []*entity.UserWithdraws, total int, err error) {
	// 构建查询模型
	m := dao.UserWithdraws.Ctx(ctx)

	// 应用过滤条件
	if len(filter) > 0 {
		for k, v := range filter {
			if v != nil && v != "" && v != 0 {
				m = m.Where(k, v)
			}
		}
	}

	// 获取总数
	total, err = m.Count()
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	// 设置排序和分页
	err = m.Order("created_at DESC").Page(page, pageSize).Scan(&list)

	return list, total, err
}

// UpdateUserWithdrawStatus 更新用户提现记录状态和相关字段
func (s *sTask) UpdateUserWithdrawStatus(ctx context.Context, id uint, status uint, data map[string]interface{}) error {
	// 准备更新数据
	updateData := g.Map{
		"state": status,
	}

	// 添加其他可选字段
	for k, v := range data {
		switch k {
		case "error_message":
			// 检查 error_message 是否为有效的 JSON
			if str, ok := v.(string); ok && str != "" {
				var js json.RawMessage
				if json.Unmarshal([]byte(str), &js) == nil {
					updateData[k] = str
				} else {
					// 如果不是有效的 JSON，则将其转换为 JSON 格式
					jsonBytes, _ := json.Marshal(map[string]string{"message": str})
					updateData[k] = string(jsonBytes)
				}
			}
		case "refuse_reason_zh", "refuse_reason_en", "tx_hash", "admin_remark":
			// 直接添加字符串类型字段
			if str, ok := v.(string); ok && str != "" {
				updateData[k] = str
			}
		case "retries":
			// 添加整数类型字段
			if val, ok := v.(int); ok {
				updateData[k] = val
			}
		}
	}

	// 执行更新
	_, err := dao.UserWithdraws.Ctx(ctx).Where("user_withdraws_id", id).Update(updateData)
	return err
}
