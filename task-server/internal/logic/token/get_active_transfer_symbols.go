package token

import (
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"

	"task-api/internal/dao"
)

// GetActiveTransferSymbols returns a list of unique symbols that are active and allowed for transfer.
func (s *sToken) GetActiveTransferSymbols(ctx context.Context) ([]string, error) {
	symbols := make([]string, 0)
	result, err := dao.Tokens.Ctx(ctx).
		Fields(dao.Tokens.Columns().Symbol).
		Distinct().
		Where(dao.Tokens.Columns().Status, 1).
		Where(dao.Tokens.Columns().AllowTransfer, 1).
		Where(dao.Tokens.Columns().IsActive, 1).
		All()

	if err != nil {
		g.Log().Errorf(ctx, "Failed to query active transfer symbols: %+v", err)
		return nil, gerror.Wrap(err, "查询可转账币种失败")
	}

	if result != nil && !result.IsEmpty() {
		value := result.Array()
		for _, v := range value {
			symbols = append(symbols, v.String())
		}
	}

	g.Log().Debugf(ctx, "Found %d active transfer symbols: %v", len(symbols), symbols)
	return symbols, nil
}
