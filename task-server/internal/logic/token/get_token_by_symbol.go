package token

import (
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"

	"task-api/internal/dao"
	"task-api/internal/model/entity"
)

// GetTokenBySymbol returns the first active token matching the given symbol.
func (s *sToken) GetTokenBySymbol(ctx context.Context, symbol string) (*entity.Tokens, error) {
	var token *entity.Tokens
	err := dao.Tokens.Ctx(ctx).
		Where(dao.Tokens.Columns().Symbol, symbol).
		Where(dao.Tokens.Columns().Status, 1).
		Where(dao.Tokens.Columns().IsActive, 1).
		Scan(&token)

	if err != nil {
		g.Log().Errorf(ctx, "Failed to query token by symbol %s: %v", symbol, err)
		return nil, gerror.Wrapf(err, "查询代币信息失败 (symbol: %s)", symbol)
	}

	if token == nil {
		g.Log().Warningf(ctx, "No active token found for symbol %s", symbol)
		return nil, gerror.Newf("未找到可用的代币 (symbol: %s)", symbol)
	}

	g.<PERSON>g().Debugf(ctx, "Found active token for symbol %s: ID %d, LogoURL: %s", symbol, token.TokenId, token.LogoUrl)
	return token, nil
}
