package token

import (
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"

	"task-api/internal/model/entity"
)

// GetTokenInfo returns token information with withdrawal fee calculations.
func (s *sToken) GetTokenInfo(ctx context.Context, symbol string, chain string) (*entity.Tokens, string, error) {
	token, err := s.GetTokenDetailsForWithdrawal(ctx, symbol, chain) // Call method on the same struct
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get token details for withdrawal (symbol: %s, chain: %s): %v", symbol, chain, err)
		return nil, "", gerror.Wrapf(err, "获取代币信息失败 (symbol: %s, chain: %s)", symbol, chain)
	}

	var fee string
	if token.WithdrawalFeeType == "fixed" {
		fee = token.WithdrawalFeeAmount
	} else if token.WithdrawalFeeType == "percent" {
		fee = token.WithdrawalFeeAmount
		g.Log().Debugf(ctx, "Token %s on %s has percent withdrawal fee: %s%%", symbol, chain, fee)
	} else {
		fee = token.WithdrawalFeeAmount
		g.Log().Warningf(ctx, "Unknown withdrawal fee type for %s on %s: %s, using raw value: %s",
			symbol, chain, token.WithdrawalFeeType, token.WithdrawalFeeAmount)
	}

	g.Log().Debugf(ctx, "Retrieved token info for %s on %s with fee: %s (%s)",
		symbol, chain, fee, token.WithdrawalFeeType)
	return token, fee, nil
}
