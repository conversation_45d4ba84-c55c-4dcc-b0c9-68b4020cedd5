package token

import (
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/shopspring/decimal"
)

// ConvertBalanceToRaw 将用户余额转换为原始值（乘以10^decimals）
func (s *sToken) ConvertBalanceToRaw(ctx context.Context, balance decimal.Decimal, symbol string) (decimal.Decimal, error) {
	tokenInfo, err := s.GetTokenBySymbol(ctx, symbol) // Call method on the same struct
	if err != nil {
		g.Log().Errorf(ctx, "获取代币信息失败 (Symbol: %s): %v", symbol, err)
		return decimal.Zero, gerror.Wrapf(err, "获取代币信息失败 (Symbol: %s)", symbol)
	}
	if tokenInfo == nil {
		g.Log().Errorf(ctx, "未找到代币信息 (Symbol: %s)", symbol)
		return decimal.Zero, gerror.Newf("未找到代币信息 (Symbol: %s)", symbol)
	}

	multiplier := decimal.NewFromInt(10).Pow(decimal.NewFromInt(int64(tokenInfo.Decimals)))
	rawBalance := balance.Mul(multiplier)

	g.Log().Infof(ctx, "余额转换: %s %s => %s 原始值 (乘以10^%d)",
		balance.String(), symbol, rawBalance.String(), tokenInfo.Decimals)

	return rawBalance, nil
}
