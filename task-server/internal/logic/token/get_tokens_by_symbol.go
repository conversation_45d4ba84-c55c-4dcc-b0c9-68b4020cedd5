package token

import (
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"

	"task-api/internal/dao"
	"task-api/internal/model/entity"
)

// GetTokensBySymbol returns all tokens with the given symbol that are active.
func (s *sToken) GetTokensBySymbol(ctx context.Context, symbol string) ([]*entity.Tokens, error) {
	var tokens []*entity.Tokens
	err := dao.Tokens.Ctx(ctx).
		Where(dao.Tokens.Columns().Symbol, symbol).
		Where(dao.Tokens.Columns().Status, 1).
		Where(dao.Tokens.Columns().IsActive, 1).
		Scan(&tokens)

	if err != nil {
		g.Log().Errorf(ctx, "Failed to query tokens by symbol %s: %v", symbol, err)
		return nil, gerror.Wrapf(err, "查询代币信息失败 (symbol: %s)", symbol)
	}

	if len(tokens) == 0 {
		g.Log().Warningf(ctx, "No active tokens found for symbol %s", symbol)
		return nil, gerror.Newf("未找到可用的代币 (symbol: %s)", symbol)
	}

	g.Log().Debugf(ctx, "Found %d tokens for symbol %s", len(tokens), symbol)
	return tokens, nil
}
