package token

import (
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"

	"task-api/internal/dao"
)

// GetActiveWithdrawalSymbols returns a list of symbols that are active for withdrawal.
func (s *sToken) GetActiveWithdrawalSymbols(ctx context.Context) ([]string, error) {
	symbols := make([]string, 0)
	result, err := dao.Tokens.Ctx(ctx).
		Fields(dao.Tokens.Columns().Symbol).
		Distinct().
		Where(dao.Tokens.Columns().Status, 1).
		Where(dao.Tokens.Columns().AllowWithdraw, 1).
		Where(dao.Tokens.Columns().IsActive, 1).
		All()

	if err != nil {
		g.Log().Errorf(ctx, "Failed to query active withdrawal symbols: %+v", err)
		return nil, gerror.Wrap(err, "查询可提现币种失败")
	}

	if result != nil && !result.IsEmpty() {
		value := result.Array()
		for _, v := range value {
			symbols = append(symbols, v.String())
		}
	}

	g.Log().Debugf(ctx, "Found %d active withdrawal symbols: %v", len(symbols), symbols)
	return symbols, nil
}
