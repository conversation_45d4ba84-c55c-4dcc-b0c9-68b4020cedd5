package token

import (
	"context"
	"strings"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/shopspring/decimal"
)

// FormatUserBalance 格式化用户余额并移除多余小数点和零 12.3000 → 12.3，但保留整数部分的0
func (s *sToken) FormatUserBalance(ctx context.Context, balance decimal.Decimal, symbol string) decimal.Decimal {
	balanceStr := balance.String()

	if strings.Contains(balanceStr, ".") {
		parts := strings.Split(balanceStr, ".")
		integerPart := parts[0]
		decimalPart := parts[1]
		decimalPart = strings.TrimRight(decimalPart, "0")
		if decimalPart == "" {
			balanceStr = integerPart
		} else {
			balanceStr = integerPart + "." + decimalPart
		}
	}

	g.Log().Debugf(ctx, "格式化余额: %s => %s %s ", balance.String(), balanceStr, symbol)
	formattedDecimal, err := decimal.NewFromString(balanceStr)
	if err != nil {
		g.Log().Errorf(ctx, "转换格式化余额失败: %v", err)
		return balance
	}
	return formattedDecimal
}
