package token

import (
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"

	"task-api/internal/dao"
	"task-api/internal/model/entity"
)

// GetTokenByID retrieves token details by its primary key ID.
func (s *sToken) GetTokenByID(ctx context.Context, id uint) (*entity.Tokens, error) {
	var token *entity.Tokens
	err := dao.Tokens.Ctx(ctx).
		Where(dao.Tokens.Columns().TokenId, id).
		Scan(&token)

	if err != nil {
		g.Log().Errorf(ctx, "Failed to query token by ID %d: %v", id, err)
		return nil, gerror.Wrapf(err, "查询代币信息失败 (ID: %d)", id)
	}

	if token == nil {
		g.Log().Warningf(ctx, "No token found for ID %d", id)
		return nil, gerror.Newf("未找到代币 (ID: %d)", id)
	}

	g.Log().Debugf(ctx, "Found token for ID %d: Symbol %s, Decimals %d", id, token.Symbol, token.Decimals)
	return token, nil
}
