package token

import (
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"

	"task-api/internal/dao"
	"task-api/internal/model/entity"
)

// IsDepositAllowed checks if depositing a specific token on a given network is allowed.
func (s *sToken) IsDepositAllowed(ctx context.Context, symbol string, network string) (bool, error) {
	var token *entity.Tokens
	err := dao.Tokens.Ctx(ctx).
		Where(dao.Tokens.Columns().Symbol, symbol).
		Where(dao.Tokens.Columns().Network, network).
		Scan(&token)

	if err != nil {
		g.Log().Errorf(ctx, "Failed to query token %s on network %s: %v", symbol, network, err)
		return false, gerror.Wrapf(err, "查询代币信息失败 (token: %s, network: %s)", symbol, network)
	}

	if token == nil {
		g.Log().Warningf(ctx, "Token %s on network %s not found in database.", symbol, network)
		return false, gerror.Newf("不支持的代币或网络 (token: %s, network: %s)", symbol, network)
	}

	// Check the conditions: must be active, status must be 1 (上架), and allow_deposit must be true (1)
	if token.IsActive == 1 && token.Status == 1 && token.AllowDeposit == 1 {
		g.Log().Debugf(ctx, "Deposit check for %s on %s: Allowed (IsActive: %d, Status: %d, AllowDeposit: %d)", symbol, network, token.IsActive, token.Status, token.AllowDeposit)
		return true, nil
	}

	g.Log().Warningf(ctx, "Deposit check for %s on %s: Denied (IsActive: %d, Status: %d, AllowDeposit: %d)", symbol, network, token.IsActive, token.Status, token.AllowDeposit)
	return false, gerror.Newf("该币种/网络暂停充值 (token: %s, network: %s)", symbol, network)
}
