package token

import (
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"

	"task-api/internal/dao"
	"task-api/internal/model/entity"
)

// GetActiveReceiveTokens returns a list of tokens that are active and allowed for receiving payments.
func (s *sToken) GetActiveReceiveTokens(ctx context.Context) ([]*entity.Tokens, error) {
	var tokens []*entity.Tokens
	err := dao.Tokens.Ctx(ctx).
		Fields(dao.Tokens.Columns().Symbol, "MAX(`order`) as `order`").
		Where(dao.Tokens.Columns().Status, 1).
		Where(dao.Tokens.Columns().IsActive, 1).
		Where(dao.Tokens.Columns().AllowReceive, 1).
		Group(dao.Tokens.Columns().Symbol).
		OrderDesc("order").
		Scan(&tokens)

	if err != nil {
		g.Log().Errorf(ctx, "Failed to get active receive tokens: %v", err)
		return nil, gerror.Wrap(err, "查询可用收款代币失败")
	}

	g.Log().Debugf(ctx, "Found %d active receive tokens", len(tokens))
	return tokens, nil
}
