package token

import (
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"

	"task-api/internal/dao"
	"task-api/internal/model/entity"
)

// GetTokenDetailsForWithdrawal returns token details for a specific symbol and chain.
func (s *sToken) GetTokenDetailsForWithdrawal(ctx context.Context, symbol string, chain string) (*entity.Tokens, error) {
	var token *entity.Tokens
	err := dao.Tokens.Ctx(ctx).
		Where(dao.Tokens.Columns().Symbol, symbol).
		Where(dao.Tokens.Columns().Network, chain).
		Where(dao.Tokens.Columns().Status, 1).
		Where(dao.Tokens.Columns().IsActive, 1).
		Scan(&token)

	if err != nil {
		g.Log().Errorf(ctx, "Failed to query token details for withdrawal (symbol: %s, chain: %s): %v", symbol, chain, err)
		return nil, gerror.Wrapf(err, "查询代币详情失败 (symbol: %s, chain: %s)", symbol, chain)
	}

	if token == nil {
		g.Log().Warningf(ctx, "Token %s on chain %s not found or not active for withdrawal", symbol, chain)
		return nil, gerror.Newf("不支持的代币或网络 (symbol: %s, chain: %s)", symbol, chain)
	}

	if token.AllowWithdraw != 1 {
		g.Log().Warningf(ctx, "Token %s on chain %s is not allowed for withdrawal (AllowWithdraw: %d)", symbol, chain, token.AllowWithdraw)
		return nil, gerror.Newf("该币种/网络暂停提现 (symbol: %s, chain: %s)", symbol, chain)
	}

	g.Log().Debugf(ctx, "Found token details for withdrawal (symbol: %s, chain: %s)", symbol, chain)
	return token, nil
}
