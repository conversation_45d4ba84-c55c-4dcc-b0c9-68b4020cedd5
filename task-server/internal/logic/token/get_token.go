package token

import (
	"context"

	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"

	"task-api/internal/dao"
	"task-api/internal/model/entity"
)

// GetToken retrieves a specific token by symbol and network.
func (s *sToken) GetToken(ctx context.Context, symbol, network string) (*entity.Tokens, error) {
	var token *entity.Tokens
	err := dao.Tokens.Ctx(ctx).
		Where(dao.Tokens.Columns().Symbol, symbol).
		Where(dao.Tokens.Columns().Network, network).
		Scan(&token)

	if err != nil {
		g.Log().<PERSON><PERSON><PERSON>(ctx, "Failed to query token by symbol '%s' and network '%s': %v", symbol, network, err)
		return nil, gerror.Wrapf(err, "查询代币信息时出错 (symbol: %s, network: %s)", symbol, network)
	}

	if token == nil {
		g.Log().Warningf(ctx, "Token not found for symbol '%s' and network '%s'", symbol, network)
		return nil, gerror.NewCodef(gcode.CodeNotFound, "未找到指定的代币/网络组合 (symbol: %s, network: %s)", symbol, network)
	}

	g.Log().Debugf(ctx, "Successfully retrieved token for symbol '%s' and network '%s': ID %d", symbol, network, token.TokenId)
	return token, nil
}
