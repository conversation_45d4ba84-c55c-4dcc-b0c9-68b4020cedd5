package token

import (
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"

	"task-api/internal/dao"
	"task-api/internal/model/entity"
)

// GetActiveRedPacketTokens returns a list of tokens that are active and allowed for red packet.
func (s *sToken) GetActiveRedPacketTokens(ctx context.Context) ([]*entity.Tokens, error) {
	var tokens []*entity.Tokens
	err := dao.Tokens.Ctx(ctx).
		Fields(dao.Tokens.Columns().Symbol, "MAX(`order`) as `order`").
		Where(dao.Tokens.Columns().Status, 1).
		Where(dao.Tokens.Columns().IsActive, 1).
		Where(dao.Tokens.Columns().AllowRedPacket, 1).
		Group(dao.Tokens.Columns().Symbol).
		OrderDesc("order").
		Scan(&tokens)
	if err != nil {
		g.Log().<PERSON>rrorf(ctx, "Failed to get active red packet tokens: %v", err)
		return nil, gerror.Wrap(err, "查询可用红包代币失败")
	}

	g.Log().Debugf(ctx, "Found %d active red packet tokens", len(tokens))
	return tokens, nil
}
