package token

import (
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/shopspring/decimal"
)

// ConvertRawToBalance 将原始值转换回用户余额（除以10^decimals）
func (s *sToken) ConvertRawToBalance(ctx context.Context, rawBalance decimal.Decimal, symbol string) (decimal.Decimal, error) {
	tokenInfo, err := s.GetTokenBySymbol(ctx, symbol) // Call method on the same struct
	if err != nil {
		g.Log().Debug(ctx, "获取代币信息失败 (Symbol: %s): %v", symbol, err)
		return decimal.Zero, gerror.Wrapf(err, "获取代币信息失败 (Symbol: %s)", symbol)
	}
	if tokenInfo == nil {
		g.Log().Debug(ctx, "未找到代币信息 (Symbol: %s)", symbol)
		return decimal.Zero, gerror.Newf("未找到代币信息 (Symbol: %s)", symbol)
	}

	divisor := decimal.NewFromInt(10).Pow(decimal.NewFromInt(int64(tokenInfo.Decimals)))
	balance := rawBalance.Div(divisor)

	g.Log().Infof(ctx, "原始值转换: %s 原始值 => %s %s (除以10^%d)",
		rawBalance.String(), balance.String(), symbol, tokenInfo.Decimals)

	return balance, nil
}
