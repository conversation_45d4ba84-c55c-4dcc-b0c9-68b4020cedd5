package token

import (
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"

	"task-api/internal/dao"
	"task-api/internal/model/entity"
)

// GetActiveTransferTokens returns a list of tokens that are active and allowed for transfer.
func (s *sToken) GetActiveTransferTokens(ctx context.Context) ([]*entity.Tokens, error) {
	var tokens []*entity.Tokens
	err := dao.Tokens.Ctx(ctx).
		Fields(dao.Tokens.Columns().Symbol, "MAX(`order`) as `order`").
		Where(dao.Tokens.Columns().Status, 1).
		Where(dao.Tokens.Columns().IsActive, 1).
		Where(dao.Tokens.Columns().AllowTransfer, 1).
		Group(dao.Tokens.Columns().Symbol).
		OrderDesc("order").
		Scan(&tokens)

	if err != nil {
		g.Log().Errorf(ctx, "Failed to get active transfer tokens: %v", err)
		return nil, gerror.Wrap(err, "查询可用转账代币失败")
	}

	return tokens, nil
}
