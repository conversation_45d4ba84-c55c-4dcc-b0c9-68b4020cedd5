package redis

import (
	"github.com/gogf/gf/v2/database/gredis"
	"github.com/gogf/gf/v2/frame/g"
)

// Client 获取指定名称的 Redis 客户端实例，如果未指定名称则获取默认实例。
// Note: This method is moved here from the original internal/logic/redis.go
func (s *sRedis) Client(name ...string) *gredis.Redis {
	redisName := ""
	if len(name) > 0 {
		redisName = name[0]
	}

	s.cacheMutex.RLock()
	client, exists := s.clientCache[redisName]
	s.cacheMutex.RUnlock()

	if exists && client != nil {
		return client
	}

	client = g.Redis(name...) // Use g.Redis() to get the client instance

	s.cacheMutex.Lock()
	s.clientCache[redisName] = client
	s.cacheMutex.Unlock()

	return client
}
