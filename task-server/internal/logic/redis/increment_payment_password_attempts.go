package redis

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
)

// IncrementPaymentPasswordAttempts 增加用户支付密码尝试次数。
// Note: This method is moved here from the original internal/logic/redis.go
func (s *sRedis) IncrementPaymentPasswordAttempts(ctx context.Context, userID uint64) (int64, error) {
	key := getPaymentAttemptsKey(userID)
	g.Log().Debugf(ctx, "Incrementing payment password attempts for user %d, key=%s", userID, key)

	count, err := s.Incr(ctx, key) // Use the Incr method of this service
	if err != nil {
		g.Log().Errorf(ctx, "Failed to increment payment password attempts for user %d: %v", userID, err)
		return 0, err
	}

	if count == 1 {
		// Set expiration only on the first increment (24 hours)
		_, expireErr := s.Client().Expire(ctx, key, 24*60*60)
		if expireErr != nil {
			g.Log().Warningf(ctx, "Failed to set expiration for payment attempts key %s: %v", key, expireErr)
			// Continue even if setting expiration fails
		}
	}
	return count, nil
}
