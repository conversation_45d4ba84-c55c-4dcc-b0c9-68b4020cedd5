package boot

import (
	"context"

	"github.com/gogf/gf/v2/os/glog"
	"github.com/yalks/wallet"
)

var ctx = context.Background()

func init() {
	// 在这里添加初始化代码

	// // 初始化系统配置（使用 XPay Config）
	// err := config.Initialize(ctx)
	// if err != nil {
	// 	glog.Errorf(ctx, "初始化系统配置失败: %v", err)
	// 	panic(err)
	// } else {
	// 	glog.Info(ctx, "系统配置初始化完成")
	// }

	// 初始化钱包管理器
	if err := wallet.Initialize(ctx); err != nil {
		glog.Errorf(ctx, "初始化钱包管理器失败: %v", err)
		panic(err)
	}
}
