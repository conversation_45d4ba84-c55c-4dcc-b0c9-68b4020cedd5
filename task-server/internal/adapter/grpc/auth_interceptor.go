package grpc

import (
	"context"

	taskv1 "task-api/api" // Import the api package

	"github.com/gogf/gf/v2/frame/g"
	"google.golang.org/grpc"

	// "google.golang.org/grpc/codes" // No longer needed for direct status.Errorf
	"google.golang.org/grpc/metadata"
	// "google.golang.org/grpc/status" // No longer needed for direct status.Errorf
)

const (
	apiKeyHeader = "x-api-key" // Standard header for API Key
)

// ApiKeyAuthInterceptor is a gRPC unary server interceptor for API Key authentication.
// It checks for the presence and validity of an API key provided in the request metadata.
func ApiKeyAuthInterceptor(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
	logger := g.Log("auth.interceptor") // Get a logger instance

	// Load valid API keys from configuration
	// Use Get and check for error and nil/empty if you want to allow running without keys configured
	validApiKeysConfig, err := g.Cfg().Get(ctx, "grpcServer.apiKeys")
	if err != nil {
		logger.Warningf(ctx, "Failed to read grpcServer.apiKeys from config: %v. Skipping authentication.", err)
		return handler(ctx, req) // Or return an internal error depending on policy
	}
	if validApiKeysConfig == nil || validApiKeysConfig.IsNil() || len(validApiKeysConfig.Strings()) == 0 {
		logger.Debug(ctx, "No API keys configured or configuration is empty/nil, skipping authentication.")
		return handler(ctx, req)
	}
	validApiKeys := validApiKeysConfig.Strings()

	// Get metadata from incoming context
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		logger.Warning(ctx, "Missing metadata in request context.")
		// Return ApiResponse instead of gRPC status error
		return &taskv1.ApiResponse{
			Code:        CodeUnauthorized,
			Message:     "Missing metadata", // Keep message concise
			DataPayload: nil,                // Use DataPayload instead of Data
		}, nil
	}

	// Get API Key from metadata header
	apiKeys := md.Get(apiKeyHeader)
	if len(apiKeys) == 0 {
		logger.Warningf(ctx, "Missing '%s' header in request metadata.", apiKeyHeader)
		// Return ApiResponse instead of gRPC status error
		return &taskv1.ApiResponse{
			Code:        CodeUnauthorized,
			Message:     "API key is required",
			DataPayload: nil, // Use DataPayload instead of Data
		}, nil

	}
	clientApiKey := apiKeys[0] // Use the first key if multiple are sent

	// Validate the API Key
	isValid := false
	for _, validKey := range validApiKeys {
		// Important: Use constant-time comparison in production to prevent timing attacks
		// For simplicity here, we use direct comparison. Consider crypto/subtle.ConstantTimeCompare
		if clientApiKey == validKey {
			isValid = true
			break
		}
	}

	if !isValid {
		logger.Warningf(ctx, "Invalid API key received.") // Avoid logging the invalid key itself
		// Return ApiResponse instead of gRPC status error
		return &taskv1.ApiResponse{
			Code:        CodeUnauthorized,
			Message:     "Invalid API key",
			DataPayload: nil, // Use DataPayload instead of Data
		}, nil
	}

	// API Key is valid, proceed with the handler
	logger.Debugf(ctx, "API key validated successfully for method: %s", info.FullMethod)
	return handler(ctx, req)
}
