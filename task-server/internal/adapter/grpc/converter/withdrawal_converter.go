package converter

import (
	taskv1 "task-api/api" // 导入 protobuf 定义
	"task-api/internal/model/entity"

	"github.com/gogf/gf/v2/os/gtime"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// EntityWithdrawalToProto 将数据库实体转换为 Protobuf 消息
func EntityWithdrawalToProto(w *entity.UserWithdraws) *taskv1.Withdrawal {
	if w == nil {
		return nil
	}

	// Helper function to convert *gtime.Time to *timestamppb.Timestamp
	toTimestamp := func(t *gtime.Time) *timestamppb.Timestamp {
		if t == nil || t.<PERSON>() {
			return nil
		}
		return timestamppb.New(t.Time)
	}

	return &taskv1.Withdrawal{
		UserWithdrawsId:  int64(w.UserWithdrawsId), // uint -> int64
		UserId:           int64(w.UserId),          // uint64 -> int64
		TokenId:          int64(w.<PERSON>),         // uint -> int64
		WalletId:         w.<PERSON>,
		Name:             w.Name,
		Chan:             w.<PERSON>,
		OrderNo:          w.<PERSON>o,
		Address:          w.Address,
		RecipientName:    w.RecipientName,
		RecipientAccount: w.RecipientAccount,
		Amount:           w.Amount,
		HandlingFee:      w.HandlingFee,
		ActualAmount:     w.ActualAmount,

		AutoWithdrawalProgress: int32(w.AutoWithdrawalProgress), // uint -> int32
		AuditStatus:            int32(w.AuditStatus),            // uint -> int32
		ProcessingStatus:       int32(w.ProcessingStatus),       // uint -> int32

		RefuseReasonZh: w.RefuseReasonZh,
		RefuseReasonEn: w.RefuseReasonEn,
		TxHash:         w.TxHash,
		ErrorMessage:   w.ErrorMessage,
		UserRemark:     w.UserRemark,
		AdminRemark:    w.AdminRemark,
		CreatedAt:      toTimestamp(w.CreatedAt),
		CheckedAt:      toTimestamp(w.CheckedAt),
		ProcessingAt:   toTimestamp(w.ProcessingAt),
		CompletedAt:    toTimestamp(w.CompletedAt),
		UpdatedAt:      toTimestamp(w.UpdatedAt),
		Retries:        int32(w.Retries),    // int -> int32
		NergyState:     int32(w.NergyState), // int -> int32
	}
}

// EntityWithdrawalsToProto 将实体列表转换为 Protobuf 消息列表
func EntityWithdrawalsToProto(ws []*entity.UserWithdraws) []*taskv1.Withdrawal {
	if ws == nil {
		return nil
	}
	protos := make([]*taskv1.Withdrawal, 0, len(ws))
	for _, w := range ws {
		if w != nil { // Add nil check for individual items
			protos = append(protos, EntityWithdrawalToProto(w))
		}
	}
	return protos
}

// ProtoStatusToEntity 和 EntityStatusToProto 函数已移除，因为状态现在是 int32
