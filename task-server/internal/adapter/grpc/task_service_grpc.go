package grpc

import (
	"context"
	"math" // For pagination calculation

	taskv1 "task-api/api"
	"task-api/internal/adapter/grpc/converter" // Import converter
	"task-api/internal/dao"

	// _ "task-api/internal/logic/task_impl"      // Import for side effect (init registration)
	"task-api/internal/service"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gcode" // For checking specific error codes
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g" // For logging

	// "google.golang.org/grpc/codes" // No longer needed
	// "google.golang.org/grpc/status" // No longer needed
	// "google.golang.org/protobuf/proto" // No longer needed
	// "google.golang.org/protobuf/types/known/anypb" // No longer needed
	"google.golang.org/protobuf/types/known/emptypb" // Import for Empty type
)

// Business status codes
const (
	CodeSuccess         int32 = 200   // Success
	CodeBadRequest      int32 = 40000 // Bad Request / Invalid Parameter
	CodeUnauthorized    int32 = 40100 // Unauthorized
	CodeForbidden       int32 = 40300 // Forbidden / Permission Denied
	CodeNotFound        int32 = 40400 // Not Found
	CodeOperationFailed int32 = 40900 // Operation Failed / Conflict (e.g., state check fail)
	CodeInternalError   int32 = 50000 // Internal Server Error
)

// taskGrpcService 实现了 TaskServiceServer 接口
type taskGrpcService struct {
	taskv1.UnimplementedTaskServiceServer // 嵌入未实现的服务，确保向前兼容
}

// NewTaskGrpcService 创建一个新的 gRPC Task 服务实例
func NewTaskGrpcService() taskv1.TaskServiceServer {
	return &taskGrpcService{}
}

// Helper function to map gerror codes to business status codes
func mapGErrorToBusinessCode(err error) (int32, string) {
	if err == nil {
		return CodeSuccess, "Success"
	}
	errCode := gerror.Code(err)
	message := err.Error()            // Default message
	businessCode := CodeInternalError // Default code

	switch errCode {
	case gcode.CodeNil, gcode.CodeValidationFailed, gcode.CodeInvalidParameter, gcode.CodeInvalidRequest:
		businessCode = CodeBadRequest
	case gcode.CodeNotAuthorized:
		businessCode = CodeForbidden // Or CodeUnauthorized depending on context
	case gcode.CodeNotFound:
		businessCode = CodeNotFound
	case gcode.CodeOperationFailed:
		businessCode = CodeOperationFailed
	// Add more mappings as needed
	default:
		// Log internal errors for debugging
		g.Log("adapter.grpc").Errorf(context.Background(), "Unhandled internal error, mapping to CodeInternalError: %+v", err)
		message = "Internal server error" // Don't expose detailed internal errors
	}
	return businessCode, message
}

// ListWithdrawals 实现获取提币记录列表的 gRPC 方法
func (s *taskGrpcService) ListWithdrawals(ctx context.Context, req *taskv1.ListWithdrawalsRequest) (*taskv1.ApiResponse, error) {
	logger := g.Log("adapter.grpc") // Get logger instance
	logger.Infof(ctx, "Received ListWithdrawals request: %+v", req)

	var err error
	var response *taskv1.ApiResponse

	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 1. Parameter Validation & Defaults
		pageSize := int(req.GetPageSize())
		page := int(req.GetPage())
		if pageSize <= 0 {
			pageSize = 10
			logger.Debugf(ctx, "PageSize not positive, defaulting to %d", pageSize)
		}
		if page <= 0 {
			page = 1
			logger.Debugf(ctx, "Page not positive, defaulting to %d", page)
		}

		// Basic validation example (can be expanded)
		if pageSize > 1000 { // Example limit
			err := gerror.NewCode(gcode.CodeInvalidParameter, "page_size cannot exceed 1000")
			logger.Warningf(ctx, "Invalid page_size: %d", pageSize)
			return err
		}

		// 2. Prepare Filter Map for Logic Layer
		filter := make(map[string]interface{})
		// 使用新的过滤字段
		if req.FilterAuditStatus != 0 {
			filter["audit_status"] = req.FilterAuditStatus
		}
		if req.FilterProcessingStatus != 0 {
			filter["processing_status"] = req.FilterProcessingStatus
		}
		if req.FilterUserId > 0 {
			filter["user_id"] = uint64(req.FilterUserId)
		}
		if req.FilterOrderNo != "" {
			filter["order_no"] = req.FilterOrderNo
		}
		logger.Debugf(ctx, "Prepared filter for logic layer: %+v", filter)

		// 3. Call Logic Layer
		list, total, err := service.Task().ListUserWithdraws(ctx, page, pageSize, filter)

		// 4. Error Handling (Business Errors)
		if err != nil {
			logger.Errorf(ctx, "Failed to list user withdrawals from logic layer: %v", err)
			return err
		}
		logger.Debugf(ctx, "Logic layer returned %d withdrawals, total count: %d", len(list), total)

		if len(list) > 0 {
			idsToUpdate := make([]uint, 0, len(list))
			for _, item := range list {
				if item != nil {

					//自动放币的订单以及处理中(待冷钱包转入)的订单
					if item.ProcessingStatus == 1 || item.ProcessingStatus == 2 {
						// 如果是自动放币的订单，且当前状态为 0 (待处理)，则更新状态
						if item.AutoWithdrawalProgress == 0 {
							idsToUpdate = append(idsToUpdate, item.UserWithdrawsId)
						}
					}
				}
			}

			if len(idsToUpdate) > 0 {
				const auto_withdrawal_progress = 1 // Define target state (Processing)
				// Use the transaction model for update

				updateModel := tx.Model(dao.UserWithdraws.Table())
				var txErr error
				_, txErr = updateModel.WhereIn(dao.UserWithdraws.Columns().UserWithdrawsId, idsToUpdate).
					Data(g.Map{dao.UserWithdraws.Columns().AutoWithdrawalProgress: auto_withdrawal_progress}).
					Update()

				if txErr != nil {
					// Log the error for debugging
					g.Log("logic.task").Errorf(ctx, "Failed to update withdrawal statuses within transaction: %v", txErr)
					// Return the error to trigger rollback
					return gerror.Wrapf(txErr, `logic: failed to update status to %d for withdrawals: %+v`, auto_withdrawal_progress, idsToUpdate)
				} else {
					// 数据库更新成功，记录日志
					g.Log("logic.task").Infof(ctx, "Successfully updated database status to %d for %d withdrawals within transaction: %v", auto_withdrawal_progress, len(idsToUpdate), idsToUpdate)
				}
			}
		}

		// 5. Data Conversion
		protoWithdrawals := converter.EntityWithdrawalsToProto(list)

		// 6. Calculate Pagination
		totalPages := int32(0)
		if pageSize > 0 && total > 0 {
			totalPages = int32(math.Ceil(float64(total) / float64(pageSize)))
		}

		// 7. Build Original Response Data
		responseData := &taskv1.ListWithdrawalsResponse{
			Withdrawals: protoWithdrawals,
			TotalCount:  int32(total),
			CurrentPage: int32(page),
			TotalPages:  totalPages,
		}

		// Store response data for return after transaction
		response = &taskv1.ApiResponse{
			Code:    CodeSuccess,
			Message: "Success",
			DataPayload: &taskv1.ApiResponse_ListWithdrawalsData{
				ListWithdrawalsData: responseData,
			},
		}

		logger.Infof(ctx, "Successfully processed ListWithdrawals request. Returning %d items.", len(protoWithdrawals))
		return nil
	})

	// 9. Handle transaction errors
	if err != nil {
		logger.Errorf(ctx, "Failed to process ListWithdrawals request: %v", err)
		businessCode, msg := mapGErrorToBusinessCode(err)
		return &taskv1.ApiResponse{
			Code:        businessCode,
			Message:     msg,
			DataPayload: nil,
		}, nil
	}

	return response, nil
}

// UpdateWithdrawalStatus 实现更新提币记录状态的 gRPC 方法
// Note: This method still uses the ApiResponse pattern. Consider refactoring for consistency.
func (s *taskGrpcService) UpdateWithdrawalStatus(ctx context.Context, req *taskv1.UpdateWithdrawalStatusRequest) (*taskv1.ApiResponse, error) {
	logger := g.Log("adapter.grpc")
	logger.Infof(ctx, "Received UpdateWithdrawalStatus request: %+v", req)

	// 1. Parameter Validation
	if req.GetWithdrawalId() <= 0 {
		err := gerror.NewCode(gcode.CodeInvalidParameter, "withdrawal_id must be positive")
		logger.Warningf(ctx, "Invalid WithdrawalId: %d", req.GetWithdrawalId())
		businessCode, msg := mapGErrorToBusinessCode(err)
		return &taskv1.ApiResponse{Code: businessCode, Message: msg, DataPayload: nil}, nil
	}
	// 验证至少有一个状态字段被设置
	if req.GetAuditStatus() <= 0 && req.GetAutoWithdrawalProgress() < 0 && req.GetProcessingStatus() <= 0 {
		err := gerror.NewCode(gcode.CodeInvalidParameter, "at least one status field must be provided")
		logger.Warning(ctx, "No valid status fields provided in request")
		businessCode, msg := mapGErrorToBusinessCode(err)
		return &taskv1.ApiResponse{Code: businessCode, Message: msg, DataPayload: nil}, nil
	}

	// 2. Prepare Update Data Map for Logic Layer
	updateData := make(map[string]interface{})

	// 添加状态字段
	if req.GetAuditStatus() > 0 {
		updateData["audit_status"] = req.GetAuditStatus()
	}
	if req.GetAutoWithdrawalProgress() >= 0 {
		updateData["auto_withdrawal_progress"] = req.GetAutoWithdrawalProgress()
	}
	if req.GetProcessingStatus() > 0 {
		updateData["processing_status"] = req.GetProcessingStatus()
	}

	// 添加其他字段
	if req.ErrorMessage != "" {
		updateData["error_message"] = req.ErrorMessage
	}
	if req.RefuseReasonZh != "" {
		updateData["refuse_reason_zh"] = req.RefuseReasonZh
	}
	if req.RefuseReasonEn != "" {
		updateData["refuse_reason_en"] = req.RefuseReasonEn
	}
	if req.TxHash != "" {
		updateData["tx_hash"] = req.TxHash
	}
	updateData["retries"] = req.Retries
	updateData["admin_remark"] = req.AdminRemark
	logger.Debugf(ctx, "Prepared update data for logic layer: %+v", updateData)

	// 4. Call Logic Layer to Update Status
	// 使用 0 作为 status 参数，因为现在状态字段都在 updateData 中
	err := service.Task().UpdateUserWithdrawStatus(ctx, uint(req.GetWithdrawalId()), 0, updateData)

	// 5. Error Handling (Business Errors)
	if err != nil {
		logger.Debug(ctx, "Failed to update withdrawal status from logic layer for ID %d: %v", req.GetWithdrawalId(), err)
		businessCode, msg := mapGErrorToBusinessCode(err)
		return &taskv1.ApiResponse{Code: businessCode, Message: msg, DataPayload: nil}, nil
	}

	// 6. Build Success Response (currently without data as GetUserWithdrawByID is not implemented)
	// TODO: Implement GetUserWithdrawByID in logic layer and service interface
	// TODO: Call GetUserWithdrawByID here
	// TODO: Convert the result using converter.EntityWithdrawalToProto
	// TODO: Pass the protoWithdrawal to newSuccessResponse

	// 6. Build Success Response using oneof with Empty
	logger.Infof(ctx, "Successfully processed UpdateWithdrawalStatus request for ID %d.", req.GetWithdrawalId())
	return &taskv1.ApiResponse{
		Code:    CodeSuccess,
		Message: "Status updated successfully", // Or just "Success"
		DataPayload: &taskv1.ApiResponse_SuccessNoData{ // Use the oneof wrapper for Empty
			SuccessNoData: &emptypb.Empty{},
		},
	}, nil
}
