// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// MerchantCallbacks is the golang structure for table merchant_callbacks.
type MerchantCallbacks struct {
	Id            uint64      `json:"id"            orm:"id"              description:""`       //
	MerchantId    uint64      `json:"merchantId"    orm:"merchant_id"     description:"商户ID"`   // 商户ID
	CallbackType  string      `json:"callbackType"  orm:"callback_type"   description:"回调事件类型"` // 回调事件类型
	RelatedId     uint64      `json:"relatedId"     orm:"related_id"      description:"关联记录ID"` // 关联记录ID
	CallbackUrl   string      `json:"callbackUrl"   orm:"callback_url"    description:"回调URL"`  // 回调URL
	Payload       string      `json:"payload"       orm:"payload"         description:"回调数据"`   // 回调数据
	Status        string      `json:"status"        orm:"status"          description:"回调状态"`   // 回调状态
	RetryCount    int         `json:"retryCount"    orm:"retry_count"     description:"重试次数"`   // 重试次数
	ResponseCode  int         `json:"responseCode"  orm:"response_code"   description:"响应状态码"`  // 响应状态码
	ResponseBody  string      `json:"responseBody"  orm:"response_body"   description:"响应内容"`   // 响应内容
	LastAttemptAt *gtime.Time `json:"lastAttemptAt" orm:"last_attempt_at" description:"最后尝试时间"` // 最后尝试时间
	CreatedAt     *gtime.Time `json:"createdAt"     orm:"created_at"      description:""`       //
	UpdatedAt     *gtime.Time `json:"updatedAt"     orm:"updated_at"      description:""`       //
}
