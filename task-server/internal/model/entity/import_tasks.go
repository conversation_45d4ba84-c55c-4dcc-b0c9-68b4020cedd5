// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// ImportTasks is the golang structure for table import_tasks.
type ImportTasks struct {
	TaskId        string      `json:"taskId"        orm:"task_id"        description:""` //
	Status        string      `json:"status"        orm:"status"         description:""` //
	Progress      float64     `json:"progress"      orm:"progress"       description:""` //
	ProcessedRows int         `json:"processedRows" orm:"processed_rows" description:""` //
	TotalRows     int         `json:"totalRows"     orm:"total_rows"     description:""` //
	ErrorMessage  string      `json:"errorMessage"  orm:"error_message"  description:""` //
	CreatedAt     *gtime.Time `json:"createdAt"     orm:"created_at"     description:""` //
	UpdatedAt     *gtime.Time `json:"updatedAt"     orm:"updated_at"     description:""` //
}
