// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// WithdrawalRiskControlSettings is the golang structure for table withdrawal_risk_control_settings.
type WithdrawalRiskControlSettings struct {
	Id                uint64      `json:"id"                orm:"id"                  description:""`                                                //
	ControlType       string      `json:"controlType"       orm:"control_type"        description:"风控类型: times_limit(提现次数风控), amount_limit(提现金额风控)"` // 风控类型: times_limit(提现次数风控), amount_limit(提现金额风控)
	TimePeriod        int         `json:"timePeriod"        orm:"time_period"         description:"时间周期数值"`                                          // 时间周期数值
	TimeUnit          string      `json:"timeUnit"          orm:"time_unit"           description:"时间单位: hour(小时), day(天), week(周), month(月)"`       // 时间单位: hour(小时), day(天), week(周), month(月)
	MaxTimes          uint        `json:"maxTimes"          orm:"max_times"           description:"最大提现次数（仅times_limit类型使用）"`                        // 最大提现次数（仅times_limit类型使用）
	MaxAmount         float64     `json:"maxAmount"         orm:"max_amount"          description:"最大提现金额（仅amount_limit类型使用）"`                       // 最大提现金额（仅amount_limit类型使用）
	MaxAmountMultiple int         `json:"maxAmountMultiple" orm:"max_amount_multiple" description:"最大提现金额倍数（仅amount_limit类型使用）  提现金额/充值金额 <=此倍数"`    // 最大提现金额倍数（仅amount_limit类型使用）  提现金额/充值金额 <=此倍数
	Status            int         `json:"status"            orm:"status"              description:"状态: 1-启用, 0-禁用"`                                  // 状态: 1-启用, 0-禁用
	CreatedAt         *gtime.Time `json:"createdAt"         orm:"created_at"          description:"创建时间"`                                            // 创建时间
	UpdatedAt         *gtime.Time `json:"updatedAt"         orm:"updated_at"          description:"更新时间"`                                            // 更新时间
	DeletedAt         *gtime.Time `json:"deletedAt"         orm:"deleted_at"          description:"软删除时间"`                                           // 软删除时间
}
