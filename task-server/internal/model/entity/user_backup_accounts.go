// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// UserBackupAccounts is the golang structure for table user_backup_accounts.
type UserBackupAccounts struct {
	BackupAccountId  uint64      `json:"backupAccountId"  orm:"backup_account_id" description:"备用账户主键ID"` // 备用账户主键ID
	TelegramId       int64       `json:"telegramId"       orm:"telegram_id"       description:""`         //
	ChatId           int64       `json:"chatId"           orm:"chat_id"           description:""`         //
	TelegramUsername string      `json:"telegramUsername" orm:"telegram_username" description:""`         //
	UserId           uint64      `json:"userId"           orm:"user_id"           description:"关联的主用户ID"` // 关联的主用户ID
	VerifiedAt       *gtime.Time `json:"verifiedAt"       orm:"verified_at"       description:"验证时间"`     // 验证时间
	CreatedAt        *gtime.Time `json:"createdAt"        orm:"created_at"        description:"创建时间"`     // 创建时间
	UpdatedAt        *gtime.Time `json:"updatedAt"        orm:"updated_at"        description:"最后更新时间"`   // 最后更新时间
	DeletedAt        *gtime.Time `json:"deletedAt"        orm:"deleted_at"        description:"软删除的时间戳"`  // 软删除的时间戳
	IsMaster         int         `json:"isMaster"         orm:"is_master"         description:""`         //
	FirstName        string      `json:"firstName"        orm:"first_name"        description:""`         //
}
