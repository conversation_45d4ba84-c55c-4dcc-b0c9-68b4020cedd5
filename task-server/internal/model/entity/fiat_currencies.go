// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// FiatCurrencies is the golang structure for table fiat_currencies.
type FiatCurrencies struct {
	FiatId        uint        `json:"fiatId"        orm:"fiat_id"        description:"法币内部 ID (主键)"`                                                 // 法币内部 ID (主键)
	Code          string      `json:"code"          orm:"code"           description:"法币代码 (ISO 4217 标准, 例如: USD, CNY, EUR)"`                        // 法币代码 (ISO 4217 标准, 例如: USD, CNY, EUR)
	Name          string      `json:"name"          orm:"name"           description:"法币名称 (例如: US Dollar, Chinese Yuan, Euro)"`                     // 法币名称 (例如: US Dollar, Chinese Yuan, Euro)
	Symbol        string      `json:"symbol"        orm:"symbol"         description:"法币符号 (例如: $, ¥, €)"`                                           // 法币符号 (例如: $, ¥, €)
	Decimals      uint        `json:"decimals"      orm:"decimals"       description:"法币精度 (小数位数, 大多数为 2)"`                                          // 法币精度 (小数位数, 大多数为 2)
	CountryCode   string      `json:"countryCode"   orm:"country_code"   description:"主要关联国家/地区代码 (ISO 3166-1 alpha-2, 例如: US, CN, EU - EU 非标准但常用)"` // 主要关联国家/地区代码 (ISO 3166-1 alpha-2, 例如: US, CN, EU - EU 非标准但常用)
	Status        int         `json:"status"        orm:"status"         description:"法币状态: active-可用, inactive-禁用"`                                 // 法币状态: active-可用, inactive-禁用
	AllowDeposit  int         `json:"allowDeposit"  orm:"allow_deposit"  description:"是否允许通过此法币充值 (例如银行转账)"`                                         // 是否允许通过此法币充值 (例如银行转账)
	AllowWithdraw int         `json:"allowWithdraw" orm:"allow_withdraw" description:"是否允许提现到此法币 (例如银行转账)"`                                          // 是否允许提现到此法币 (例如银行转账)
	DisplayOrder  int         `json:"displayOrder"  orm:"display_order"  description:"显示排序 (数字越小越靠前)"`                                               // 显示排序 (数字越小越靠前)
	CreatedAt     *gtime.Time `json:"createdAt"     orm:"created_at"     description:"创建时间"`                                                         // 创建时间
	UpdatedAt     *gtime.Time `json:"updatedAt"     orm:"updated_at"     description:"最后更新时间"`                                                       // 最后更新时间
	DeletedAt     *gtime.Time `json:"deletedAt"     orm:"deleted_at"     description:"软删除时间"`                                                        // 软删除时间
}
