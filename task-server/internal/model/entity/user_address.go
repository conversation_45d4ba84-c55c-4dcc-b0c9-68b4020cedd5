// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// UserAddress is the golang structure for table user_address.
type UserAddress struct {
	UserAddressId uint        `json:"userAddressId" orm:"user_address_id" description:""`     //
	TokenId       uint        `json:"tokenId"       orm:"token_id"        description:"币种ID"` // 币种ID
	UserId        int64       `json:"userId"        orm:"user_id"         description:"用户id"` // 用户id
	Lable         string      `json:"lable"         orm:"lable"           description:"备注"`   // 备注
	Name          string      `json:"name"          orm:"name"            description:"币种"`   // 币种
	Chan          string      `json:"chan"          orm:"chan"            description:"链"`    // 链
	Address       string      `json:"address"       orm:"address"         description:"地址"`   // 地址
	Image         string      `json:"image"         orm:"image"           description:"二维码"`  // 二维码
	CreatedAt     *gtime.Time `json:"createdAt"     orm:"created_at"      description:""`     //
	UpdatedAt     *gtime.Time `json:"updatedAt"     orm:"updated_at"      description:""`     //
	Type          string      `json:"type"          orm:"type"            description:""`     //
}
