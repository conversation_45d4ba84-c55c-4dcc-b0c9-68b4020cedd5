// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// Address is the golang structure for table address.
type Address struct {
	Id         uint        `json:"id"         orm:"id"          description:""`                    //
	Chan       string      `json:"chan"       orm:"chan"        description:"链"`                   // 链
	Address    string      `json:"address"    orm:"address"     description:"地址"`                  // 地址
	Image      string      `json:"image"      orm:"image"       description:"二维码"`                 // 二维码
	CreatedAt  *gtime.Time `json:"createdAt"  orm:"created_at"  description:""`                    //
	UpdatedAt  *gtime.Time `json:"updatedAt"  orm:"updated_at"  description:""`                    //
	BindStatus uint        `json:"bindStatus" orm:"bind_status" description:"绑定状态 0: 未绑定, 1: 已绑定"` // 绑定状态 0: 未绑定, 1: 已绑定
}
