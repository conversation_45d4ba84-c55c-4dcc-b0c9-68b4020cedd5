// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// PersonalAccessTokens is the golang structure for table personal_access_tokens.
type PersonalAccessTokens struct {
	Id            uint64      `json:"id"            orm:"id"             description:""`        //
	TokenableType string      `json:"tokenableType" orm:"tokenable_type" description:""`        //
	UserId        uint64      `json:"userId"        orm:"user_id"        description:""`        //
	Token         string      `json:"token"         orm:"token"          description:""`        //
	LastUsedAt    *gtime.Time `json:"lastUsedAt"    orm:"last_used_at"   description:""`        //
	ExpiresAt     *gtime.Time `json:"expiresAt"     orm:"expires_at"     description:""`        //
	CreatedAt     *gtime.Time `json:"createdAt"     orm:"created_at"     description:""`        //
	UpdatedAt     *gtime.Time `json:"updatedAt"     orm:"updated_at"     description:""`        //
	DeletedAt     *gtime.Time `json:"deletedAt"     orm:"deleted_at"     description:"软删除的时间戳"` // 软删除的时间戳
}
