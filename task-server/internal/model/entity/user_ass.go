// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// UserAss is the golang structure for table user_ass.
type UserAss struct {
	Id         uint64      `json:"id"         orm:"id"          description:"备用账户主键ID"` // 备用账户主键ID
	AUserId    int64       `json:"aUserId"    orm:"a_user_id"   description:"主账户"`      // 主账户
	BUserId    int64       `json:"bUserId"    orm:"b_user_id"   description:"备账户"`      // 备账户
	VerifiedAt *gtime.Time `json:"verifiedAt" orm:"verified_at" description:"验证时间"`     // 验证时间
	CreatedAt  *gtime.Time `json:"createdAt"  orm:"created_at"  description:"创建时间"`     // 创建时间
	UpdatedAt  *gtime.Time `json:"updatedAt"  orm:"updated_at"  description:"最后更新时间"`   // 最后更新时间
	DeletedAt  *gtime.Time `json:"deletedAt"  orm:"deleted_at"  description:"软删除的时间戳"`  // 软删除的时间戳
}
