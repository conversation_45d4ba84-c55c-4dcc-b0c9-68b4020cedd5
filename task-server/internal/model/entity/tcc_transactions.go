// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// TccTransactions is the golang structure for table tcc_transactions.
type TccTransactions struct {
	Id         int64       `json:"id"         orm:"id"          description:""`                            //
	Gid        string      `json:"gid"        orm:"gid"         description:"全局事务ID"`                      // 全局事务ID
	BranchId   string      `json:"branchId"   orm:"branch_id"   description:"分支ID"`                        // 分支ID
	BusinessId string      `json:"businessId" orm:"business_id" description:"业务ID(幂等性)"`                   // 业务ID(幂等性)
	Operation  string      `json:"operation"  orm:"operation"   description:"操作类型: credit/debit/transfer"` // 操作类型: credit/debit/transfer
	Status     int         `json:"status"     orm:"status"      description:"0:待处理, 1:尝试中, 2:已确认, 3:已取消"`  // 0:待处理, 1:尝试中, 2:已确认, 3:已取消
	TryData    string      `json:"tryData"    orm:"try_data"    description:"Try阶段序列化数据"`                  // Try阶段序列化数据
	CreatedAt  *gtime.Time `json:"createdAt"  orm:"created_at"  description:""`                            //
	UpdatedAt  *gtime.Time `json:"updatedAt"  orm:"updated_at"  description:""`                            //
}
