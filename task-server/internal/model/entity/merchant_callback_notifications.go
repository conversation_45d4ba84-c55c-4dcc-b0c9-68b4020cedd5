// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// MerchantCallbackNotifications is the golang structure for table merchant_callback_notifications.
type MerchantCallbackNotifications struct {
	Id             int64       `json:"id"             orm:"id"               description:"主键ID"`                           // 主键ID
	MerchantId     int64       `json:"merchantId"     orm:"merchant_id"      description:"商户ID"`                           // 商户ID
	EventType      string      `json:"eventType"      orm:"event_type"       description:"事件类型"`                           // 事件类型
	EventId        int64       `json:"eventId"        orm:"event_id"         description:"事件ID（deposit_id 或 withdraw_id）"` // 事件ID（deposit_id 或 withdraw_id）
	OrderNo        string      `json:"orderNo"        orm:"order_no"         description:"订单号"`                            // 订单号
	CallbackUrl    string      `json:"callbackUrl"    orm:"callback_url"     description:"回调URL"`                          // 回调URL
	RequestBody    string      `json:"requestBody"    orm:"request_body"     description:"请求体JSON"`                        // 请求体JSON
	RequestHeaders string      `json:"requestHeaders" orm:"request_headers"  description:"请求头信息"`                          // 请求头信息
	ResponseStatus int         `json:"responseStatus" orm:"response_status"  description:"HTTP响应状态码"`                      // HTTP响应状态码
	ResponseBody   string      `json:"responseBody"   orm:"response_body"    description:"响应体内容"`                          // 响应体内容
	ResponseTimeMs int         `json:"responseTimeMs" orm:"response_time_ms" description:"响应时间（毫秒）"`                       // 响应时间（毫秒）
	RetryCount     int         `json:"retryCount"     orm:"retry_count"      description:"重试次数"`                           // 重试次数
	MaxRetries     int         `json:"maxRetries"     orm:"max_retries"      description:"最大重试次数"`                         // 最大重试次数
	NextRetryAt    *gtime.Time `json:"nextRetryAt"    orm:"next_retry_at"    description:"下次重试时间"`                         // 下次重试时间
	Status         string      `json:"status"         orm:"status"           description:"回调状态"`                           // 回调状态
	ErrorMessage   string      `json:"errorMessage"   orm:"error_message"    description:"错误信息"`                           // 错误信息
	CreatedAt      *gtime.Time `json:"createdAt"      orm:"created_at"       description:"创建时间"`                           // 创建时间
	UpdatedAt      *gtime.Time `json:"updatedAt"      orm:"updated_at"       description:"更新时间"`                           // 更新时间
}
