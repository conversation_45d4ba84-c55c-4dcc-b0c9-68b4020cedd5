// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// RedPacketImages is the golang structure for table red_packet_images.
type RedPacketImages struct {
	RedPacketImagesId     int64       `json:"redPacketImagesId"     orm:"red_packet_images_id"    description:"红包 ID (主键)"`                                                       // 红包 ID (主键)
	Status                string      `json:"status"                orm:"status"                  description:"图片状态 pending_review, fail, success"`                               // 图片状态 pending_review, fail, success
	ProcessingStatus      string      `json:"processingStatus"      orm:"processing_status"       description:"处理状态: pending-待处理, processing-处理中, completed-已完成,    failed-处理失败"` // 处理状态: pending-待处理, processing-处理中, completed-已完成,    failed-处理失败
	ProcessingAttempts    uint        `json:"processingAttempts"    orm:"processing_attempts"     description:"处理尝试次数"`                                                           // 处理尝试次数
	ProcessingStartedAt   *gtime.Time `json:"processingStartedAt"   orm:"processing_started_at"   description:"开始处理时间"`                                                           // 开始处理时间
	ProcessingCompletedAt *gtime.Time `json:"processingCompletedAt" orm:"processing_completed_at" description:"处理完成时间"`                                                           // 处理完成时间
	ProcessingError       string      `json:"processingError"       orm:"processing_error"        description:"处理错误信息"`                                                           // 处理错误信息
	OriginalFileSize      uint64      `json:"originalFileSize"      orm:"original_file_size"      description:"原始文件大小(字节)"`                                                       // 原始文件大小(字节)
	ProcessedFileSize     uint64      `json:"processedFileSize"     orm:"processed_file_size"     description:"处理后文件大小(字节)"`                                                      // 处理后文件大小(字节)
	ImageWidth            uint        `json:"imageWidth"            orm:"image_width"             description:"图片宽度"`                                                             // 图片宽度
	ImageHeight           uint        `json:"imageHeight"           orm:"image_height"            description:"图片高度"`                                                             // 图片高度
	ImageFormat           string      `json:"imageFormat"           orm:"image_format"            description:"图片格式(jpeg, png等)"`                                                 // 图片格式(jpeg, png等)
	CreatedAt             *gtime.Time `json:"createdAt"             orm:"created_at"              description:"创建时间"`                                                             // 创建时间
	DeletedAt             *gtime.Time `json:"deletedAt"             orm:"deleted_at"              description:"软删除的时间戳"`                                                          // 软删除的时间戳
	UpdatedAt             *gtime.Time `json:"updatedAt"             orm:"updated_at"              description:"最后更新时间"`                                                           // 最后更新时间
	RefuseReasonZh        string      `json:"refuseReasonZh"        orm:"refuse_reason_zh"        description:"拒绝原因 (中文)"`                                                        // 拒绝原因 (中文)
	RefuseReasonEn        string      `json:"refuseReasonEn"        orm:"refuse_reason_en"        description:"拒绝原因 (英文)"`                                                        // 拒绝原因 (英文)
	UserId                uint64      `json:"userId"                orm:"user_id"                 description:"用户ID (Foreign key to users table recommended)"`                    // 用户ID (Foreign key to users table recommended)
	ImagesUrl             string      `json:"imagesUrl"             orm:"images_url"              description:""`                                                                 //
	FileId                string      `json:"fileId"                orm:"file_id"                 description:""`                                                                 //
	NotificationSent      uint        `json:"notificationSent"      orm:"notification_sent"       description:"是否已发送通知: 0-未发送, 1-已发送"`                                            // 是否已发送通知: 0-未发送, 1-已发送
	NotificationSentAt    *gtime.Time `json:"notificationSentAt"    orm:"notification_sent_at"    description:"通知发送时间"`                                                           // 通知发送时间
}
