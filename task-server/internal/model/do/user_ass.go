// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// UserAss is the golang structure of table user_ass for DAO operations like Where/Data.
type UserAss struct {
	g.Meta     `orm:"table:user_ass, do:true"`
	Id         interface{} // 备用账户主键ID
	AUserId    interface{} // 主账户
	BUserId    interface{} // 备账户
	VerifiedAt *gtime.Time // 验证时间
	CreatedAt  *gtime.Time // 创建时间
	UpdatedAt  *gtime.Time // 最后更新时间
	DeletedAt  *gtime.Time // 软删除的时间戳
}
