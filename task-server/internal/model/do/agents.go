// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// Agents is the golang structure of table agents for DAO operations like Where/Data.
type Agents struct {
	g.Meta                    `orm:"table:agents, do:true"`
	AgentId                   interface{} // 代理唯一ID
	Username                  interface{} // 代理登录用户名
	PasswordHash              interface{} // 加密后的登录密码
	AgentName                 interface{} // 代理真实姓名或昵称
	Email                     interface{} // 电子邮箱
	Level                     interface{} // 代理级别 (1: 一级代理, 2: 二级代理, 3: 三级代理)
	ParentAgentId             interface{} // 上级代理ID (一级代理此字段为NULL)
	Status                    interface{} // 账户状态 (0-禁用, 1-启用)
	InvitationCode            interface{} // 专属邀请码 (用于下级注册)
	GoogleAuthenticatorSecret interface{} // Google Authenticator 的秘钥 (用于2FA)
	Relationship              interface{} // 代理商关系 (例如层级路径或其他关系标识)
	CreatedAt                 *gtime.Time // 创建时间
	UpdatedAt                 *gtime.Time // 最后更新时间
	DeletedAt                 *gtime.Time // 软删除时间
}
