// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// Tokens is the golang structure of table tokens for DAO operations like Where/Data.
type Tokens struct {
	g.Meta                  `orm:"table:tokens, do:true"`
	TokenId                 interface{} // 代币内部 ID (主键)
	Symbol                  interface{} // 代币符号 (例如: USDT, BTC, ETH)
	Name                    interface{} // 代币名称 (例如: Tether, Bitcoin, Ethereum)
	Network                 interface{} // 所属网络/链 (例如: Ethereum, Tron, Bitcoin, BSC, Solana)
	ChainId                 interface{} // 链 ID (主要用于 EVM 兼容链)
	Confirmations           interface{} // 确认次数
	ContractAddress         interface{} // 代币合约地址 (对于非原生代币). 对于原生代币为 NULL.
	TokenStandard           interface{} // 代币标准 (例如: native, ERC20, TRC20, BEP20, SPL)
	Decimals                interface{} // 代币精度/小数位数
	IsStablecoin            interface{} // 是否为稳定币
	IsFiat                  interface{} // 是否为法币
	IsActive                interface{} // 代币是否在系统内激活可用 (总开关)
	AllowDeposit            interface{} // 是否允许充值 (设为 FALSE 即表示充值维护中)
	AllowWithdraw           interface{} // 是否允许提现 (设为 FALSE 即表示提现维护中)
	AllowTransfer           interface{} // 是否允许内部转账
	AllowReceive            interface{} // 是否允许内部收款 (通常与 allow_transfer 联动或独立控制)
	AllowRedPacket          interface{} // 是否允许发红包
	AllowTrading            interface{} // 是否允许在交易对中使用
	MaintenanceMessage      interface{} // 维护信息 (当充值/提现/其他功能禁用时显示)
	WithdrawalFeeType       interface{} // 提币手续费类型: fixed-固定金额, percent-百分比
	WithdrawalFeeAmount     interface{} // 提币手续费金额/比例 (根据 fee_type 决定)
	MinDepositAmount        interface{} // 单笔最小充值金额
	MaxDepositAmount        interface{} // 单笔最大充值金额 (NULL 表示无限制)
	MinWithdrawalAmount     interface{} // 单笔最小提币金额
	MaxWithdrawalAmount     interface{} // 单笔最大提币金额 (NULL 表示无限制)
	MinTransferAmount       interface{} // 单笔最小转账金额
	MaxTransferAmount       interface{} // 单笔最大转账金额 (NULL 表示无限制)
	MinReceiveAmount        interface{} // 单笔最小收款金额
	MaxReceiveAmount        interface{} // 单笔最大收款金额 (NULL 表示无限制)
	MinRedPacketAmount      interface{} // 单个红包最小金额
	MaxRedPacketAmount      interface{} // 单个红包最大金额 (NULL 表示无限制)
	MaxRedPacketCount       interface{} // 单次发放红包最大个数 (NULL 表示无限制)
	MaxRedPacketTotalAmount interface{} // 单次发放红包最大总金额 (NULL 表示无限制)
	LogoUrl                 interface{} // 代币 Logo 图片 URL
	ProjectWebsite          interface{} // 项目官方网站 URL
	Description             interface{} // 代币或项目描述
	Order                   interface{} // 排序字段 (用于前端展示时的排序)
	Status                  interface{} // 状态 0-下架 1-上架
	CreatedAt               *gtime.Time // 创建时间
	UpdatedAt               *gtime.Time // 最后更新时间
	DeletedAt               *gtime.Time // 软删除时间
}
