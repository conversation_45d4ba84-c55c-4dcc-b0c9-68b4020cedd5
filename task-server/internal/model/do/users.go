// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// Users is the golang structure of table users for DAO operations like Where/Data.
type Users struct {
	g.Meta                         `orm:"table:users, do:true"`
	Id                             interface{} //
	Email                          interface{} //
	EmailVerifiedAt                *gtime.Time //
	Password                       interface{} // 用户登录密码（经过哈希处理）
	RememberToken                  interface{} //
	Account                        interface{} // 唯一用户账户标识符（例如，用于登录）
	AccountType                    interface{} // 账户类型 1 用户 2商户 3 代理
	InviteCode                     interface{} // 用户唯一邀请码（用于分享给其他人）
	AreaCode                       interface{} // 电话区号
	Phone                          interface{} // 用户电话号码
	Avatar                         interface{} // 用户头像URL或路径
	PaymentPassword                interface{} // 支付密码（经过哈希处理，未设置时为NULL）
	RecommendId                    interface{} // 推荐该用户的用户ID（关联users.id）
	Deep                           interface{} // 推荐结构中的层级深度
	RecommendRelationship          interface{} // 表示客户推荐层级关系的路径（例如，/1/5/10/）
	AgentRelationship              interface{} // 表示代理推荐层级关系的路径
	FirstId                        interface{} // 关联的代理一级ID
	SecondId                       interface{} // 关联的代理二级ID
	ThirdId                        interface{} // 关联的代理三级ID
	IsStop                         interface{} // 用户账户暂停状态：0=活跃，1=已暂停
	CurrentToken                   interface{} // 最后使用的认证令牌（例如，API令牌）
	LastLoginTime                  *gtime.Time // 最后一次成功登录的时间戳
	Reason                         interface{} // 暂停或其他状态变更的原因
	CreatedAt                      *gtime.Time //
	UpdatedAt                      *gtime.Time //
	DeletedAt                      *gtime.Time // 软删除的时间戳
	Google2FaSecret                interface{} // 谷歌2fa密钥
	Google2FaEnabled               interface{} // 谷歌2fa是否启用
	IsPaymentPassword              interface{} // 是否开启免密支付
	PaymentPasswordAmount          interface{} // 免密支付额度
	BackupAccounts                 interface{} // 备用账户
	Language                       interface{} // 语言
	RedPacketPermission            interface{} // 红包权限
	TransferPermission             interface{} // 转账权限
	WithdrawPermission             interface{} // 提现权限
	FlashTradePermission           interface{} // 闪兑权限
	RechargePermission             interface{} // 充值权限
	ReceivePermission              interface{} // 收款权限
	ResetPaymentPasswordPermission interface{} // 重置支付密码权限：0=无权限，1=有权限
	MainWalletId                   interface{} // 钱包id
}
