// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// FiatCurrencies is the golang structure of table fiat_currencies for DAO operations like Where/Data.
type FiatCurrencies struct {
	g.Meta        `orm:"table:fiat_currencies, do:true"`
	FiatId        interface{} // 法币内部 ID (主键)
	Code          interface{} // 法币代码 (ISO 4217 标准, 例如: USD, CNY, EUR)
	Name          interface{} // 法币名称 (例如: US Dollar, Chinese Yuan, Euro)
	Symbol        interface{} // 法币符号 (例如: $, ¥, €)
	Decimals      interface{} // 法币精度 (小数位数, 大多数为 2)
	CountryCode   interface{} // 主要关联国家/地区代码 (ISO 3166-1 alpha-2, 例如: US, CN, EU - EU 非标准但常用)
	Status        interface{} // 法币状态: active-可用, inactive-禁用
	AllowDeposit  interface{} // 是否允许通过此法币充值 (例如银行转账)
	AllowWithdraw interface{} // 是否允许提现到此法币 (例如银行转账)
	DisplayOrder  interface{} // 显示排序 (数字越小越靠前)
	CreatedAt     *gtime.Time // 创建时间
	UpdatedAt     *gtime.Time // 最后更新时间
	DeletedAt     *gtime.Time // 软删除时间
}
