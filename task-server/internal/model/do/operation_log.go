// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// OperationLog is the golang structure of table operation_log for DAO operations like Where/Data.
type OperationLog struct {
	g.Meta        `orm:"table:operation_log, do:true"`
	Id            interface{} // 日志ID
	ReqId         interface{} // 请求ID
	MemberType    interface{} // 用户类型
	MemberId      interface{} // 用户ID
	Username      interface{} // 用户名
	Module        interface{} // 操作模块
	Action        interface{} // 操作名称
	RequestMethod interface{} // 请求方法
	RequestUrl    interface{} // 请求URL
	RequestParams interface{} // 请求参数
	Response      interface{} // 响应数据
	Duration      interface{} // 操作时长(ms)
	OperationIp   interface{} // 操作IP
	ProvinceId    interface{} // 省编码
	CityId        interface{} // 市编码
	UserAgent     interface{} // UA信息
	ErrMsg        interface{} // 错误提示
	Status        interface{} // 状态 (1-成功 0-失败)
	CreatedAt     *gtime.Time // 创建时间 (操作时间)
	UpdatedAt     *gtime.Time // 修改时间
	DeletedAt     *gtime.Time // 软删除的时间戳
}
