// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// MerchantAddress is the golang structure of table merchant_address for DAO operations like Where/Data.
type MerchantAddress struct {
	g.Meta     `orm:"table:merchant_address, do:true"`
	AddressId  interface{} //
	TokenId    interface{} // 币种ID
	MerchantId interface{} // 用户id
	Lable      interface{} // 备注
	Name       interface{} // 币种
	Chan       interface{} // 链
	Address    interface{} // 地址
	Image      interface{} // 二维码
	QrUrl      interface{} // S3二维码URL
	CreatedAt  *gtime.Time //
	UpdatedAt  *gtime.Time //
	Type       interface{} //
}
