// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// AdminPermissions is the golang structure of table admin_permissions for DAO operations like Where/Data.
type AdminPermissions struct {
	g.Meta    `orm:"table:admin_permissions, do:true"`
	Id        interface{} //
	Name      interface{} // 权限名称 (给人看，例如：保存订单按钮)
	Pid       interface{} // 父权限ID
	Key       interface{} // 权限标识符 (给 Casbin 用，例如：page:order:btn_save)
	Type      interface{} // 权限类型 (例如: menu, api, button)
	ParentKey interface{} // 父权限标识符 (用于分组展示)
	Remark    interface{} // 备注
	Status    interface{} // 角色状态
	CreatedAt *gtime.Time // 创建时间
	UpdatedAt *gtime.Time // 更新时间
	DeletedAt *gtime.Time // 软删除的时间戳
	Level     interface{} // 关系树等级
	Tree      interface{} // 关系树
	Sort      interface{} // 排序
}
