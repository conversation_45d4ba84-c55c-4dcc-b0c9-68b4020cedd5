// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// UserBackupAccounts is the golang structure of table user_backup_accounts for DAO operations like Where/Data.
type UserBackupAccounts struct {
	g.Meta           `orm:"table:user_backup_accounts, do:true"`
	BackupAccountId  interface{} // 备用账户主键ID
	TelegramId       interface{} //
	ChatId           interface{} //
	TelegramUsername interface{} //
	UserId           interface{} // 关联的主用户ID
	VerifiedAt       *gtime.Time // 验证时间
	CreatedAt        *gtime.Time // 创建时间
	UpdatedAt        *gtime.Time // 最后更新时间
	DeletedAt        *gtime.Time // 软删除的时间戳
	IsMaster         interface{} //
	FirstName        interface{} //
}
