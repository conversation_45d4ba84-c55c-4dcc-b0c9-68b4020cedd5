// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// WithdrawalRiskControlSettings is the golang structure of table withdrawal_risk_control_settings for DAO operations like Where/Data.
type WithdrawalRiskControlSettings struct {
	g.Meta            `orm:"table:withdrawal_risk_control_settings, do:true"`
	Id                interface{} //
	ControlType       interface{} // 风控类型: times_limit(提现次数风控), amount_limit(提现金额风控)
	TimePeriod        interface{} // 时间周期数值
	TimeUnit          interface{} // 时间单位: hour(小时), day(天), week(周), month(月)
	MaxTimes          interface{} // 最大提现次数（仅times_limit类型使用）
	MaxAmount         interface{} // 最大提现金额（仅amount_limit类型使用）
	MaxAmountMultiple interface{} // 最大提现金额倍数（仅amount_limit类型使用）  提现金额/充值金额 <=此倍数
	Status            interface{} // 状态: 1-启用, 0-禁用
	CreatedAt         *gtime.Time // 创建时间
	UpdatedAt         *gtime.Time // 更新时间
	DeletedAt         *gtime.Time // 软删除时间
}
