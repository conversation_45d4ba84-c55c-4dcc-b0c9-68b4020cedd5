// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// WithdrawalApprovalSettings is the golang structure of table withdrawal_approval_settings for DAO operations like Where/Data.
type WithdrawalApprovalSettings struct {
	g.Meta            `orm:"table:withdrawal_approval_settings, do:true"`
	Id                interface{} //
	Currency          interface{} // 币种符号 (如 USDT, BTC)
	Network           interface{} // 网络类型 (如 TRC20, ERC20)
	AutoReleaseMin    interface{} // 无需审核自动放币最小金额
	AutoReleaseMax    interface{} // 无需审核自动放币最大金额
	ApprovalAutoMin   interface{} // 审核确定后自动放币最小金额
	ApprovalAutoMax   interface{} // 审核确定后自动放币最大金额
	ApprovalManualMin interface{} // 审核确定后手动放币最小金额
	ApprovalManualMax interface{} // 审核确定后手动放币最大金额
	Status            interface{} // 状态: 1-启用, 0-禁用
	CreatedAt         *gtime.Time // 创建时间
	UpdatedAt         *gtime.Time // 更新时间
	DeletedAt         *gtime.Time // 软删除时间
}
