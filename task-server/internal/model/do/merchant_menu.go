// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// MerchantMenu is the golang structure of table merchant_menu for DAO operations like Where/Data.
type MerchantMenu struct {
	g.Meta             `orm:"table:merchant_menu, do:true"`
	Id                 interface{} // 菜单ID
	Pid                interface{} // 父菜单ID
	Level              interface{} // 关系树等级
	Tree               interface{} // 关系树
	Name               interface{} // 名称编码
	Path               interface{} // 路由地址
	Icon               interface{} // 菜单图标
	HideInMenu         interface{} // 是否隐藏
	HideChildrenInMenu interface{} // 是否隐藏子菜单
	Sort               interface{} // 排序
	Remark             interface{} // 备注
	Status             interface{} // 菜单状态
	UpdatedAt          *gtime.Time // 更新时间
	CreatedAt          *gtime.Time // 创建时间
	DeletedAt          *gtime.Time // 软删除的时间戳
	Target             interface{} //
	Access             interface{} //
	Key                interface{} //
}
