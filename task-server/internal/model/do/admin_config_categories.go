// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// AdminConfigCategories is the golang structure of table admin_config_categories for DAO operations like Where/Data.
type AdminConfigCategories struct {
	g.Meta      `orm:"table:admin_config_categories, do:true"`
	Id          interface{} // 分类唯一标识符 (例如 UUID)
	Name        interface{} // 分类显示名称
	CategoryKey interface{} // 分类键 (唯一, 创建后不可修改)
	SortOrder   interface{} // 排序顺序
	CreatedAt   *gtime.Time // 创建时间
	UpdatedAt   *gtime.Time // 更新时间
}
