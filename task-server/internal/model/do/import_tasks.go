// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// ImportTasks is the golang structure of table import_tasks for DAO operations like Where/Data.
type ImportTasks struct {
	g.Meta        `orm:"table:import_tasks, do:true"`
	TaskId        interface{} //
	Status        interface{} //
	Progress      interface{} //
	ProcessedRows interface{} //
	TotalRows     interface{} //
	ErrorMessage  interface{} //
	CreatedAt     *gtime.Time //
	UpdatedAt     *gtime.Time //
}
