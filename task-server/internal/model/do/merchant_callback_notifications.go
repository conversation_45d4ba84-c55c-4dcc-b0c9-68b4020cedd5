// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// MerchantCallbackNotifications is the golang structure of table merchant_callback_notifications for DAO operations like Where/Data.
type MerchantCallbackNotifications struct {
	g.Meta         `orm:"table:merchant_callback_notifications, do:true"`
	Id             interface{} // 主键ID
	MerchantId     interface{} // 商户ID
	EventType      interface{} // 事件类型
	EventId        interface{} // 事件ID（deposit_id 或 withdraw_id）
	OrderNo        interface{} // 订单号
	CallbackUrl    interface{} // 回调URL
	RequestBody    interface{} // 请求体JSON
	RequestHeaders interface{} // 请求头信息
	ResponseStatus interface{} // HTTP响应状态码
	ResponseBody   interface{} // 响应体内容
	ResponseTimeMs interface{} // 响应时间（毫秒）
	RetryCount     interface{} // 重试次数
	MaxRetries     interface{} // 最大重试次数
	NextRetryAt    *gtime.Time // 下次重试时间
	Status         interface{} // 回调状态
	ErrorMessage   interface{} // 错误信息
	CreatedAt      *gtime.Time // 创建时间
	UpdatedAt      *gtime.Time // 更新时间
}
